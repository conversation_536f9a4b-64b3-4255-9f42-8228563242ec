module.exports = {
  env: {
    browser: false,
    node: true,
    commonjs: true,
    es2021: true,
  },
  extends: ['airbnb-base', 'plugin:import/recommended'],
  parserOptions: {
    ecmaVersion: 12,
  },
  rules: {
    'no-unused-vars': 'off',
    'no-useless-catch': 'off',
    'no-underscore-dangle': 'off', // mongo _id
    'arrow-body-style': 'off',
    'linebreak-style': 'off',
    'max-len': 'off',
    'import/no-extraneous-dependencies': 'off',
    'no-use-before-define': 'off',
    'no-tabs': 'off',
    indent: 'off',
  },
  settings: {
    'import/resolver': {
      alias: {
        map: [
          ['#config/*', './src/config'],
          ['#controllers/*', './src/controllers'],
          ['#dtos/*', './src/dtos'],
          ['#helpers/*', './src/helpers'],
          ['#loaders/*', './src/loaders'],
          ['#middlewares/*', './src/middlewares'],
          ['#models/*', './src/models'],
          ['#services/*', './src/services'],
          ['#utils/*', './src/utils'],
        ],
        extensions: ['.ts', '.js', '.jsx', '.json'],
      },
    },
  },
};
