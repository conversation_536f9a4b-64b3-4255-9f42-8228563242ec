module.exports = {
  env: {
    browser: false,
    node: true,
    commonjs: true,
    es2021: true,
  },
  extends: ["airbnb", "plugin:import/recommended"],
  plugins: ["prettier"],
  parserOptions: {
    ecmaVersion: 12,
  },
  rules: {
    "react/prop-types": 0,
    "react/jsx-filename-extension": 0,
    "import/no-unresolved": 0,
    "import/extensions": 0,
    "no-use-before-define": 0,
    "linebreak-style": 0,
    "max-len": 0,
    quotes: 0,
  },
  settings: {
    "import/resolver": {
      alias: {
        map: [
          ["#helpers/*", "./src/helpers"],
          ["#redux/*", "./src/redux"],
          ["#screens/*", "./src/screens"],
          ["#services/*", "./src/services"],
          ["#components/*", "./src/components"],
          ["#resources/*", "./src/resources"],
        ],
        extensions: [".js", ".jsx", ".json"],
      },
    },
  },
};
