const fetch = require('node-fetch');
const sharp = require('sharp');
const UserModel = require('#models/User.model');
const _ = require('lodash');
const { storage } = require('#config/firebase');
const { v4: uuidv4 } = require('uuid');

require('#config/mongodb');

const uploadImage = async (buffer, path = '') => {
  const upload = storage.file(`${path}/${uuidv4()}.jpg`, {
    metadata: { contentType: 'image/jpeg' },
    validation: 'md5',
  });

  const imageBuffer = buffer;
  await upload.save(imageBuffer);

  const photoUrl = await upload
    .getSignedUrl({
      action: 'read',
      expires: '03-09-9999',
    })
    .then((signedUrls) => signedUrls[0]);

  if (!photoUrl) {
    console.log('photo cannot be uploaded.');
  }

  return photoUrl;
};

const run = async () => {
  await fetch(
    'https://ffc7d44611e26a58346b1950c3df3c51875739a72d157690375ed70-apidata.googleusercontent.com/download/storage/v1/b/facial-recognition-3bf89.appspot.com/o/faces%2Fb266b22a-569c-4436-92c5-ee6da0ae56d4.jpg?jk=AXZI6Jy_rbysPr_mRQy0fI2u0fSLx3FS5MRBhNklL0gQgy0Kens0S_rmupNO_HmE-6VOro_HLQMWID6MRG7IpKoYbg1kHhw_z-yn36MvSpz1UlciDEFWmNf5j3ZwOCkbt8BkxnkPnzN2InUvkGNCes5iA54g7YKCyMqrlHBY9k9DLq6ANAM2e6kijs6R3miq9dl7ZmzSitGYgbKq6H0pId6-d1nPGDesdyXpcEuyZ4DTmdEabcF-Nz1GNONzDjE9E9kpNxKx3r74qN3ZLJs1CMDMT9dOUsddkw9w04832wA_StaW5_1gn6D8P6wzUpptHgmdWz2cBmFfzk---jM6YPn2Pvyf7sU71GBRVjx2d1tMg5zuC6DG8ffHDwEKFKfA5xobGU4ZD3yrYz1LhJy9_wZZla4V0CldaNhZATJ_2SDn-wG_HCtqVm9ATV67dVqvaWxC0KnzKm-Xt1YhKla4kXP4IMAdJ0Uf4Ux-Iov3K0azdV-bYMqXjXW8DWvPTH-WKXMyC8Hu8RkyNyXwt38hkfz1xWO1ASlJ1B_dHCeLcGAi-LCKwbFv3KHYPE8URg1zjcasZXG8ZGaELZabNMHWcF-Ov9FZgCx_rDgyt5rrZ1LAp9NEWyj02WalQ_KksUSwoBXOdZMPtW88dK3_HLDDk9lgP5jiGmo5jge6Dt-n-s2ca0vapRI7JEhdE9eW9vbMCKmfVV3ZK3LG_iu9VttwoB9X6sImCFuUgF1Nxm3NDn5FGDteNsvEmJPTRVF9Nd8bEFhaN4FdkVqeiCjATDv7FhSFgLgpVYNEiHSGhAaEaq8GvaTSflNCf5kfoZeiNUCZkBJ19T9CW1ER_45uOxm12jTebKkuVgiW9MOmhO_ZWwQ1osx2JU2Ln88uEBi3QZnu1lWUNn4wKXqvAxfFTRa4Stk6bxjvsMRMu2MLZC4L7ueo36bqeM9eSnEhcajqIqA0z9DmIlnCciER8evMRwzauaa56srrmB-9yHcdK2ZKcJfHtafAxSvbJxhPzDqX6fu59rNuD7HKyvAvUdF8t0ofyWd1BFrT8cIdircFzNncxoH9SO8vXX1pyBZPwOSxA30QgRGYAOP1HOKQdCkpa47eLerwAmFPd3WbKmfittqXT3yzPeZWmEwOS3m2ZkL39aSw2i_8TcYkUWEg5TzCd9YAlgnA3rbHwOgNISfpFblvnln1TUtynHpVR6igqc6jkO5rH9KXtE1gH4RnpRqqTCeGwY-oWTKJEXLNj3jRLh94vrZGYjn45e7FxqVmHj3s5Mh5IbMwOfjttQ&isca=1',
  ).then(async (res) => {
    const imageBuffer = await res.buffer();
    const image = await sharp(imageBuffer);
    await image.resize(400, 400).jpeg({ quality: 80 }).toFile('test.png');
  });
};

const changeImageResolution = async () => {
  try {
    const users = await UserModel.find().lean();

    await Promise.all(
      _.map(users, async (user) => {
        // get user image url
        console.log('user image url:', user?.copyOfHDImageUrl);
        await fetch(user.copyOfHDImageUrl).then(async (res) => {
          const imageBuffer = await res.buffer();
          const image = await sharp(imageBuffer);
          await image
            .resize(350, 350)
            .jpeg({ quality: 80 })
            .toBuffer()
            .then(async (data) => {
              const imageUrl = await uploadImage(data, 'face');
              await UserModel.findOneAndUpdate(
                {
                  _id: user._id,
                },
                {
                  faceImageUrl: imageUrl,
                },
              );
            });
        });
      }),
    );
  } catch (err) {
    console.log('error when changing image resolution:', err);
  }
};

const populateImage = async () => {
  const users = await UserModel.find().lean();

  console.log(_.filter(users, { copyOfHDImageUrl: '' }));
};

// run();
changeImageResolution();
// populateImage();
