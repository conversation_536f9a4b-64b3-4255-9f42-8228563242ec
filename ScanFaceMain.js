import { LinearGradient } from "expo-linear-gradient";
import React, { useEffect, useRef, useState } from "react";
import {
  Dimensions,
  SafeAreaView,
  StyleSheet,
  View,
  PixelRatio,
  UIManager,
  findNodeHandle,
  NativeEventEmitter,
  Button,
} from "react-native";
import {
  FacePassViewManager,
  FacePass,
  getFace,
  controlDoor,
  getDeviceSerialNumber,
  pauseListener,
  getFacePath,
  getGroupInfo,
  startCameraScreen,
  releaseFacePassHandler,
} from "facepass-react-native-module";

import _, { cond } from "lodash";
import moment from "moment";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useNetInfo } from "@react-native-community/netinfo";
import AccessStatus from "./components/AccessStatus";
import {
  accessDeniedAudio,
  accessGrantedAudio,
  checkInAudio,
  checkOutAudio,
} from "./audio";
import CameraHeader from "./components/CameraHeader";
import CameraFooterOverlay from "./components/CameraFooterOverlay";
// import CameraFooter from "./components/CameraFooter";
import ScanOptionOverlay from "./components/ScanOptionOverlay";
import {
  addAccessRecords, socket, syncAccessRecords, syncUser,
} from "./socket";
import { appendDataToTextFile, getTextFile, updateFile } from "./helpers/handlingTextFile";
import api from "./helpers/api";

const stype = null;

function ScanFaceMain({ route }) {
  const test = async () => {
    const serialNumber = await getDeviceSerialNumber();
    console.log('serial number from facepass:', serialNumber);
  };

  useEffect(() => {
    console.log('route params:', route?.params);
  }, [route?.params]);

  // update settings
  // React.useEffect(() => {
  //   console.log('update settings');
  //   if (socket) {
  //     console.log("socket status:", socket.connected);
  //     if (
  //       settings[0]?.settings !== null
  //       || settings[0]?.settings !== undefined
  //     ) {
  //       socket.emit("updateSettings", settings[0]);
  //     }
  //   }
  // }, [socket]);

  useEffect(() => {
    console.log('test:', test);
  }, []);
  const [overlayVisible, setoverlayVisible] = useState("none");

  return (
    <ScanFace type={route?.params?.type} />
  );
}

function ScanFace(props) {
  // const { type = "none", companyName = "PREMISE_NAME" } = route.params;
  const [scanType, setScanType] = useState(props.type);
  const [footerText, setfooterText] = useState("Scan Mode");
  const [dimensions, setdimensions] = useState({ width: 0, height: 0 });
  // const [isScanning, setisScanning] = useState("false");
  const [recognitionStatus, setRecognitionStatus] = useState(false);
  // const [isLightOn, setisLightOn] = useState(false);
  // const [lightColor, setlightColor] = useState("green");
  const [userFullName, setuserFullName] = useState("");
  const [companyInfo, setCompanyInfo] = useState({});
  const [companyLogo, setcompanyLogo] = useState(null);
  const [showOverlay, setShowOverlay] = useState(true);
  const [stayAfterScan, setStayAfterScan] = useState("stay");
  const [currentUserImage, setcurrentUserImage] = useState(
    "https://i.imgur.com/LOm6jSY.png",
  );
  const [networkInfo, setnetworkInfo] = useState({});

  const netinfo = useNetInfo();
  const scanTypeRef = useRef(null);
  const netinfoRef = useRef(null);

  useEffect(() => {
    console.log('scan type:', scanType);
    scanTypeRef.current = scanType;
  }, [scanType]);

  useEffect(() => {
    api("premise/api")
      .then(async (premiseData) => {
        await AsyncStorage.setItem("premiseId", JSON.stringify(premiseData.data.premiseId));
        await AsyncStorage.setItem("premiseData", JSON.stringify(premiseData.data));
        console.log('premise data from check api:', premiseData);
        await updateFile('premiseData', premiseData?.data);
      }).catch(async (err) => {
        console.log('gettting premise error:', err);
        throw err;
      });
  }, []);

  useEffect(() => {
    if (!_.isNil(socket) && netinfo.isConnected) {
      syncUser();
    }
  }, [socket, netinfo]);

  useEffect(() => {
    console.log('init netinfo:', netinfo);
    netinfoRef.current = netinfo;
    if (netinfo.isConnected && !_.isNil(socket)) {
      setTimeout(() => {
        syncAccessRecords();
      }, 3000);
    }
  }, [netinfo, socket]);

  const refAccessStatus = useRef(null);
  const refCameraHeader = useRef(null);
  const refCameraFooter = useRef(null);

  const getAllFaceTokens = async () => {
    // const faceTokenListArray = await getGroupInfo("facepass");
    // console.log('face token list array:', faceTokenListArray);
  };
  const pauseRecognition = async () => {
    await pauseListener(true);
    console.log(CONSOLE.FgBlue, "pausing recognition");
  };

  const resumeRecognition = async () => {
    console.log(CONSOLE.FgBlue, "resuming recognition");
    pauseListener(false);
  };

  useEffect(() => {
    if (showOverlay) {
      pauseRecognition();
    } else {
      resumeRecognition();
    }
  }, [showOverlay]);

  useEffect(() => {
    AsyncStorage.getItem("stayAfterScan").then((res) => {
      console.log(CONSOLE.FgRed, res);
      if (!_.isNil(res)) {
        setStayAfterScan(res);
        console.log(CONSOLE.FgRed, "stayAfterScan FOUND");
      } else {
        AsyncStorage.setItem("stayAfterScan", "stay");
        console.log(CONSOLE.FgRed, "SET  TO STORAGE stayAfterScan");
      }
    });
  }, []);

  // FUNCTIONS FOR AUDIO
  async function playSound(isIdentified) {
    if (isIdentified) {
      switch (scanType) {
        case "checkIn":
          await checkInAudio.sound.replayAsync();
          break;
        case "lunchIn":
          await checkInAudio.sound.replayAsync();
          break;
        case "checkOut":
          await checkOutAudio.sound.replayAsync();
          break;
        case "lunchOut":
          await checkOutAudio.sound.replayAsync();
          break;
        default:
          await accessGrantedAudio.sound.replayAsync();
          break;
      }
    } else {
      await accessDeniedAudio.sound.replayAsync();
    }
  }

  // CREATING VIEW FOR FACEPASS CAMERA
  const createFragment = (viewId) => {
    if (!viewId) {
      console.log('Warning: Attempted to create fragment with null viewId');
      return;
    }

    try {
      UIManager.dispatchViewManagerCommand(
        viewId,
        UIManager.FacePassViewManager.Commands.create.toString(),
        [viewId],
      );
    } catch (error) {
      console.log('Error creating fragment:', error);
    }
  };

  const destroyFragment = (viewId) => {
    if (!viewId) {
      console.log('Warning: Attempted to destroy fragment with null viewId');
      return;
    }

    try {
      UIManager.dispatchViewManagerCommand(
        viewId,
        UIManager.FacePassViewManager.Commands.remove.toString(),
        [viewId],
      );
    } catch (error) {
      console.log('Error destroying fragment:', error);
    }
  };

  const eventEmitter = new NativeEventEmitter(FacePass);
  const ref = useRef(null);
  const [reInitAudio, setReinitAudio] = useState(false);
  const [trackId, setTrackId] = useState(null);
  const accessStatusRef = useRef(null);

  const exitIfSameTrackId = (tId) => {
    if (tId === trackId) {
      console.log('same track id, exiting');
      return true;
    }
    return false;
  };

  const reInitSound = async () => {
    playSound(true).then(() => {
      setReinitAudio(!reInitAudio);
      setStayAfterScan(stayAfterScan);
    });
  };
  useEffect(() => {
    if (reInitAudio) {
      reInitSound();
    }
  }, [reInitAudio]);

  // Event listener to receive the data
  const startFaceDetectedListener = () => {
    try {
      return eventEmitter.addListener("FaceDetectedEvent", async (params) => {
        try {
          const facetoken = params.faceToken;
          console.log('face token:', facetoken);
          const users = await getTextFile('users');

          if (facetoken) {
            try {
              exitIfSameTrackId(params.trackID); // to prevent issue such as user scanned, first time not recognized but staying awhile more will be able to recognized. occurs often and might affect experience.
              controlDoor("open");
              // setisScanning(false);
              setRecognitionStatus(true);
              const user = _.find(users, { faceToken: facetoken });
              console.log('user scanned face:', user);
              setuserFullName(user?.fullName);

              // Get face data
              try {
                const faceData = await getFace(facetoken);
                setcurrentUserImage(`data:image/png;base64,${faceData}`);
              } catch (faceError) {
                console.log('Error getting face data:', faceError);
                setcurrentUserImage("https://i.imgur.com/LOm6jSY.png");
              }

              setReinitAudio(!reInitAudio);

              // Perform other async operations with proper error handling
              try {
                // get premise data
                const premiseId = await AsyncStorage.getItem('premiseId');
                const premiseStringify = await AsyncStorage.getItem('premiseData');
                const parsedPremise = await JSON.parse(premiseStringify);

                // get serial number
                const serialNumberStringify = await getDeviceSerialNumber();
                const sn = JSON.parse(serialNumberStringify);
                const serialNumber = sn['persist.ro.serialno'];

                // get department data
                const departmentStringify = await AsyncStorage.getItem('departmentData');
                _.unset(params, 'image');
                console.log('parsedpremise:', parsedPremise);
                console.log('scan type:', scanTypeRef.current);

                setTimeout(() => {
                  // Close the door after a delay
                  setRecognitionStatus(false);
                  controlDoor('close');
                  setcurrentUserImage("https://i.imgur.com/LOm6jSY.png");
                }, 3000);

                console.log('netinfo in face scan:', netinfoRef.current);
                if (netinfoRef.current.isConnected) {
                  await addAccessRecords({
                    ...params,
                    scanType: scanTypeRef.current,
                    faceToken: facetoken,
                    attributes: params,
                    premiseId,
                    serialNumber,
                    score: params.searchScore,
                    department: departmentStringify,
                    timestamps: moment().toISOString(),
                    user: user._id,
                  });
                } else {
                  await appendDataToTextFile('accessrecords', {
                    ...params,
                    scanType: scanTypeRef.current,
                    faceToken: facetoken,
                    attributes: params,
                    premiseId,
                    serialNumber,
                    score: params.searchScore,
                    department: departmentStringify,
                    timestamps: moment().toISOString(),
                    user: user._id,
                  });
                }
              } catch (asyncError) {
                console.log('Error during async operations after face detection:', asyncError);
              }
            } catch (controlError) {
              console.log('Error controlling door or processing detection:', controlError);
            }
          }
        } catch (processError) {
          console.log('Error processing face detection event:', processError);
        }
      });
    } catch (listenerError) {
      console.log('Error setting up face detection listener:', listenerError);
      return {
        remove: () => console.log('Dummy listener removal (no actual listener created)'),
      };
    }
  };

  useEffect(() => {
    let faceDetectListener = null;
    let unknownFaceListener = null;
    let viewId = null;

    try {
      // Initialize face detection listener
      faceDetectListener = startFaceDetectedListener();

      // Set up unknown face listener
      unknownFaceListener = eventEmitter.addListener(
        "UnknownFaceDetectedEvent",
        async (params) => {
          console.log('unknown face detected event:', params);
          setTrackId(params.trackID);
          try {
            // Handle unknown face
            // await playSound(false);
            setRecognitionStatus(false);
            setcurrentUserImage("https://i.imgur.com/LOm6jSY.png");
          } catch (error) {
            console.log('Error handling unknown face:', error);
          }
        },
      );

      // Get view ID and create fragment
      if (ref.current) {
        viewId = findNodeHandle(ref.current);
        if (viewId) {
          createFragment(viewId);
        } else {
          console.log('Warning: findNodeHandle returned null viewId');
        }
      } else {
        console.log('Warning: ref.current is null');
      }

      // Set footer text based on scan type
      switch (scanType) {
        case "checkIn":
          setfooterText("Employee Check-In Mode");
          break;
        case "checkOut":
          setfooterText("Employee Check-Out Mode");
          break;
        case "lunchOut":
          setfooterText("Employee Lunch-Out Mode");
          break;
        case "lunchIn":
          setfooterText("Employee Lunch-In Mode");
          break;
        case "videoCall":
          setfooterText("Video Doorbell Mode");
          break;
        default:
          setfooterText("Scan Mode");
          break;
      }
    } catch (error) {
      console.log('Error in camera setup:', error);
    }

    // Clean up function
    return () => {
      console.log('Cleaning up camera resources...');

      // Clean up event listeners
      if (faceDetectListener && typeof faceDetectListener.remove === 'function') {
        try {
          faceDetectListener.remove();
        } catch (error) {
          console.log('Error removing face detect listener:', error);
        }
      }

      if (unknownFaceListener && typeof unknownFaceListener.remove === 'function') {
        try {
          unknownFaceListener.remove();
        } catch (error) {
          console.log('Error removing unknown face listener:', error);
        }
      }

      // Release face pass handler
      try {
        releaseFacePassHandler();
      } catch (error) {
        console.log('Error releasing face pass handler:', error);
      }

      // Destroy camera fragment
      if (viewId) {
        try {
          destroyFragment(viewId);
        } catch (error) {
          console.log('Error destroying fragment:', error);
        }
      }
    };
  }, []);

  // const turnLight = () => {
  //   if (!isLightOn) {
  //     if (recognitionStatus) {
  //       setlightColor("green");
  //     } else setlightColor("red");
  //     changeLight(lightColor);
  //     // changeLight("red");
  //     setisLightOn(true);
  //   } else {
  //     changeLight("off");
  //     setisLightOn(false);
  //   }
  // };

  const redisplayOverlay = () => {
    refCameraFooter.current.toggle(true);
  };

  // Effect for handling premise data
  useEffect(() => {
    // Create a separate async function within the effect
    const loadPremiseData = async () => {
      try {
        if (Dimensions) {
          setdimensions({
            width: Dimensions.get("window").width,
            height: Dimensions.get("window").height,
          });
        }

        // Load premise data with proper error handling
        try {
          const premiseData = await getTextFile('premiseData');
          console.log('premise data:', premiseData);

          if (premiseData) {
            console.log('premise data after sync:', premiseData);
            const parsedInfo = premiseData;

            // Set name and theme safely
            if (refCameraHeader.current) {
              refCameraHeader.current.setName(parsedInfo?.name || 'Default Premise');
              if (parsedInfo?.theme) {
                refCameraHeader.current.setTheme(parsedInfo.theme);
              }
            }

            // Set logo and themes safely
            if (refCameraFooter.current) {
              if (parsedInfo?.theme?.premiseLogoUrl) {
                refCameraFooter.current.setLogo(parsedInfo.theme.premiseLogoUrl);
              }

              // Safe theme setting with fallback
              if (parsedInfo?.theme) {
                try {
                  refCameraFooter.current.setThemes(parsedInfo.theme);
                } catch (error) {
                  console.log('Error setting footer themes, using defaults:', error);
                  // Use default theme colors if theme setting fails
                  refCameraFooter.current.setThemes({
                    mainColor: "#FFF",
                    textColor: "#000",
                  });
                }
              }
            }

            // Safe theme color setting with fallback
            if (refAccessStatus.current) {
              const mainColor = parsedInfo?.theme?.mainColor !== "#undefined" ? parsedInfo?.theme?.mainColor : "#083b74";
              const textColor = parsedInfo?.theme?.textColor !== "#undefined" ? parsedInfo?.theme?.textColor : "#0c2358";

              refAccessStatus.current.setThemeColors({
                mainColor,
                textColor,
              });
            }
          }
        } catch (error) {
          console.log('Error loading premise data:', error);
        }
      } catch (error) {
        console.log('Error in useEffect processing premise data:', error);
      }
    };

    // Call the async function
    loadPremiseData();

    // No cleanup function needed for this effect
  }, [Dimensions]);

  return (
    <SafeAreaView style={{ height: Dimensions.get("screen").height }}>
      <CameraHeader
        ref={refCameraHeader}
      />

      <View id="cameraBody">
        <View style={{ marginTop: 0, display: "flex" }}>
          <FacePassViewManager
            style={{
              // aspectRatio: '1/1',
              // display: "none",
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "#000",
              // converts dpi to px, provide desired height
              height: PixelRatio.getPixelSizeForLayoutSize(
                Dimensions.get("window").height,
              ),
              // converts dpi to px, provide desired width
              width: PixelRatio.getPixelSizeForLayoutSize(
                Dimensions.get("window").width + 140,
              ),
            }}
            ref={ref}
          />
        </View>
      </View>
      <AccessStatus
        scanType={scanType}
        ref={refAccessStatus}
        // ref={accessStatusRef}
        // isScanning={isScanning}
        status={recognitionStatus}
        type={scanType}
        faceSource={currentUserImage}
        employeeName={userFullName}
      />

      <CameraFooterOverlay
        ref={refCameraFooter}
        redisplayOverlay={redisplayOverlay}
        scanType={scanType}
        setScanType={setScanType}
      />
    </SafeAreaView>
  );
}

const CONSOLE = {
  Reset: "\x1b[0m",
  Bright: "\x1b[1m",
  Dim: "\x1b[2m",
  Underscore: "\x1b[4m",
  Blink: "\x1b[5m",
  Reverse: "\x1b[7m",
  Hidden: "\x1b[8m",

  FgBlack: "\x1b[30m",
  FgRed: "\x1b[31m",
  FgGreen: "\x1b[32m",
  FgYellow: "\x1b[33m",
  FgBlue: "\x1b[34m",
  FgMagenta: "\x1b[35m",
  FgCyan: "\x1b[36m",
  FgWhite: "\x1b[37m",
  FgGray: "\x1b[90m",
};

export default ScanFaceMain;
