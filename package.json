{"name": "quantumfacepass", "version": "1.0.0", "scripts": {"start": "expo start", "android": "npx expo run:android", "production": "expo run:android --variant release", "ios": "expo run:ios", "web": "expo start --web", "postinstall": "patch-package", "deploy-android": "cd ./android && ./gradlew assembleRelease"}, "dependencies": {"@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/netinfo": "^11.4.1", "@react-native-picker/picker": "2.4.10", "@react-navigation/native": "^6.1.6", "@react-navigation/stack": "^6.3.16", "@realm/react": "^0.5.1", "axios": "^1.4.0", "cors": "^2.8.5", "expo": "49.0.9", "expo-av": "~13.4.1", "expo-camera": "~13.4.2", "expo-constants": "~14.4.2", "expo-dev-client": "~2.4.6", "expo-device": "~5.4.0", "expo-file-system": "~15.4.5", "expo-linear-gradient": "~12.3.0", "expo-linking": "~5.0.2", "expo-modules-core": "~1.5.12", "expo-router": "2.0.0", "expo-splash-screen": "~0.20.4", "expo-status-bar": "~1.6.0", "facepass-react-native-module": "0.1.43", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react": "18.2.0", "react-native": "0.72.10", "react-native-agora": "^4.2.1", "react-native-bouncy-checkbox": "^3.0.7", "react-native-confirmation-code-field": "^7.3.1", "react-native-device-info": "^10.8.0", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.12.0", "react-native-get-random-values": "^1.9.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-user-avatar": "^1.0.8", "realm": "^11.10.1", "rn-fetch-blob": "^0.12.0", "sharp": "^0.33.5", "socket.io-client": "^4.7.0", "uuid": "^9.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "babel-plugin-module-resolver": "^4.1.0", "eslint": "8.11.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.29.4", "eslint-plugin-react-hooks": "^4.3.0", "prettier": "^3.0.2"}, "private": true}