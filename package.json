{"name": "qv_backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "serve": "nodemon index.js", "deploy": "gcloud app deploy --project dahua-e2392 && gcloud app logs tail -s default"}, "author": "", "license": "ISC", "dependencies": {"body-parser": "^1.19.0", "cors": "^2.8.5", "express": "^4.17.1", "firebase-admin": "^10.0.0", "joi": "^17.4.2", "lodash": "^4.17.21", "moment": "^2.29.1", "moment-timezone": "^0.5.34", "request": "^2.88.2", "request-promise": "^4.2.6", "sharp": "^0.29.3", "socket.io": "^2.3.0", "socket.io-client": "^2.3.0", "uuid": "^8.3.2"}}