{"name": "facial-recognition", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon ./src/app.js", "start": "node ./src/app.js", "postman-dev": "AUTH_MODE=postman nodemon ./src/app.js", "setup-device": "node ./setup-device.js && node ./get-device-serial.js", "deploy": "gcloud app deploy --appyaml production.yaml"}, "repository": {"type": "git", "url": "git+https://github.com/andychoo7005/facial-recognition.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/andychoo7005/facial-recognition/issues"}, "homepage": "https://github.com/andychoo7005/facial-recognition#readme", "dependencies": {"@casl/ability": "^6.3.3", "@middleware.io/node-apm": "^2.1.0", "@socket.io/mongo-adapter": "^0.3.0", "axios": "^1.6.5", "bcrypt": "^5.1.0", "body-parser": "^1.20.2", "chalk": "^5.2.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "ejs": "^3.1.9", "exceljs": "4.3.0", "express": "^4.18.2", "fast-xml-parser": "^4.5.1", "firebase-admin": "^11.8.0", "firebase-functions": "^4.2.1", "google-auth-library": "^8.8.0", "helmet": "^6.0.1", "is-base64": "^1.1.0", "joi": "^17.8.4", "limax": "^4.1.0", "lodash": "^4.17.21", "mailgun-js": "^0.22.0", "moment": "^2.29.4", "moment-timezone": "^0.5.41", "mongodb": "^5.6.0", "mongoose": "^6.10.0", "mongoose-unique-validator": "^3.1.0", "multer": "^1.4.5-lts.1", "node-fetch": "2", "pkg": "^5.8.1", "read-excel-file": "^5.7.1", "redis": "^4.7.0", "request": "^2.88.2", "request-promise": "^4.2.6", "sharp": "^0.33.5", "short-unique-id": "^4.4.4", "socket.io": "4.7.0", "socket.io-client": "^4.6.1", "uuid": "^9.0.0", "ws": "^8.18.0", "yarn": "^1.22.19"}, "devDependencies": {"eslint": "^8.11.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-node": "^4.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.25.4", "eslint-plugin-node": "^11.1.0", "nodemon": "^2.0.21"}, "imports": {"#config/*": "./src/config/*.js", "#controllers/*": "./src/controllers/*.js", "#dtos/*": "./src/dtos/*.js", "#helpers/*": "./src/helpers/*.js", "#loaders/*": "./src/loaders/*.js", "#middlewares/*": "./src/middlewares/*.js", "#models/*": "./src/models/*.js", "#services/*": "./src/services/*.js", "#utils/*": "./src/utils/*.js", "#resources/*": "./src/resources/*"}}