import axios from "axios";
import React, { useEffect, useState } from "react";
import { Button, TextInput, View } from "react-native";
import { Manager, io } from "socket.io-client";


// const manager = new Manager(h)

function TestSocketPage({ route, navigation }) {
  const [message, setmessage] = useState("CALLING my server hello");

  const getUsers = async () => {
    await axios
      .get("http://192.168.0.223:8080/api/test-send-to-device")
      .then((response) => {
        console.log(JSON.stringify(response.data));
      });
  };

  const sendMessage = () => {
    console.log(socket);
    // socket.emit("FIRST_CALL", { m: message });
  };

  // useEffect(() => {
  //   // console.log('socket is connected')
  //   // getUsers();
  //   socket.on("connection", () => {
  //     console.log(socket.id); //
  //   });

  //   socket.emit("syncUser", (result) => {
  //     console.log(result); //
  //   });
  // }, []);

  return (
    <View>
      <TextInput value={message} onChange={(e) => setmessage(e)} />
      <Button
        onPress={() => sendMessage()}
        title="send message"
        color="#841584"
      />
      <Button title="go back" onPress={() => navigation.navigate("Home")} />
    </View>
  );
}

export default TestSocketPage;
