/* eslint-disable no-lonely-if */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-loop-func */
/* eslint-disable no-plusplus */
/* eslint-disable no-await-in-loop */
/* eslint-disable no-underscore-dangle */
import { Socket, io } from "socket.io-client";
import fetchBlob from 'rn-fetch-blob';
import RNFS from 'react-native-fs';
import _ from "lodash";
import {
  addFace,
  bindGroup,
  checkGroupExist,
  controlDoor,
  createGroup,
  deleteFace,
  getDeviceSerialNumber,
  getFace,
  getGroupInfo,
  restartApplication,
  restartDevice,
} from "facepass-react-native-module";
import AsyncStorage from "@react-native-async-storage/async-storage";
import React, { createContext } from "react";
import { ToastAndroid } from "react-native";
import moment from "moment";
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import {
  copyAsync,
  deleteAsync, documentDirectory, downloadAsync, makeDirectoryAsync,
} from "expo-file-system";
import {
  appendDataToTextFile, deleteFile, getTextFile, updateFile,
} from './helpers/handlingTextFile';

let socket = null;

const bindFaceTokenWithImagePath = async (userRealm, userId, faceToken, imagePath, existing) => {
  // TO DO LIST
  // no validation on face token will cause the database to be flooded with data.
  await userRealm.write(async () => {
    await userRealm.create("facetokenwithimagepath", {
      faceToken,
      imagePath,
      userId,
    });
  });
};

async function initSocket(key) {
  const serialNumberStringify = await getDeviceSerialNumber();
  const parsedSerialNumber = await JSON.parse(serialNumberStringify);
  const serialNumber = parsedSerialNumber['persist.ro.serialno'];
  console.log(`....INSIDE INIT FUNCTION....`);
  socket = io("https://facial-recognition-3bf89.as.r.appspot.com", {
    // socket = io("http://192.168.5.211:8080", {
    transports: ["websocket"],
    auth: {
      apiKey: key,
    },
    extraHeaders: {
      serialNumber,
    },
  });
  console.log(`....INSIDE INIT d....`);
  socket.on('connect', () => {
    console.log('connected to the server:');
  });

  // check face can be added or not.
  socket.on("check-face", async (data, cb) => {
    await addFace(data.faceImageUrl).then(async (faceToken) => {
      console.log("face token:", faceToken);
      const bindGroupResult = await bindGroup(
        faceToken.toString(),
        "facepass",
      );

      await deleteFace(faceToken.toString(), 'facepass').then((res) => {
        cb({
          result: 0,
          isFaceValidated: true,
        });
      }).catch((err) => {
        console.log('error:', err);
      });
    }).catch((err) => {
      cb({
        result: 0,
        isFaceValidated: false,
      });
    });
  });

  // add face
  socket.on("addFace", async (data, cb) => {
    await addFace(data.faceImageUrl).then(async (faceToken) => {
      console.log("face token:", faceToken);
      const bindGroupResult = await bindGroup(
        faceToken.toString(),
        "facepass",
      );

      await appendDataToTextFile('users', {
        faceToken,
        fullName: data.fullName,
        faceImageUrl: data.faceImageUrl,
      });

      // make an image copy to app scope folder
      // const base64FaceImage = await getFace(faceToken);
      // const uid = uuidv4();
      // const imagePath = await RNFS.writeFile(`/added-images/${uid}.png`, `data:image/png;base64,${base64FaceImage}`, 'base64');

      // make a copy to local and bind face token with image path.
      // await bindFaceTokenWithImagePath(userRealm, data._id, faceToken.toString(), imagePath, facetokenWithImagePath);

      cb({
        result: 0,
        faceToken,
      });
    });
  });

  // update premise data
  socket.on("updatePremise", async (data, cb) => {
    if (!_.isEmpty(data)) {
      const premiseBody = {
        ...data,
        timestamps: moment().toISOString(),
        _id: data._id,
      };

      console.log("premise data from cloud:", premiseBody);

      await updateFile('premiseData', premiseBody);

      cb({
        result: 0,
      });
    } else {
      // cb({
      //   result: 1,
      //   error: "Empty premise data received",
      // });
    }
  });

  // update settings
  socket.on("updateSettings", async (stgs, cb) => {
    console.log("settings from cloud", stgs);
    const settings = await getTextFile('generalSettings');
    if (!_.isEmpty(settings)) {
      const settingsBody = {
        ...stgs,
        timestamps: moment().toISOString(),
      };

      await updateFile('generalSettings', settingsBody);
      // await appendDataToTextFile('generalSettings', settingsBody);
    } else {
      const settingsBody = {
        ...stgs,
        timestamps: moment().toISOString(),
      };
      await updateFile('generalSettings', settingsBody);
    }
    await restartApplication();
    cb({
      result: 0,
    });
  });

  socket.on('reboot', async (data, cb) => {
    console.log('rebooting request by admin');
    await cb({
      result: 0,
    });
    await restartDevice();
  });

  socket.on('doorOpen', async (data, cb) => {
    console.log('data_doortest:', data.duration);
    console.log('opening door');
    await cb({
      result: 0,
    });
    await controlDoor("open");
    setTimeout(async () => {
      await controlDoor("close");
    }, _.get(data, 'duration', 7000));
  });
  socket.on('restart', async (data, cb) => {
    console.log('restart request by admin');
    await cb({
      result: 0,
    });
    await restartApplication();
  });
  socket.on('force-sync', async (data, cb) => {
    console.log('force sync request by admin');
    await cb({
      result: 0,
    });
    await syncUser();
  });

  socket.on('clear-face-cache', async (data, cb) => {
    await cb({
      result: 0,
    });

    const faceTokens = await getGroupInfo("facepass");
    await Promise.all(_.map(faceTokens, async (token) => {
      await deleteFace(token, 'facepass');
    }));
    await updateFile("users", []);
    await restartApplication();
  });
}

const updateSettings = async (setting) => {
  await socket.emit('update-settings', setting);
};

const addAccessRecords = async (record) => {
  await socket.emit('add-access-records', record);
};

const syncAccessRecords = async () => {
  const accessRecords = await getTextFile('accessrecords');
  await socket.emit('sync-access-records', accessRecords);
  await deleteFile("accessrecords");
};

const getApiKey = async () => {
  const key = await AsyncStorage.getItem("apiKey");
  return key;
};

let isSyncing = false;

const syncUser = async () => {
  if (isSyncing) return;

  try {
    isSyncing = true;
    console.log('syncing user from cloud');
    _.isNil((await getApiKey()) && socket)
      ? console.log(`NO API KEY STORED or SOCKET NOT INIT`)
      : socket.emit("syncUser", async (data) => {
        // add to local if new user is found in cloud.
        for (let i = 0; i < data.length; i++) {
          ToastAndroid.show(
            `Syncing user ${i + 1}/${data.length}`,
            ToastAndroid.SHORT,
          );

          const localUsers = await getTextFile('users');
          const foundUserInLocal = _.filter(localUsers, {
            _id: data[i]._id.toString(),
          });
          if (_.isEmpty(foundUserInLocal)) {
            await addFace(data[i].faceImageUrl).then(async (faceToken) => {
              console.log('face token:', faceToken);
              socket.emit("addFaceToken", {
                userId: data[i]._id.toString(),
                faceToken,
              });

              try {
                const userData = {
                  faceToken,
                  _id: data[i]._id.toString(),
                  fullName: data[i].fullName,
                  faceImageUrl: data[i].faceImageUrl,
                };
                await appendDataToTextFile('users', userData);

                // console.log('typeof created data:', typeof createdData);
                // console.log('created data:', await createdData);
                // console.log('is created data empty? - ', !_.isEmpty(await createdData));

                if (!_.isEmpty(userData.toString())) {
                  console.log('download data:');
                  await downloadAsync(
                    data[i].faceImageUrl,
                    `${documentDirectory}self/${data[i]._id.toString()}.png`,
                    {
                      cache: false,
                    },
                  ).then(async ({ uri }) => {
                    console.log('Finished downloading to ', uri);
                  })
                    .catch((error) => {
                      console.error('downloading error:', error);
                    });
                }
              } catch (err) { }
              // console.log("face token:", faceToken);
              try {
                await checkGroupExist("facepass");
              } catch (e) {
                try {
                  await createGroup("facepass");
                } catch (ea) {
                  // Create group error
                }
              } finally {
                await bindGroup(faceToken.toString(), "facepass");
              }
            }).catch((err) => {
              console.log('error when adding face:', err);
            });
          } else {
            // found user in local.
            if (data[i].fullName !== foundUserInLocal[0].fullName) {
              const amendedUserList = _.filter(localUsers, (user) => {
                if (user._id.toString() === data[i]._id.toString()) {
                  return {
                    ...user,
                    fullName: data[i].fullName,
                  };
                }
                return user;
              });
              await updateFile('users', amendedUserList);
            }
            if (data[i].faceImageUrl !== foundUserInLocal[0].faceImageUrl) {
              // console.log('got image different - from cloud:', data[i].faceImageUrl);
              // console.log('got image different - from local:', foundUserInLocal[0].faceImageUrl);
              // console.log('local user data:', foundUserInLocal[0]);
              const localFaceToken = foundUserInLocal;
              console.log('deleting face token:', localFaceToken[0].faceToken.toString());
              console.log('facetokenUlr', foundUserInLocal);
              try {
                await deleteFace(localFaceToken[0].faceToken.toString(), "facepass").then(
                  async () => {
                  },
                );
              } catch (e) {
                console.log("Delete error", e);
              }
              try {
                await addFace(data[i].faceImageUrl).then(async (faceToken) => {
                  console.log('face token is added:', faceToken);

                  const userData = {
                    faceToken,
                    fullName: data[i].fullName,
                    faceImageUrl: data[i].faceImageUrl,
                  };
                  const newUserList = _.filter(localUsers, (user) => {
                    if (user._id.toString() === userData._id.toString()) {
                      return userData;
                    }
                    return user;
                  });
                  await updateFile('users', newUserList);

                  console.log('added face data to realm:', JSON.stringify({
                    faceToken,
                    fullName: data[i].fullName,
                    faceImageUrl: data[i].faceImageUrl,
                  }, null, 2));
                  await bindGroup(faceToken.toString(), "facepass");
                  socket.emit("addFaceToken", {
                    userId: data[i]._id.toString(),
                    faceToken,
                  });
                });
              } catch (e) {
                console.log("add face error", e);
              }

              await downloadAsync(
                data[i].faceImageUrl,
                `${documentDirectory}self/${data[i]._id}.png`,
              ).then(({ uri }) => {
                console.log('Finished downloading to updated user face:', uri);
              })
                .catch((error) => {
                  console.error(error);
                });
            } else { }
          }
        }

        // delete the users that are not in the cloud.
        const users = await getTextFile('users');
        const listOfUsersToBeDeleted = _.differenceBy(users, data, '_id');
        console.log('difference:', listOfUsersToBeDeleted);
        const localUsers = await getTextFile('users');
        _.map(listOfUsersToBeDeleted, async (user) => {
          try {
            await deleteFace(user.faceToken, "facepass").then(
              async () => {
                _.remove(localUsers, (localUser) => localUser._id === user._id);
                console.log('latest local users:', localUsers);
                await updateFile('users', localUsers);
                await deleteAsync(`${documentDirectory}self/${user._id}.png`);
              },
            );
          } catch (err) {
            console.log("delete face:", err);
          }
        });
        ToastAndroid.show(
          `Done syncing users.`,
          ToastAndroid.SHORT,
        );
        isSyncing = false;
      });
  } catch (error) {
    console.log("Face import error", error);
  }
};

export {
  socket, syncUser, syncAccessRecords, initSocket, addAccessRecords, updateSettings,
};
