/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable no-unused-vars */
import {
  SafeAreaView, View, Text, PermissionsAndroid, Platform,
} from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import {
  ClientRoleType,
  createAgoraRtcEngine,

  IRtcEngine,
  RtcSurfaceView,
  ChannelProfileType,
  VideoCodecType,
  FrameRate,
  VideoDimensions,
  EncodingPreference,
} from 'react-native-agora';
import { TouchableOpacity } from 'react-native-gesture-handler';
import axios from 'axios';

const appId = '9d885264376345a594dc341a9fb25e41';
const channelName = 'spo';
const token = '007eJxTYDh1n73Gb8OyWzzWSq1fmq4Z/vy9ZrWiB9/mhsS/2Uof2iIUGCxTLCxMjcxMjM3NjE1ME00tTVKSjU0MEy3TkoxMU00My/ffTWkIZGRg9N/BCCTBEMRnZiguyGdgAABqox+Q';
const uid = 0;

function Doorbell({ navigation }) {
  const agoraEngineRef = useRef(); // Agora engine instance
  const [isJoined, setIsJoined] = useState(false); // Indicates if the local user has joined the channel
  const [remoteUid, setRemoteUid] = useState(0); // Uid of the remote user
  const [message, setMessage] = useState(''); // Message to the user

  const getPermission = async () => {
    if (Platform.OS === 'android') {
      await PermissionsAndroid.requestMultiple([
        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        PermissionsAndroid.PERMISSIONS.CAMERA,
      ]);
    }
  };

  function showMessage(msg) {
    setMessage(msg);
  }

  useEffect(() => {
    // Initialize Agora engine when the app starts
    setupVideoSDKEngine();
    return (() => {
      leave();
    });
  }, []);

  const setupVideoSDKEngine = async () => {
    try {
      // use the helper function to get permissions
      if (Platform.OS === 'android') { await getPermission(); }
      agoraEngineRef.current = createAgoraRtcEngine();
      const agoraEngine = agoraEngineRef.current;
      agoraEngine.registerEventHandler({
        onJoinChannelSuccess: () => {
          showMessage(`Successfully joined the channel ${channelName}`);
          setIsJoined(true);
        },
        onUserJoined: (_connection, Uid) => {
          showMessage(`Remote user joined with uid ${Uid}`);
          setRemoteUid(Uid);
        },
        onUserOffline: (_connection, Uid) => {
          showMessage(`Remote user left the channel. uid: ${Uid}`);
          setRemoteUid(0);
        },

      });

      agoraEngine.initialize({
        appId,
        channelProfile: ChannelProfileType.ChannelProfileCommunication,
      });
      agoraEngine.enableVideo();
      // const videoConfig = {
      //   frameRate: FrameRate.FrameRateFps60,
      //   bitrate: 2000,
      //   codecType: VideoCodecType.VideoCodecH265,
      //   advanceOptions: {
      //     encodingPreference: EncodingPreference.PreferHardware,
      //   },
      // };

      // agoraEngine.setVideoEncoderConfiguration(videoConfig);
      join();
    } catch (e) {
      console.log(e);
    }
  };

  const join = async () => {
    if (isJoined) {
      return;
    }
    try {
      agoraEngineRef.current?.setChannelProfile(
        ChannelProfileType.ChannelProfileCommunication,
      );
      const videoConfig = {
        frameRate: FrameRate.FrameRateFps60,
        bitrate: 2000,
        codecType: VideoCodecType.VideoCodecH265,
        advanceOptions: {
          encodingPreference: EncodingPreference.PreferHardware,
        },
      };

      agoraEngineRef.current?.startPreview();

      agoraEngineRef.current?.joinChannel(token, channelName, uid, {
        clientRoleType: ClientRoleType.ClientRoleBroadcaster,
      });
      agoraEngineRef.current?.setVideoEncoderConfiguration(videoConfig);
      const data = {
        serialNumber: "64c1d3b1828cfff73a97708c",
        status: "start",

      };
      // const response = await axios.post("http://192.168.0.239:8080/api/call/start", data);
      // console.log(response);
    } catch (e) {
      console.log(e);
    }
  };

  const leave = () => {
    try {
      agoraEngineRef.current?.disableVideo();
      agoraEngineRef.current?.stopPreview();

      agoraEngineRef.current?.leaveChannel();
      agoraEngineRef.current?.release();

      setRemoteUid(0);
      setIsJoined(false);
      showMessage('You left the channel');
      navigation.navigate("Home");
    } catch (e) {
      console.log(e);
    }
  };

  return (
    <SafeAreaView style={{
      flex: 1, flexDirection: "column",
    }}
    >
      {isJoined ? (
        <React.Fragment key={0}>
          {/* <RtcSurfaceView canvas={{ uid: 0 }} /> */}
          <Text style={{ marginTop: 10 }}>
            Local user uid:
            {' '}
            {uid}
          </Text>
          <Text>{message}</Text>

        </React.Fragment>
      ) : (
        <Text>Join a channel</Text>
      )}
      {isJoined && remoteUid !== 0 ? (
        <>
          <View style={{ flex: 15 }}>
            <React.Fragment key={remoteUid}>
              <Text>
                Remote user uid:
                {' '}
                {remoteUid}
              </Text>
              <RtcSurfaceView
                canvas={{ uid: 0 }}
                style={{
                  width: '100%',
                  height: '100%',
                // position: 'absolute',
                // left: 0,
                // top: 0,
                }}
              />

            </React.Fragment>
          </View>
          <View style={{
            flex: 1,
            flexGrow: 1,
          }}
          >
            {/* <Text>agora</Text> */}
            {/* <TouchableOpacity style={{ height: 200, backgroundColor: 'green' }} onPress={join}>
                <Text>Join</Text>
              </TouchableOpacity> */}
            <TouchableOpacity
              style={{
                height: '100%',
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: 'red',
              }}
              onPress={leave}
            >
              <Text style={{ textAlign: 'center' }}>Leave</Text>
            </TouchableOpacity>
          </View>

        </>
      ) : (
        <Text>Waiting for a remote user to join</Text>
      )}

    </SafeAreaView>
  );
}

export default Doorbell;
