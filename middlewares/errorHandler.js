const _ = require('lodash')
const responses = require('../helpers/responses')
module.exports = (err, req, res, next) => {
  if (_.has(err, 'success')) {
    // Here means the error is expected
    return res.status(err.status).send(err);
  }
  // logging all unexpected error
  console.error(`${req.method} - ${req.originalUrl} [Unexpected Error]`, err);
  return res.status(500).send(responses.exception(err.message));
}
