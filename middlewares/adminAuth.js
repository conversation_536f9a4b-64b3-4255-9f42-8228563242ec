const {db, admin} = require('../helpers')
const responses = require('../helpers/responses')

module.exports = async (req, res, next) => {
  try {
    const key = req.headers.authorization;

    const verify = await admin.auth().verifyIdToken(key, true)
    req.user = await db.doc(`admins/${verify.uid}`).get().then(doc => {
      const data = doc.data();
      data.id = doc.id;
      return data;
    })

    return next()
  } catch (e) {
    return res.failure(e.message)
  }
}
