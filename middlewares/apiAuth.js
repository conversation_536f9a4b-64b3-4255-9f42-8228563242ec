const {db} = require('../helpers')
const responses = require('../helpers/responses')

module.exports = async (req, res, next) => {
  try {
    const key = req.headers.authorization;
    const getAdminByKey = await db.collection('admins')
      .where('apiKey', '==', key)
      .get()
      .then(collection => {
        if(collection.size) {
          const doc = collection.docs[0];
          const data = doc.data();
          data.id = doc.id;
          return data;
        }
        return null
      });

    if(!getAdminByKey) {
      throw responses.failure("Invalid API Key", 401)
    }

    console.log(`[${getAdminByKey.id}] ${req.method} - ${req.originalUrl}`, req.body);

    req.user = getAdminByKey;
    return next()
  } catch (e) {
    return res.failure(e.message)
  }
}
