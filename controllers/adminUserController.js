const express = require('express')
const router = express.Router();
const {joiValidateJson, db, parseFirestoreDoc, uploadImage, compressPhotoBase64, serverTimestamp} = require('../helpers')
const responses = require('../helpers/responses')
const Joi = require('joi')
const _ = require('lodash')
const {
  detectMask, detectFaceQuality,
} = require('../helpers/facepass')
const {
  getAdminPremiseById
} = require('../services/premiseService')

module.exports = (io, serverUsers, facePassSockets) => {

  const updateUserEntry = async (req, res, next) => {
    try {

      const {allowedPremises} = req.user;
      const {id, premiseEntry} = req.body

      const allowedUpdateEntry = {};
      const premises = [];

      _.forEach(allowedPremises, premiseId => {
        const premiseEntryInfo = _.get(premiseEntry, premiseId, false);
        if (premiseEntryInfo) {
          allowedUpdateEntry[`premiseEntry.${premiseId}`] = premiseEntryInfo;
          premises.push(premiseId)
        }
      })

      await db.doc(`users/${id}`).update(allowedUpdateEntry)

      _.forEach(premises, (premiseId) => {
        _.forEach(_.get(facePassSockets, premiseId), (socket) => {
          socket.emit('sync')
        })
      })

      res.success('ok')
    } catch (e) {
      next(e)
    }
  }

  const deleteUser = async (req, res, next) => {
    try {

      const {allowedPremises} = req.user;
      const {uid} = req.params

      const ref = db.doc(`users/${uid}`);

      const user = await ref.get().then(doc => doc.data());

      if (_.get(user, 'from') !== 'manual') {
        throw responses.failure('Only manually added user can be deleted');
      }

      const canEdit = _.some(_.map(allowedPremises, premiseId => {
        return _.has(user, `premises.${premiseId}`)
      }));

      if (!canEdit) {
        throw responses.failure(`You don't have permission to edit this.`);
      }

      await db.doc(`users/${uid}`).delete();

      res.success('ok')
    } catch (e) {
      next(e)
    }
  }

  const createUser = async (req, res, next) => {
    try {
      const admin = req.user;

      const params = joiValidateJson(req.body, {
        photoBase64: Joi.string().base64().required(),
        fullName: Joi.string().required(),
        premiseId: Joi.string().required(),
      })

      const id = db.collection(`users`).doc().id;

      const premise = await getAdminPremiseById(params.premiseId, admin.id);

      if (!premise) {
        throw responses.failure('Invalid Premise ID')
      }

      let premiseUpdate = {
        id: params.premiseId,
        name: premise.name,
        createdAt: serverTimestamp
      }

      let premiseEntry = {
        access: true,
        type: 'toggle',
      }

      const user = {
        id,
        active: true,
        status: "active",
        fullName: _.get(params, 'fullName', false),
        icNo: _.get(params, 'icNo', false),
        email: _.get(params, 'email', false),
        phoneNumber: _.get(params, 'phoneNumber', false),
        createdAt: serverTimestamp,
        createdBy: admin.id,
        from: 'manual',
        premises: {
          [params.premiseId]: premiseUpdate
        },
        premiseEntry: {
          [params.premiseId]: premiseEntry
        }
      }

      let photoBase64 = await compressPhotoBase64(params.photoBase64);
      await detectFaceQuality(photoBase64);
      user.photoUrl = await uploadImage(photoBase64);

      const userRef = db.doc(`users/${id}`);
      await userRef.set(user)

      res.success('ok')
    } catch (e) {
      next(e)
    }
  }

  router.post('/', createUser)
  router.put('/entry', updateUserEntry)
  router.delete('/:uid', deleteUser)

  return router
}
//module.exports = router
