const express = require('express')
const router = express.Router();
const {joiValidate<PERSON>son, db, parseFirestoreDoc} = require('../helpers')
const {getAdminPremiseById} = require('../services/premiseService')
const responses = require('../helpers/responses')
const Joi = require('joi')
const _ = require('lodash')
const moment = require('moment')

module.exports = (io, serverUsers, facePassSockets) => {

  const getRecords = async (req, res, next) => {
    const admin = req.user;

    try {

      const params = joiValidateJson(req.query, {
        fromDate: Joi.string().required(),
        toDate: Joi.string().required(),
        premiseId: Joi.string().required(),
        userId: Joi.string()
      })

      const records = []

      const fromDate = moment(params.fromDate).startOf('day')
      const toDate = moment(params.toDate).endOf('day')

      if(!fromDate.isValid() || !toDate.isValid()) {
        throw responses.failure('Date format is invalid');
      } else if (!_.includes(admin.allowedPremises, params.premiseId)) {
        throw responses.failure('Invalid Premise ID');
      }

      let query = db.collection(`facePassScans`)
        .where('premiseId', '==', params.premiseId)
        .where('createdAt', '>=', fromDate.toDate())
        .where('createdAt', '<=', toDate.toDate())

      if(params.userId) {
        query = query.where('uid', '==', params.userId)
      }

      await query
        .orderBy("createdAt", 'desc')
        .get()
        .then(collection => {
          collection.forEach(doc => {
            records.push(parseFirestoreDoc(doc))
          })
        })

      res.success(records)
    } catch (e) {
      next(e)
    }
  }

  router.get('/', getRecords)

  return router;
}
