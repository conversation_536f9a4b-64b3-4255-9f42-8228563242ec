runtime: nodejs
env: flex

runtime_config:
    operating_system: "ubuntu22"
    runtime_version: 22

network:
  session_affinity: true

manual_scaling:
  instances: 1

resources:
  cpu: 1
  memory_gb: 1
  disk_size_gb: 10

env_variables:
  #mongodb
  DATABASE_USERNAME: 'arX'
  DATABASE_PASSWORD: 'arx'
  DATABASE_HOST: 'cluster0.9m1y6.mongodb.net'
  DATABASE_NAME: 'quantum-vision'

  #firebase
  FIREBASE_ADMIN_CREDENTIALS_FILENAME: 'facial-recognition-3bf89-firebase-adminsdk-g3a42-791efa1632.json'
  # FIREBASE_STORAGE_BUCKET:
  # FIREBASE_REALTIME_DATABASE:

  ## mailgun
  DOMAIN: 'sandbox07f06d98f013460882e4c3a32490cb6d.mailgun.org'
  MAILGUN_API_KEY: '**************************************************'
  MAILGUN_SENDER: '<EMAIL>'
