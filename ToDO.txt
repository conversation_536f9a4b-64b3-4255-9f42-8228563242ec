To be done:

hide something(<PERSON>ail password,those unused feature)


Login ,logout(TESTING)- done

createDepartment -> done

Change premise to manage inside dashboard - done

button to back in device setting - done

Employee edit-move additional information to left(together With name those),-done

Remove call and live statistic from menu - done

Leave only google login - done

scan record add in-done

Record date filter-done

Hide elevator ip ,fix undefined issue after back from detail setting (Device setting ) - done

User filtering(country), make data such as departments in different color-pending (DB Store color?)

Make display data into andy give

Some Styling issue-done

Think on Dashboard content

All edit/add/delete function

Call( one to many / many to one)

Learn eslint

Record data need review



-
User list no created at
Attendance record list,one ppl only record one