import _ from "lodash";
import moment from "moment-timezone";
import React, { useEffect, useState } from "react";
import { Dimensions, StyleSheet, Text } from "react-native";
import premiseInfo from "../premiseInfo";

const PREMISE_ID = "arx";

function TimeDate({ type }) {
  const [currentTime, setCurrentTime] = useState(moment().format("hh:mmA"));
  const [currentDate, setCurrentDate] = useState(moment().format('DD/MM/YYYY ddd'));

  useEffect(() => {
    const intervalId = setInterval(() => {
      setCurrentTime(moment().format("hh:mmA"));
      setCurrentDate(moment().format('DD/MM/YYYY ddd'));
    }, 1000); // Update every 1 second

    return () => clearInterval(intervalId);
  }, []);

  return (
    <Text numberOfLines={1} style={styles.headerText}>
      {type === 'time' ? currentTime : currentDate}
    </Text>
  );
}

const styles = StyleSheet.create({
  headerText: {
    display: "flex",
    fontSize: 16,
    color: _.get(premiseInfo, `${PREMISE_ID}.fontColor`),
    fontWeight: "600",
    maxWidth: Dimensions.get("screen").width * 0.3,
  },
});

export default TimeDate;
