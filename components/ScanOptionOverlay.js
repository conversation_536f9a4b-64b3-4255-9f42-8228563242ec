import {
  Dimensions,
  StyleSheet,
  Image,
  ImageBackground,
  Text,
  View,
  Pressable,
  But<PERSON>,
  BackHandler,
  ActivityIndicator,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { pauseListener } from "facepass-react-native-module";
import FloatingButton from "./FloatingButton";
// import { useNavigation } from "@react-navigation/native";

function ScanOptionOverlay({
  // showOverlay,
  // setShowOverlay,
  // setScanType,
  // scanType,
  // setfooterText,
  // setIsLoadPage,
  footerTextRef,
}, ref) {
  const [showOverlay, setShowOverlay] = useState(true);
  const [scanType, setScanType] = useState("none");

  const [setFooter, setSetFooter] = useState(null); // keeping track of the ref

  useImperativeHandle(ref, () => ({
    toggle: (e) => setShowOverlay(e),
    getType: () => scanType,
  }), []);

  const pauseRecognition = async () => {
    await pauseListener(true);
    console.log(CONSOLE.FgBlue, "pausing recognition");
  };

  const resumeRecognition = async () => {
    console.log(CONSOLE.FgBlue, "resuming recognition");
    pauseListener(false);
  };

  useEffect(() => {
    console.log(CONSOLE.FgRed, footerTextRef);
    if (showOverlay) {
      pauseRecognition();
    } else {
      resumeRecognition();
    }
  }, [showOverlay, footerTextRef]);

  return (
    <View
      style={{
        display: showOverlay ? "flex" : "none",
        width: Dimensions.get("screen").width,
        height: Dimensions.get("screen").height,
        backgroundColor: "#FFF",
        position: "absolute",
        // opacity: 0.5,
      }}
    >
      <ImageBackground
        source={require("../assets/FRBackground.png")}
        resizeMethod="resize"
        resizeMode="stretch"
        style={{ padding: 30 }}
      >
        <View style={{ display: "flex", alignItems: "center" }}>
          <Image
            resizeMethod="resize"
            resizeMode="contain"
            style={{
              maxWidth: 0.7 * Dimensions.get("screen").width,
              marginTop: 50,
              // borderWidth: 0.5,
            }}
            source={require("../assets/qv_logo_new.png")}
          />
        </View>
        <View style={{ marginTop: 40 }}>
          <View style={{ display: "flex", flexDirection: "row" }}>
            <StyleButton
              text="Check-In"
              color1="#093c75"
              color2="#0d2458"
              imageSource={require("../assets/checkinIcon.png")}
              onPress={async () => {
                setScanType("checkIn");
                // footerTextRef.setFooter("Employee Check-In Mode")
                setShowOverlay(false);
              }}
            />
            <StyleButton
              text="Check-Out"
              color1="#FF9C06"
              color2="#FF9C06"
              imageSource={require("../assets/checkout.png")}
              onPress={async () => {
                setScanType("checkOut");
                // footerTextRef.setFooter("Employee Check-Out Mode")
                setShowOverlay(false);
              }}
            />
          </View>
          <View style={{ display: "flex", flexDirection: "row" }}>
            <StyleButton
              text="Lunch-Out"
              color1="#309831"
              color2="#008001"
              imageSource={require("../assets/lunchout.png")}
              onPress={() => {
                setScanType("lunchOut");
                footerTextRef.setFooter("Employee Lunch-Out Mode");
                setShowOverlay(false);
              }}
            />
            <StyleButton
              text="Lunch-In"
              color1="#e12f2f"
              color2="#da0001"
              imageSource={require("../assets/lunchin.png")}
              onPress={async () => {
                setScanType("lunchIn");
                footerTextRef.setFooter("Employee Lunch-In Mode");
                setShowOverlay(false);
              }}
            />
          </View>
          <View style={{ display: "flex", flexDirection: "row" }}>
            <StyleButton
              text="Scan"
              color1="#093c75"
              color2="#0d2458"
              imageSource={require("../assets/scanicon.png")}
              onPress={async () => {
                setScanType("none");
                setShowOverlay(false);
                footerTextRef.setFooter("Scan Mode");
              }}
            />
          </View>
          <View style={{ display: "flex", flexDirection: "row" }}>
            {/* <StyleButton
              disabled
              text="View Doorbell"
              color1="#093c75"
              color2="#0d2458"
              imageSource={require("../assets/viewdoorbell.png")}
              onPress={() => {
                console.log("navigating to doorbell");
                // RootNavigation.navigate("Doorbell");
                // navigation.navigate("Doorbell");
              }}
            /> */}
            {/* <StyleButton
              text="Exit app"
              color1="#093c75"
              color2="#0d2458"
              imageSource={require("../assets/viewdoorbell.png")}
              onPress={() => BackHandler.exitApp()}
            /> */}
          </View>
        </View>
      </ImageBackground>
      <View style={{
        display: "flex", position: "absolute", top: 30, right: 20,
      }}
      >
        <FloatingButton link="Settings" />
      </View>
    </View>
  );
}

function StyleButton({
  color1 = "#777",
  color2 = "#777",
  text = "TEXT_BUTTON",
  imageSource = require("../assets/checkout.png"),
  onPress = () => null,
  disabled = false,
}) {
  return (
    <Pressable
      style={{
        borderRadius: 10,
        flex: 1,
        margin: 15,
      }}
      disabled={disabled}
      onPress={onPress}
    >
      <LinearGradient
        colors={[color1, color2]}
        style={{
          padding: 17,
          borderRadius: 10,
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
        }}
      >
        <Image style={{ height: 20, width: 20 }} source={imageSource} />
        <Text
          style={{
            color: "#FFF",
            fontSize: 20,
            textAlign: "center",
            flex: 1,
            fontWeight: "600",
            // TODO: ADD POPPINS FONT8
          }}
        >
          {text}
        </Text>
      </LinearGradient>
    </Pressable>
  );
}

export default React.forwardRef(ScanOptionOverlay);

// This is just for QOL dev purposes
const CONSOLE = {
  Reset: "\x1b[0m",
  Bright: "\x1b[1m",
  Dim: "\x1b[2m",
  Underscore: "\x1b[4m",
  Blink: "\x1b[5m",
  Reverse: "\x1b[7m",
  Hidden: "\x1b[8m",

  FgBlack: "\x1b[30m",
  FgRed: "\x1b[31m",
  FgGreen: "\x1b[32m",
  FgYellow: "\x1b[33m",
  FgBlue: "\x1b[34m",
  FgMagenta: "\x1b[35m",
  FgCyan: "\x1b[36m",
  FgWhite: "\x1b[37m",
  FgGray: "\x1b[90m",
};
