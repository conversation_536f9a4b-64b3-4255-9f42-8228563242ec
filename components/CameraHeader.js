import { LinearGradient } from 'expo-linear-gradient';
import React, { useImperativeHandle, useState } from 'react';
import { Dimensions, Text, View } from 'react-native';

function CameraHeader(props, ref) {
  const [companyName, setCompanyName] = useState(false);
  const [colors, setColors] = useState({
    maincolor: false,
    textcolor: false,
  });

  useImperativeHandle(ref, () => ({
    setName: (e) => {
      setCompanyName(e);
    },
    setTheme: (e) => {
      setColors({
        maincolor: e?.mainColor,
        textcolor: e?.textColor,
      });
    },
  }), []);

  return (
    <View id="header" style={{ width: "100%", marginTop: 0 }}>
      <LinearGradient
        style={{
				  display: "flex",
				  flexDirection: "row",
				  justifyContent: "space-around",
				  color: "#FFF",
				  height: Dimensions.get("screen").height * 0.05,
				  alignContent: "center",
				  alignItems: "center",
				  margin: 0,
				  // position:'absolute'
        }}
				// TODO: get colors from theme and apply here
        colors={[
				  colors.maincolor ? colors.maincolor : "#0b3d76",
				  colors.maincolor ? colors.maincolor : "#0c2458",
        ]}
      >
        <Text
          numberOfLines={1}
          style={{
					  fontWeight: "bold",
					  display: "flex",
					  fontSize: 16,
					  color: colors.textcolor,
					  maxWidth: Dimensions.get("screen").width * 0.3,
          }}
        >
          {companyName || "Premise Name N/A"}
        </Text>
      </LinearGradient>
    </View>
  );
}

export default React.forwardRef(CameraHeader);

// This is just for QOL dev purposes
const CONSOLE_ = {
  Reset: "\x1b[0m",
  Bright: "\x1b[1m",
  Dim: "\x1b[2m",
  Underscore: "\x1b[4m",
  Blink: "\x1b[5m",
  Reverse: "\x1b[7m",
  Hidden: "\x1b[8m",

  FgBlack: "\x1b[30m",
  FgRed: "\x1b[31m",
  FgGreen: "\x1b[32m",
  FgYellow: "\x1b[33m",
  FgBlue: "\x1b[34m",
  FgMagenta: "\x1b[35m",
  FgCyan: "\x1b[36m",
  FgWhite: "\x1b[37m",
  FgGray: "\x1b[90m",
};
