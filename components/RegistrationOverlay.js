import AsyncStorage from "@react-native-async-storage/async-storage";
import React, { useEffect, useRef } from "react";
import {
  ActivityIndicator,
  Button,
  Dimensions,
  Pressable,
  StyleSheet,
  Text,
  TextInput,
  View,
} from "react-native";
import {
  CodeField,
  Cursor,
  useBlurOnFulfill,
  useClearByFocusCell,
} from "react-native-confirmation-code-field";

const styles = StyleSheet.create({
  root: { flex: 1, padding: 20 },
  title: { textAlign: "center", fontSize: 30 },
  codeFieldRoot: { marginTop: 20 },
  cell: {
    width: 40,
    height: 40,
    lineHeight: 38,
    fontSize: 24,
    borderWidth: 2,
    borderColor: "#00000030",
    textAlign: "center",
    margin: 2,
  },
  focusCell: {
    borderColor: "#000",
  },
  registerbutton: {
    padding: 20,
    borderWidth: 1,
    width: "100%",
    marginTop: 30,
  },
});

function RegistrationOverlay(props) {
  const {
    apiString, setApiString, setSuccess, setKeyExist,
  } = props;
  const ref = useBlurOnFulfill({ value: apiString, cellCount: 8 });
  const [propss, getCellOnLayoutHandler] = useClearByFocusCell({
    value: apiString,
    setValue: setApiString,
  });
  const [loading, setloading] = React.useState(false);

  useEffect(() => {
    ref.current.focus();
  }, []);

  const saveApiKey = async () => {
    // await AsyncStorage.setItem("apiKey", apiString);
    setKeyExist(true);
    setSuccess(true);
  };

  try {
    return (
      <View
        style={{
          width: Dimensions.get("screen").width,
          height: Dimensions.get("screen").height,
          display: "flex",
          position: "absolute",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: "#FFF",
        }}
      >
        <Text style={{ fontSize: 22 }}>Please enter provided API key</Text>
        <View style={{ display: "flex", flexDirection: "row" }}>
          <CodeField
            ref={ref}
            {...propss}
            value={apiString}
            onChangeText={setApiString}
            cellCount={8}
            rootStyle={styles.codeFieldRoot}
            textContentType="oneTimeCode"
            renderCell={({ index, symbol, isFocused }) => (
              <View
                key={index}
                style={{ display: "flex", flexDirection: "row" }}
              >
                <Text
                  key={index}
                  style={[styles.cell, isFocused && styles.focusCell]}
                  onLayout={getCellOnLayoutHandler(index)}
                >
                  {symbol || (isFocused ? <Cursor /> : null)}
                </Text>
                {index === 3 ? (
                  <View style={{ padding: 10 }}>
                    <Text> - </Text>
                  </View>
                ) : null}
              </View>
            )}
          />
        </View>
        <View style={{ display: "flex", flexDirection: "column", padding: 20 }}>
          <Button
            title="Register"
            disabled={apiString.length < 8}
            onPress={() => saveApiKey()}
          />
        </View>
        <ActivityIndicator
          size="large"
          style={{
            display: loading ? "flex" : "none",
          }}
        />
      </View>
    );
  } catch (error) {}
}

export default RegistrationOverlay;
