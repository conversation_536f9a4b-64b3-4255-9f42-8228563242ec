import React, {
  useEffect, useImperativeHandle, useRef, useState,
} from "react";
import { LinearGradient } from "expo-linear-gradient";
import {
  Animated, Dimensions, Image, Text, View,
} from "react-native";
import UserAvatar from "react-native-user-avatar";
import _ from "lodash";
import { controlDoor } from "facepass-react-native-module";
import {
  deleteAsync, documentDirectory,
} from "expo-file-system";
import TimeDate from "./TimeDate";
import premiseInfo from "../premiseInfo";

function AccessStatus(props, ref) {
  const { scanType, status, faceSource, employeeName } = props;

  // const [status, setStatus] = useState(false);
  // const [faceSource, setFaceSource] = useState("https://i.imgur.com/LOm6jSY.png");
  // const [employeeName, setEmployeeName] = useState("");
  const [userId, setUserId] = useState(null);
  const [themeColors, setThemeColors] = useState({
    mainColor: "#083b74",
    textColor: "#0c2358",
  });

  let t = null;
  // const startHide = () => {
  //   if (!_.isNil(t)) {
  //     clearTimeout(t);
  //   }
  //   t = setTimeout(() => {
  //     setFaceSource("https://i.imgur.com/LOm6jSY.png");
  //     setEmployeeName("");
  //     setStatus(false);
  //     controlDoor("close");
  //   }, 5000);
  // };
  //
  // useImperativeHandle(ref, () => ({
  //   setStatus: (e) => {
  //     setStatus(e);
  //     startHide();
  //   },
  //   setUserId: (e) => {
  //     setUserId(e);
  //   },
  //   // setType: (e) => setType(e),
  //   setFaceSource: (e) => setFaceSource(e),
  //   setEmployeeName: (e) => setEmployeeName(e),
  //   setThemeColors: (e) => {
  //     console.log('access status page checking : ', e);
  //     setThemeColors(e);
  //   },
  // }), []);

  return (
    <View
      style={{
        alignItems: "center",
        justifyContent: "center",
        position: "absolute",
        width: "100%",
        height: 170,
        padding: 20,
        display: status ? "flex" : "none",
        bottom: "15%",
      }}
    >
      <LinearGradient
        colors={[
          // _.get(themeColors, `mainColor`, "#083b74"),
          // _.get(themeColors, `mainColor`, "#0c2358"),
          "#083b74", "#0c2358",
        ]}
        style={{
          // borderColor: "lime",
          borderRadius: 100,
          width: "100%",
          height: "100%",
          padding: 10,
          display: "flex",
          flexDirection: "row",
        }}
      >
        {/* here */}
        <RegisteredFace
          status={status}
          faceSource={faceSource}
          employeeName={employeeName}
          userId={userId}
        />
        <View
          style={{
            display: "flex",
            paddingLeft: 15,
            width: "80%",
            justifyContent: "center",
          }}
        >
          <Text
            style={{
              color: status ? "#0f0" : "#ff2f1a",
              fontSize: 24,
              fontWeight: "bold",
            }}
          >
            {accessMessage(status, scanType)}
          </Text>

          <Text style={{ color: "#FFF", fontSize: 16, fontWeight: "bold" }}>
            {status ? employeeName : "Face Unrecognized"}
          </Text>
          <View
            style={{
              paddingTop: 6,
              display: "flex",
              flexDirection: "row",
              justifyContent: "flex-start",
            }}
          >
            <TimeDate type="date" />
            <View style={{ marginRight: 30 }} />
            <TimeDate type="time" />
          </View>
        </View>
      </LinearGradient>
    </View>
  );
}

const accessMessage = (status, type) => {
  if (status) {
    switch (type) {
      case "checkIn":
        return "Employee Checked-In";
      case "checkOut":
        return "Employee Checked-Out";
      case "lunchOut":
        return "Lunch-Out";
      case "lunchIn":
        return "Lunch-In";
      default:
        return "Access Granted";
    }
  } else {
    return "Acess Denied";
  }
};

function RegisteredFace({
  status = false,
  faceSource,
  userId,
  employeeName = "",
}) {
  // console.log(faceSource);
  console.log(status);

  return (
    <View
      style={{
        height: "100%",
        aspectRatio: 1,
        borderRadius: 1000,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      {status ? (
        <Image
          name={employeeName}
          source={{ uri: faceSource }}
          // source={{ uri: `${documentDirectory}self/${userId}.png?${new Date()}` }}
          // source={{ uri: `file:///data/user/0/com.qinusaxia.quantumfacepass/files/self/${userId}.png` }}
          style={{
            width: 110,
            height: 110,
            borderRadius: 100,
          }}
        />
      ) : (
        <Text
          style={{
            fontSize: 80,
            fontWeight: "bold",
            color: "#FFF",
          }}
        >
          !
        </Text>
      )}
    </View>
  );
}

export default React.forwardRef(AccessStatus);

// This is just for QOL dev purposes
const CONSOLE_ = {
  Reset: "\x1b[0m",
  Bright: "\x1b[1m",
  Dim: "\x1b[2m",
  Underscore: "\x1b[4m",
  Blink: "\x1b[5m",
  Reverse: "\x1b[7m",
  Hidden: "\x1b[8m",

  FgBlack: "\x1b[30m",
  FgRed: "\x1b[31m",
  FgGreen: "\x1b[32m",
  FgYellow: "\x1b[33m",
  FgBlue: "\x1b[34m",
  FgMagenta: "\x1b[35m",
  FgCyan: "\x1b[36m",
  FgWhite: "\x1b[37m",
  FgGray: "\x1b[90m",
};
