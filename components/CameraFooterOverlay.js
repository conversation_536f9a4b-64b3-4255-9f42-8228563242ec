import { LinearGradient } from 'expo-linear-gradient';
import _ from 'lodash';
import React, {
  forwardRef, useEffect, useImperativeHandle, useState,
} from 'react';
import {
  ImageBackground, Pressable, Dimensions, View, Text, Image,
} from 'react-native';
import { getDeviceSerialNumber, pauseListener } from 'facepass-react-native-module';
import AsyncStorage from '@react-native-async-storage/async-storage';
import FloatingButton from "./FloatingButton";

const CameraFooterOverlay = forwardRef((props, ref) => {
  const { scanType, setScanType } = props;

  const [themeColor, setThemeColor] = useState({
    mainColor: "#FFF",
    textColor: "#000",
  });
  const [companyLogo, setcompanyLogo] = useState(null);
  // Here set default
  const [footerText, setfooterText] = useState("Scan Mode"); // TODO: Turn this into a component
  const [showOverlay, setShowOverlay] = useState(true);
  const [serialNumber, setSerialNumber] = useState('');
  const [defaultScan, setDefaultScan] = useState(null);
  useImperativeHandle(ref, () => ({
    setThemes: (e) => {
      if (
        _.get(e, "mainColor") === "#undefined"
        || _.get(e, "textColor") === "#undefined"
      ) {
        console.log("Theme colors are undefined, using default values");
        // Use default values instead of throwing an error
        setThemeColor({
          mainColor: "#FFF",
          textColor: "#000",
        });
      } else {
        setThemeColor({
          mainColor: e?.mainColor || "#FFF",
          textColor: e?.textColor || "#000",
        });
      }
    },

    setLogo: (e) => {
      setcompanyLogo(e);
    },
    setFooter: (e) => setfooterText(e),

    toggle: (e) => setShowOverlay(e),
    getType: () => {
      console.log('scan type in camera footer overlay ref:', scanType);
      return scanType;
    },
  }), []);

  useEffect(() => {
    console.log("serialNumber", serialNumber);
    // Hardcoded first
    // if (serialNumber == 'QV3011' || serialNumber == 'QV3018' || serialNumber == 'QV3024') {
    //   setScanType("checkIn");
    //   setfooterText("Employee Check-In Mode");
    //   setShowOverlay(false);
    // }

    // if (serialNumber == 'QV3015' || serialNumber == 'QV3014' || serialNumber == 'QV3016') {
    //   setScanType("checkOut");
    //   setfooterText("Employee Check-Out Mode");
    //   setShowOverlay(false);
    // }

    if (defaultScan == 'checkIn') {
      setScanType("checkIn");
      setfooterText("Employee Check-In Mode");
      setShowOverlay(false);
    }

    if (defaultScan == 'checkOut') {
      setScanType("checkOut");
      setfooterText("Employee Check-Out Mode");
      setShowOverlay(false);
    }
  }, [defaultScan]);

  const redisplayOverlay = () => {
    setShowOverlay(true);
  };

  return (
    <View style={{
      position: 'absolute',
      display: 'flex',
      height: Dimensions.get('screen').height,
    }}
    >
      <View
        id="footer"
        style={{
          width: Dimensions.get("screen").width,
          position: "absolute",
          height: Dimensions.get("screen").height,
        }}
      >
        <View style={{
          height: (7 / 8) * Dimensions.get("screen").height,
        }}
        />
        <View
          style={{
            height: Dimensions.get("screen").height,
            color: _.get(themeColor, `textColor`),
            backgroundColor: _.get(themeColor, `mainColor`),
            padding: 10,
            display: "flex",
            flexDirection: "row",
            alignItems: "flex-start",
            justifyContent: "flex-start",
          }}
        >
          {companyLogo ? (
            <Image
              source={{ uri: companyLogo }}
              resizeMethod="resize"
              resizeMode="contain"
              style={{
                borderWidth: 1,
                marginTop: 5,
                width: 0.24 * Dimensions.get("window").width,
                height: (1 / 10) * Dimensions.get("window").height,
              }}
            />
          ) : (
            <Image // default logo if no logo present
              source={require("../assets/qv_logo_new.png")}
              resizeMethod="resize"
              resizeMode="contain"
              style={{
                marginTop: 10,
                maxWidth: 0.3 * Dimensions.get("window").width,
                height: (1 / 11) * Dimensions.get("window").height,
              }}
            />
          )}
          <View
            style={{
              width: 0.7 * Dimensions.get("window").width,
              height: (1 / 9) * Dimensions.get("window").height,
              alignItems: "center",
              justifyContent: "center",
            }}
            onTouchStart={() => redisplayOverlay()}
          >
            <Text
              style={{
                fontSize: 20,
                fontWeight: "bold",
                color: _.get(themeColor, `textColor`),
              }}
            >
              {footerText}
            </Text>
            <Text
              style={{
                fontSize: 14,
                fontWeight: "400",
                color: _.get(themeColor, `textColor`),
                marginLeft: 'auto',
              }}
            >
              {serialNumber}
            </Text>
          </View>
        </View>
      </View>
      <ScanOptionOverlay
        setfooterText={setfooterText}
        setScanType={setScanType}
        showOverlay={showOverlay}
        setShowOverlay={setShowOverlay}
        setSerialNumber={setSerialNumber}
        setDefaultScan={setDefaultScan}
      />
    </View>
  );
});

function ScanOptionOverlay({
  setfooterText,
  setScanType,
  showOverlay,
  setShowOverlay,
  setSerialNumber, setDefaultScan,
}) {
  // const [showOverlay, setShowOverlay] = useState(true);
  // const [scanType, setScanType] = useState("none");

  const pauseRecognition = async () => {
    await pauseListener(true);
    console.log(CONSOLE.FgBlue, "pausing recognition");
  };

  const resumeRecognition = async () => {
    console.log(CONSOLE.FgBlue, "resuming recognition");
    pauseListener(false);
  };

  const [showCheckIn, setShowCheckIn] = useState(true);
  const [showLunchIn, setshowLunchIn] = useState(true);
  const [deviceType, setDeviceType] = useState(null);
  const logIt = () => console.log(showCheckIn);

  const getSerialNumber = async () => {
    const stringifySerialNumber = await getDeviceSerialNumber();
    const parsedSerialNumber = await JSON.parse(stringifySerialNumber);
    setSerialNumber(parsedSerialNumber['persist.ro.serialno']);
    const scanType = await AsyncStorage.getItem('defaultScan');
    setDefaultScan(scanType);
    // if (parsedSerialNumber['persist.ro.serialno'] == 'QV3014' || parsedSerialNumber['persist.ro.serialno'] == 'QV3015' || parsedSerialNumber['persist.ro.serialno'] == 'QV3016') {
    //   setDeviceType('checkOut');
    // } else if (parsedSerialNumber['persist.ro.serialno'] == 'QV3018' || parsedSerialNumber['persist.ro.serialno'] == 'QV3011' || parsedSerialNumber['persist.ro.serialno'] == 'QV3024') {
    //   setDeviceType('checkIn');
    // }
    if (scanType == 'checkOut') {
      setDeviceType('checkOut');
    } else if (scanType == 'checkIn') {
      setDeviceType('checkIn');
    }
  };

  useEffect(() => {
    AsyncStorage.getItem("showCheckIn").then((res) => {
      if (!_.isNil(res)) {
        setShowCheckIn(JSON.parse(res));
      } else {
        // console.log(CONSOLE.FgYellow, "showCheckIn not found, creating in Aysncstorage");
        AsyncStorage.setItem('showCheckIn', "true").then(() => console.log(CONSOLE.FgYellow, "set showCheckIn"));
      }
    });

    AsyncStorage.getItem('showLunchIn').then((res) => {
      if (!_.isNil(res)) {
        setshowLunchIn(JSON.parse(res));
      } else {
        // console.log(CONSOLE.FgYellow, "showLunchIn not found, creating in Aysncstorage");
        AsyncStorage.setItem('showLunchIn', "true").then(() => console.log(CONSOLE.FgYellow, "set showLunchIn"));
      }
    });

    getSerialNumber();
  }, []);

  React.useEffect(() => {
    // console.log(CONSOLE.FgRed, footerTextRef);
    if (showOverlay) {
      pauseRecognition();
    } else {
      resumeRecognition();
    }
  }, [showOverlay]);

  return (
    <View
      style={{
        display: showOverlay ? "flex" : "none",
        width: Dimensions.get("screen").width,
        height: Dimensions.get("screen").height,
        backgroundColor: "#FFF",
        position: "absolute",
        // opacity: 0.5,
      }}
    >
      <ImageBackground
        source={require("../assets/FRBackground.png")}
        resizeMethod="resize"
        resizeMode="stretch"
        style={{ padding: 30 }}
      >
        <View onTouchEnd={() => logIt()} style={{ display: "flex", alignItems: "center" }}>
          <Image
            resizeMethod="resize"
            resizeMode="contain"
            style={{
              maxWidth: 0.7 * Dimensions.get("screen").width,
              marginTop: 50,
            }}
            source={require("../assets/qv_logo_new.png")}
          />
        </View>
        <View style={{ marginTop: 40 }}>
          <View style={{
            flexDirection: "row",
            display: showCheckIn ? "flex" : "none",
          }}
          >
            {deviceType == "checkIn"
              ? (
                <StyleButton
                  text="Check-In"
                  color1="#093c75"
                  color2="#0d2458"
                  imageSource={require("../assets/checkinIcon.png")}
                  onPress={() => {
                    setScanType("checkIn");
                    // footerTextRef.setFooter("Employee Check-In Mode")
                    setfooterText("Employee Check-In Mode");
                    setShowOverlay(false);
                  }}
                />
              )
              : deviceType == "checkOut"
                ? (
                  <StyleButton
                    text="Check-Out"
                    color1="#FF9C06"
                    color2="#FF9C06"
                    imageSource={require("../assets/checkout.png")}
                    onPress={() => {
                      setScanType("checkOut");
                      // footerTextRef.setFooter("Employee Check-Out Mode")
                      setfooterText("Employee Check-Out Mode");
                      setShowOverlay(false);
                    }}
                  />
                )
                : (
                  <>

                    <StyleButton
                      text="Check-In"
                      color1="#093c75"
                      color2="#0d2458"
                      imageSource={require("../assets/checkinIcon.png")}
                      onPress={() => {
                        setScanType("checkIn");
                        // footerTextRef.setFooter("Employee Check-In Mode")
                        setfooterText("Employee Check-In Mode");
                        setShowOverlay(false);
                      }}
                    />
                    <StyleButton
                      text="Check-Out"
                      color1="#FF9C06"
                      color2="#FF9C06"
                      imageSource={require("../assets/checkout.png")}
                      onPress={() => {
                        setScanType("checkOut");
                        // footerTextRef.setFooter("Employee Check-Out Mode")
                        setfooterText("Employee Check-Out Mode");
                        setShowOverlay(false);
                      }}
                    />
                  </>
                )}

          </View>

          <View style={{
            flexDirection: "row",
            display: showLunchIn ? "flex" : "none",
          }}
          >
            {deviceType == null ? (
              <>
                <StyleButton
                  text="Lunch-Out"
                  color1="#309831"
                  color2="#008001"
                  imageSource={require("../assets/lunchout.png")}
                  onPress={() => {
                    setScanType("lunchOut");
                    // footerTextRef.setFooter("Employee Lunch-Out Mode")
                    setfooterText("Employee Lunch-Out Mode");
                    setShowOverlay(false);
                  }}
                />
                <StyleButton
                  text="Lunch-In"
                  color1="#e12f2f"
                  color2="#da0001"
                  imageSource={require("../assets/lunchin.png")}
                  onPress={() => {
                    setScanType("lunchIn");
                    // footerTextRef.setFooter("Employee Lunch-In Mode")
                    setfooterText("Employee Lunch-In Mode");
                    setShowOverlay(false);
                  }}
                />
              </>
            ) : null}
          </View>
          <View style={{ display: "flex", flexDirection: "row" }}>
            {deviceType == null
              ? (
                <StyleButton
                  text="Scan"
                  color1="#093c75"
                  color2="#0d2458"
                  imageSource={require("../assets/scanicon.png")}
                  onPress={() => {
                    setScanType("none");
                    setShowOverlay(false);
                    // footerTextRef.setFooter("Scan Mode")
                    setfooterText("Scan Mode");
                  }}
                />
              )
              : null}
          </View>
          {/* <View style={{ display: "flex", flexDirection: "row" }}>
            <StyleButton
              disabled
              text="View Doorbell"
              color1="#093c75"
              color2="#0d2458"
              imageSource={require("../assets/viewdoorbell.png")}
              onPress={() => {
                console.log("navigating to doorbell");
                // RootNavigation.navigate("Doorbell");
                // navigation.navigate("Doorbell");
              }}
            />
          </View> */}
        </View>
      </ImageBackground>
      <View style={{
        display: "flex", position: "absolute", top: 0, right: 0, opacity: 0,
      }}
      >
        <FloatingButton link="Settings" />
      </View>
    </View>
  );
}

function StyleButton({
  color1 = "#777",
  color2 = "#777",
  text = "TEXT_BUTTON",
  imageSource = require("../assets/checkout.png"),
  onPress = () => null,
  disabled = false,
}) {
  return (
    <Pressable
      style={{
        borderRadius: 10,
        flex: 1,
        margin: 15,
      }}
      disabled={disabled}
      onPress={onPress}
    >
      <LinearGradient
        colors={[color1, color2]}
        style={{
          padding: 17,
          borderRadius: 10,
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
        }}
      >
        <Image style={{ height: 20, width: 20 }} source={imageSource} />
        <Text
          style={{
            color: "#FFF",
            fontSize: 20,
            textAlign: "center",
            flex: 1,
            fontWeight: "600",
            // TODO: ADD POPPINS FONT8
          }}
        >
          {text}
        </Text>
      </LinearGradient>
    </Pressable>
  );
}

export default CameraFooterOverlay;

// This is just for QOL dev purposes
const CONSOLE = {
  Reset: "\x1b[0m",
  Bright: "\x1b[1m",
  Dim: "\x1b[2m",
  Underscore: "\x1b[4m",
  Blink: "\x1b[5m",
  Reverse: "\x1b[7m",
  Hidden: "\x1b[8m",

  FgBlack: "\x1b[30m",
  FgRed: "\x1b[31m",
  FgGreen: "\x1b[32m",
  FgYellow: "\x1b[33m",
  FgBlue: "\x1b[34m",
  FgMagenta: "\x1b[35m",
  FgCyan: "\x1b[36m",
  FgWhite: "\x1b[37m",
  FgGray: "\x1b[90m",
};
