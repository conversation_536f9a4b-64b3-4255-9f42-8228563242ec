import React, { useState, useEffect } from "react";
import { Button, Dimensions, Modal, Text, TextInput, TextInputBase, TouchableOpacity } from "react-native";
import { TouchableHighlight, View } from "react-native";
import { useNavigation } from "@react-navigation/native";

export default function FloatingButton(props) {
  const { text = "Settings", icon, link, showBuffer } = props;
  const navigation = useNavigation();
  const [modalVisible, setModalVisible] = useState(false);
  const [passCode, onChangeText] = React.useState('');
  const [count, setCount] = useState(0)

  const checkPassword = () => {
    console.log(passCode);
    if (passCode === "133337") {
      onChangeText("")
      setModalVisible(false);
      navigation.navigate("Settings");
    } else {
      alert("invalid pass")
    }
  }

  useEffect(() => {
    console.log("OPENING LOG IN ", count)
    console.log(modalVisible)
    if (count === 5) {
      setModalVisible(true);
      setCount(0);
    } else {
      // setModalVisible(false)
    }
  }, [count, modalVisible])

  useEffect(() => {
    const interval = setInterval(() => {
      setCount(0);
    }, 5000);
    return () => clearInterval(interval);
  }, [])
  

  return (
    <View>
      <Modal
        animationType="slide"
        transparent={false}
        visible={modalVisible}
      >
        <View
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: Dimensions.get("screen").height,
          }}
        >
          <Text style={{ fontSize: 20 }}>Enter Access Code: </Text>
          <TextInput
            style={{
              display: 'flex', borderWidth: 1, width: "80%", padding: 10, marginTop: 10, borderColor: "#aaa", borderRadius: 5, textAlign: 'center',
              fontSize: 20
            }}
            onChangeText={onChangeText}
            value={passCode}
            secureTextEntry={true}
          />
          <View style={{ display: "flex", flexDirection: "row", justifyContent: 'flex-end', width: "80%" }}>
            <TouchableOpacity
              style={{
                padding: 10, width: 100, borderRadius: 5, alignItems: 'center',
                backgroundColor: "#"
              }}
              onPress={() => setModalVisible(false)}
            >
              <Text style={{ fontWeight: 'bold', color: "#777" }}>Go back</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={{
                padding: 10, width: 100, borderRadius: 5, alignItems: 'center'
              }}
              onPress={() => checkPassword()}
            >
              <Text style={{ fontWeight: 'bold', color: "#027bff" }}>Submit</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      <TouchableOpacity
        // onLongPress={() => setModalVisible(true)}
        onPress={() => setCount(count + 1)}
        style={{
          borderWidth: 1,
          padding: 10,
          borderRadius: 5,
          borderColor: "#CCC",
          height:150,
        }}
      >
        <Text style={{ fontSize: 18 }}> {text} </Text>
      </TouchableOpacity>
    </View >
  );
}
