{"expo": {"name": "quantumfacepass", "slug": "quantumfacepass", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.qinusaxia.quantumfacepass"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO"], "package": "com.qinusaxia.quantumfacepass"}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [], "extra": {"eas": {"projectId": "25103fd3-91c6-46f9-a727-83739a89b068"}}}}