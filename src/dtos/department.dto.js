const Joi = require('joi');

exports.createDepartment = Joi.object({
  premiseId: Joi.string().required(),
  departmentName: Joi.string().required(),
});

exports.getDepartments = Joi.object({
  premiseId: Joi.string().required(),
});

exports.getDepartmentByDeviceSerialNumber = Joi.object({
  apiKey: Joi.string().required(),
  serialNumber: Joi.string().required(),
});

exports.deleteDepartmentByPremiseId = Joi.object({
  premiseId: Joi.string().required(),
  departmentId: Joi.string().required(),
});
