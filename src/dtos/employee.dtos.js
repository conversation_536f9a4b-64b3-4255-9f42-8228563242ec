const Joi = require('joi');

exports.addEmployeeToPremise = Joi.object({
  uid: Joi.string().required(),
  premiseId: Joi.string().required(),
});

exports.createEmployee = Joi.object({
  fullName: Joi.string().required(),
  imageObj: Joi.object().required(),
  phoneNumber: Joi.string(),
  premiseId: Joi.string().required(),

  NRIC: Joi.string(),
  passportNumber: Joi.string(),
  gender: Joi.string(),
  role: Joi.string(), // 'visitor', 'employee', 'user'
  accessStartTime: Joi.date(), // only use if role is visitor
  accessEndTime: Joi.date(), // only use if role is visitor

  KWSP: Joi.string(),
  SOCSO: Joi.string(),
  religion: Joi.string(),
  birthDate: Joi.date(),
  race: Joi.string(),
  maritalStatus: Joi.string(),
  nationality: Joi.string(),
  designation: Joi.string(),
  department: Joi.string(),
  emergencyContact: Joi.object({
    name: Joi.string(),
    email: Joi.string().email(),
    phoneNumber: Joi.string(),
  }),
});
