const Joi = require('joi');

exports.createPremise = Joi.object({
  premiseLogoImage: Joi.object().required(),
  mainColor: Joi.string().required(),
  textColor: Joi.string().required(),
  name: Joi.string().required(),
  departments: Joi.array().items(Joi.object({
    departmentName: Joi.string().required(),
  }).required()).required(),
});

exports.addAdminToPremise = Joi.object({
  email: Joi.string().required(),
  premiseId: Joi.string().required(),
});

exports.addChildrenToPremise = Joi.object({
  premiseLogoImage: Joi.object().required(),
  mainColor: Joi.string().required(),
  textColor: Joi.string().required(),
  name: Joi.string().required(),
  departments: Joi.array().items(Joi.object({
    departmentName: Joi.string().required(),
  })).required(),
  parentPremiseId: Joi.string().required(),
});

exports.createPremiseExternal = Joi.object({
  premiseLogoImage: Joi.object().required(),
  mainColor: Joi.string().required(),
  textColor: Joi.string().required(),
  name: Joi.string().required(),
  departments: Joi.array().items(Joi.object({
    departmentName: Joi.string().required(),
  })).required(),
  apiKey: Joi.string().required(),
});

exports.updatePremiseExternal = Joi.object({
  premiseId: Joi.string().required(),
  premiseLogoImage: Joi.object(),
  mainColor: Joi.string(),
  textColor: Joi.string(),
  name: Joi.string().required(),
  apiKey: Joi.string().required(),
});

exports.updatePremise = Joi.object({
  premiseId: Joi.string().required(),
  premiseLogoImage: Joi.object(),
  mainColor: Joi.string().min(1),
  textColor: Joi.string().min(1),
});

exports.getPremiseByIdAndKey = Joi.object({
  premiseId: Joi.string().required(),
});

exports.updatePremiseCallbackURL = Joi.object({
  premiseId: Joi.string().required(),
  callbackUrl: Joi.string().allow(''),
});

exports.deletePremiseExternal = Joi.object({
  apiKey: Joi.string().required(),
  premiseId: Joi.string().required(),
});

exports.getAllPremisesByAPIKey = Joi.object({
  apiKey: Joi.string().required(),
  q: Joi.string(),
  limit: Joi.number(),
  page: Joi.number(),
  showDeleted: Joi.boolean(),
});

exports.getPremiseExternalByPremiseId = Joi.object({
  apiKey: Joi.string().required(),
  premiseId: Joi.string().required(),
});
