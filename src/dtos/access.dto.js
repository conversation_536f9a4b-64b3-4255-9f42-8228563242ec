const Joi = require('joi');

exports.getAllAccessRecords = Joi.object({
  premiseId: Joi.string().required(),
  startDate: Joi.date().iso(),
  endDate: Joi.date().iso(),
  page: Joi.number(),
  limit: Joi.number(),
  searchText: Joi.string().allow(''),
});
exports.getTodayVisitorRecords = Joi.object({
  premiseId: Joi.string().required(),

});

exports.generateExcelAttendanceReport = Joi.object({
  premiseId: Joi.string().required(),
  startDate: Joi.date().iso().required(),
  endDate: Joi.date().iso().required(),
  userList: Joi.array().items(Joi.string()),
  hideEmpty: Joi.boolean(),
});

exports.exportAccessRecordExternal = Joi.object({
  premiseId: Joi.string().required(),
  startDate: Joi.date().iso().required(),
  endDate: Joi.date().iso().required(),
});

exports.assignUserToDepartments = Joi.object({
  premiseId: Joi.string().required(),
  departments: Joi.array().items(Joi.object({
    departmentName: Joi.string().required(),
    departmentId: Joi.string().required(),
  })).required(),
  userId: Joi.string().required(),
});

exports.createNewAccess = Joi.object({
  userId: Joi.string().required(),
  premiseIdToQuery: Joi.string().required(),
});

exports.createNewAccessByBody = Joi.object({
  userId: Joi.string().required(),
  premiseId: Joi.string().required(),
  access: Joi.string().valid('grant', 'revoke').required(),
});

exports.restoreAccess = Joi.object({
  userId: Joi.string().required(),
});

exports.getAcessRecordbyPremise = Joi.object({
  premiseIdToQuery: Joi.string().required(),
  role: Joi.string(),
  startDate: Joi.date().iso(),
  endDate: Joi.date().iso(),
});

exports.getAccessRecordsByUser = Joi.object({
  startTime: Joi.date().iso().required(),
  endTime: Joi.date().iso().required(),
  premiseId: Joi.string().required(),
  userId: Joi.array().items(Joi.string().required()),
  page: Joi.number(),
  limit: Joi.number(),
});

exports.bulkExternalAccess = Joi.object({
  premiseId: Joi.string().required(),
  apiKey: Joi.string().required(),
  userIds: Joi.array().items(Joi.string().required()).required(),
  status: Joi.string().valid('grant', 'remove').required(),
});

exports.searchUsersAccessByPremise = Joi.object({
  premiseId: Joi.string().required(),
  q: Joi.string(),
  limit: Joi.number(),
  page: Joi.number(),
});

exports.searchPremisesBelongToUser = Joi.object({
  userId: Joi.string().required(),
  q: Joi.string(),
  limit: Joi.number(),
  page: Joi.number(),
});
