const Joi = require('joi');

exports.getAttendanceRecords = Joi.object({
  startTime: Joi.date().iso().required(),
  endTime: Joi.date().iso().required(),
  premiseId: Joi.string().required(),
  userId: Joi.string(),
});

exports.getAttendanceRates = Joi.object({
  premiseId: Joi.string().required(),
});

exports.getScanStatistic = Joi.object({
  startTime: Joi.date().iso().required(),
  endTime: Joi.date().iso().required(),
  premiseId: Joi.string().required(),
});
