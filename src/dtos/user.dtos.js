const Joi = require('joi');

exports.createUser = Joi.object({
  fullName: Joi.string().required(),
  imageObj: Joi.object().required(),
  phoneNumber: Joi.string(),
  premiseId: Joi.string().required(),

  NRIC: Joi.string(),
  passportNumber: Joi.string(),
  gender: Joi.string(),
  role: Joi.string(), // 'visitor', 'employee', 'user'
  accessStartTime: Joi.date(), // only use if role is visitor
  accessEndTime: Joi.date(), // only use if role is visitor

});

exports.createUserInCustomPremiseWithKey = Joi.object({
  fullName: Joi.string().required(),
  imageObj: Joi.object().required(),
  phoneNumber: Joi.string(),
  premiseId: Joi.string().required(),

  NRIC: Joi.string(),
  passportNumber: Joi.string(),
  gender: Joi.string(),
  role: Joi.string(), // 'visitor', 'employee', 'user'
  accessStartTime: Joi.date(), // only use if role is visitor
  accessEndTime: Joi.date(), // only use if role is visitor

});

exports.createUserWithoutPremise = Joi.object({
  fullName: Joi.string().required(),
  imageObj: Joi.object().required(),
  phoneNumber: Joi.string(),
  premiseId: Joi.string(),

  NRIC: Joi.string(),
  passportNumber: Joi.string(),
  gender: Joi.string(),
  role: Joi.string(), // 'visitor', 'employee', 'user'
  accessStartTime: Joi.date(), // only use if role is visitor
  accessEndTime: Joi.date(), // only use if role is visitor

});

exports.createUserInPremise = Joi.object({
  fullName: Joi.string().required(),
  imageObj: Joi.object().required(),
  phoneNumber: Joi.string(),
  premiseToAdd: Joi.string().required(),
  foundKey: Joi.object().required(),
});

exports.fetchList = Joi.object({
  uid: Joi.string().required(),
  premiseId: Joi.string(),
  fetchType: Joi.string(),
});

exports.fetchExternalUserList = Joi.object({
  premiseId: Joi.string().required(),
  fetchType: Joi.string(),
  premiseIds: Joi.array().items(Joi.string()),
  limit: Joi.number(),
  page: Joi.number(),
});

exports.fetchUserListByParams = Joi.object({
  adminPremiseId: Joi.string().required(),
  premiseIds: Joi.array().items(Joi.string()),
  fullName: Joi.string(),
  showDeleted: Joi.boolean(),
  limit: Joi.number(),
  page: Joi.number(),
});

exports.fetchUserListByPremiseId = Joi.object({
  premiseId: Joi.string().required(),
  page: Joi.number().required(),
  limit: Joi.number().required(),
  searchText: Joi.string().allow(null, ''),
});
exports.fetchUserNumber = Joi.object({
  premiseId: Joi.string().required(),

});

exports.fetchVisitorListByPremiseId = Joi.object({
  premiseId: Joi.string().required(),
  page: Joi.number().required(),
  limit: Joi.number().required(),
  searchText: Joi.string().allow(null, ''),
});

exports.fetchEmployeeListByPremiseId = Joi.object({
  premiseId: Joi.string().required(),
  page: Joi.number().required(),
  limit: Joi.number().required(),
  searchText: Joi.string().allow(null, ''),
});
exports.exportUserListByPremiseId = Joi.object({
  premiseId: Joi.string().required(),
  role: Joi.string(),
  idArray: Joi.array().items(Joi.string().required()),
});

exports.updateUser = Joi.object({
  faceImageUrl: Joi.string(),
  fullName: Joi.string().required(),
  phoneNumber: Joi.number().positive().allow(0),
  userId: Joi.string().required(),
  premiseId: Joi.string().required(),
  NRIC: Joi.string(),
  passportNumber: Joi.string(),
  gender: Joi.string(),
  role: Joi.string(),
  accessStartTime: Joi.date(),
  accessEndTime: Joi.date(),
});

exports.updateUserWithoutPremise = Joi.object({
  faceImageUrl: Joi.string(),
  fullName: Joi.string().required(),
  phoneNumber: Joi.number().positive().allow(0),
  userId: Joi.string().required(),
  NRIC: Joi.string(),
  passportNumber: Joi.string(),
  gender: Joi.string(),
  role: Joi.string(),
  accessStartTime: Joi.date(),
  accessEndTime: Joi.date(),
});

exports.deleteUser = Joi.object({
  userId: Joi.string().required(),
  premiseId: Joi.string().required(),
});

exports.fetchUser = Joi.object({
  userId: Joi.string().required(),
  premiseId: Joi.string().required(),
  showNoPremiseUser: Joi.boolean(),
});

exports.fetchUsersOfPremise = Joi.object({
  premiseId: Joi.string().required(),
});

exports.deleteUserByExternal = Joi.object({
  userId: Joi.string().required(),
});

// exports.updateUserWithFace = Joi.object({
//   imageObj: Joi.object().required(),
//   fullName: Joi.string().required(),
//   phoneNumber: Joi.number().positive().allow(0),
//   userId: Joi.string().required(),
//   premiseId: Joi.string().required(),
// });
