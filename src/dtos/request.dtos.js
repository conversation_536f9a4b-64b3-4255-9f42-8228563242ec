const Joi = require('joi');

exports.createRequest = Joi.object({
  id: Joi.string().required(),
  type: Joi.string().required(),
  premise: Joi.string().required(),
  remark: Joi.string().required(),
  isLeave: Joi.boolean().required(),
  paidLeave: Joi.boolean().required(),
  startDate: Joi.date().required(),
  endDate: Joi.date().required(),
  user: Joi.string().required(),
});

exports.updateRequest = Joi.object({
  id: Joi.string().required(),
  type: Joi.string(),
  premise: Joi.string(),
  remark: Joi.string(),
  isLeave: Joi.boolean(),
  paidLeave: Joi.boolean(),
  startDate: Joi.date(),
  endDate: Joi.date(),
  user: Joi.string(),
});

exports.updateRequestStatus = Joi.object({
  id: Joi.string().required(),
  status: Joi.string().required(),
});

exports.deleteRequest = Joi.object({
  id: Joi.string().required(),
});

exports.getAllRequests = Joi.object({
  premiseId: Joi.string().required(),
});

exports.getRequestById = Joi.object({
  id: Joi.string().required(),
});
