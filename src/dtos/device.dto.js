const Joi = require('joi');

exports.getAllDevices = Joi.object({
  premiseId: Joi.string().required(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).default(10),
});

exports.getDeviceUserCount = Joi.object({
  serialNumber: Joi.string().required(),
});

exports.updateDevice = Joi.object({
  serialNumber: Joi.string().required(),
  premiseId: Joi.string().required(),
  name: Joi.string(),
  departmentId: Joi.string(),
});

exports.deleteDevice = Joi.object({
  serialNumber: Joi.string().required(),
  premiseId: Joi.string().required(),
});

exports.remoteControlDevice = Joi.object({
  serialNumber: Joi.string().required(),
  premiseId: Joi.string().required(),
  durationOpen: Joi.number().positive(),
});
