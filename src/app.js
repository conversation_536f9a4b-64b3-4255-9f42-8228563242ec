/* eslint-disable import/no-unresolved */
/* eslint-disable import/no-extraneous-dependencies */
const tracker = require('@middleware.io/node-apm');

tracker.track({
  serviceName: 'facial-recognition',
  accessToken: 'rhsayehwbrwnazlfxsmfohtpzlezmxhkvins',
  target: 'https://kmavo.middleware.io',
});
const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const _ = require('lodash');

const app = express();

const http = require('http').createServer(app);
const { Server } = require('socket.io');
const { runRedis } = require('#config/redis');

const io = new Server(http);

const { port } = require('#config/env');
const resModifier = require('#middlewares/resModifier');
const jsonParser = require('#middlewares/jsonParser');
const accessLogger = require('#middlewares/accessLogger');

const routes = require('#controllers/routes');
const errorHandler = require('#middlewares/errorHandler');
const route404Handler = require('#middlewares/route404Handler');
const bodyParser = require('body-parser');
const { WebSocketServer } = require('ws');
const {
  wsSwitch,
  sendToDevice,
} = require('#config/websocket');
const { XMLParser } = require('fast-xml-parser');
const Facepass = require('./classes/io.class');

// init mongodb
require('#config/mongodb');

const facePassServer = new Facepass(io);

// for ebkn device.
const wss = new WebSocketServer({ noServer: true });
// runRedis(wss);

http.on('upgrade', (request, socket, head) => {
  if (request.url === '/ws') {
    wss.handleUpgrade(request, socket, head, (ws) => {
      wss.emit('connection', ws, request);
      ws.on('message', (msg) => {
        console.log('message from client:', msg.toString());
      });
    });
  } else {
    // socket.destroy();
  }
});

// wss.on('connection', (ws) => {
//   let msg = '';
//   ws.on('message', async (data) => {
//     const parser = new XMLParser();
//     const jObj = parser.parse(data);
//
//     msg = jObj.Message;
//
//     if (!_.isEmpty(msg)) {
//       wsSwitch(msg, ws, 'client');
//     }
//   });
//
//   ws.on('error', (err) => {
//     console.error(err);
//   });
// });

// apis
const expressFunction = (customAPIs) => {
  // Security Middlware
  app.use(cors());
  app.use(helmet());

  /* Modify res param for custom functions */
  app.use('/*', resModifier);

  app.get('/', async (req, res) => res.send('ok'));

  app.use(bodyParser.urlencoded({ extended: false, limit: '5mb' }));
  app.use(bodyParser.json());

  // JSON parser
  app.use(jsonParser);

  // access logger
  app.use('/*', accessLogger);

  // Routes init
  app.use('/api', routes(facePassServer));
  // if (process.env.MODE !== 'test') {
  //   app.use('/api', customAPIs);
  // }

  // error handler
  app.use(errorHandler);

  // 404 route handler
  app.use('*', route404Handler);

  http.listen(port, () => {
    console.log(`PORT ${port} is listening`);
  }).on('error', (err) => {
    console.log(err);
    process.exit(1);
  });
};

expressFunction();
module.exports = expressFunction;
