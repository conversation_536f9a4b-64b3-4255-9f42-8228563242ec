const _ = require('lodash');
const moment = require('moment');
const { auth } = require('#config/firebase');
const User = require('#models/User.model');
const responses = require('#helpers/responses');
const { OAuth2Client, GoogleAuth, AuthClient } = require('google-auth-library');
const AdminModel = require('#models/Admin.model');
const { authMode } = require('#config/env');

// for postman ONLY!
const CLIENT_ID = '353589176934-ti6pjqlf8at7tq8h090l0nkg2s6mrpl5.apps.googleusercontent.com';
const client = new OAuth2Client();

const requireAuth = (...roles) => async (req, res, next) => {
  const bearerHeader = req.headers.authorization;

  console.log(bearerHeader);

  try {
    if (typeof bearerHeader === 'undefined') {
      return res.status(401).send(
        responses.failure('Unauthenticated', null, 401),
      );
    }

    let firebaseUser = null;
    if (authMode === 'postman') {
      // google identity
      firebaseUser = await client.verifyIdToken({
        idToken: bearerHeader,
      });
      req.uid = await firebaseUser.payload.sub;
    } else {
      // google auth
      firebaseUser = await auth.verifyIdToken(bearerHeader);
      req.uid = await firebaseUser.uid;
    }

    if (!req.uid) {
      return res.status(401).send(
        responses.failure('UID not found', null, 401),
      );
    }

    req.user = await AdminModel.findOne({
      uid: req.uid,
    });

    if (!_.includes(roles, req.user.type)) {
      return res.send(responses.failure('Unauthorized', {}, 403));
    }

    if (!req.user) {
      return res.status(401).send(
        responses.failure('User data not found', { register: true }, 401),
      );
    }

    const clonedUser = _.cloneDeep(req.user);

    req.user = {
      ...clonedUser.toJSON(),
      id: clonedUser._id.toString(),
      countryCode: clonedUser.countryCode,
      phoneNumber: clonedUser.phoneNumber,
    };
  } catch (e) {
    console.log('ERROR', e);
    return res.status(500).send(responses.exception(e.message, e.code));
  }

  return next();
};

module.exports = requireAuth;
