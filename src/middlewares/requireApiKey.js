const responses = require('#helpers/responses');
const AdminApikeyModel = require('#models/AdminApikey.model');
const _ = require('lodash');

const requireApiKey = (...roles) => async (req, res, next) => {
  const { authorization } = req.headers;
  const { body, user } = req;

  try {
    const foundApiKey = await AdminApikeyModel.findOne({ apiKey: authorization });
    console.log('authorization', foundApiKey);
    if (_.isEmpty(foundApiKey)) {
      const err = responses.failure('Unrecognized key');
      err.data = { content: 'Please make sure the API key is valid.' };
      return next(err);
    }

    // console.log('BODY', body);

    const getPremiseId = _.get(foundApiKey, 'premises[0].premiseId').toString();
    req.query.premiseId = getPremiseId;
    req.body.premiseId = getPremiseId;
    req.params.premiseId = getPremiseId;

    // console.log('foundApiKey here:', foundApiKey);
    // console.log('getPremiseId', getPremiseId);
    // console.log('req.query', req.query);
  } catch (e) {
    console.log('ERROR', e);

    return res.status(500).send(responses.exception(e.message, e.code));
  }

  return next();
  // return next(new Error('Finished at here'));
};

module.exports = requireApiKey;
