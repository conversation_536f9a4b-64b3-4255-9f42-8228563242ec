const admin = require('firebase-admin');

const db = admin.firestore();

exports.checkAdminPermission = async (adminId) => {
  let returnDoc = {};
  await db.collection('admins').doc(adminId).get()
    .then((doc) => {
      if (!doc.exists) {
        throw 'you do not have permission';
      }
      returnDoc = doc.data();
    });
  return returnDoc;
};

exports.changeAdminPassword = async (uid, newPass) => {
  console.log(uid);
  console.log(newPass);
  await admin.auth().updateUser(uid, {
    password: newPass,
  }).then((userRecord) => {
    // See the UserRecord reference doc for the contents of userRecord.
    console.log('Successfully updated user', userRecord.toJSON());
  }).catch((error) => {
    console.log('Error updating user:', error);
  });
  return null;
};

const _ = require('lodash');

exports.convertMapToString = (map) => {
  let stringValues = '';
  if (_.isEmpty(map)) {
    // return 'none'
  }
  _.forOwn(map, (value, key) => {
    const department = _.split(value, '/')[1];
    stringValues += ` ${department},`;
  });
  if (stringValues.slice(stringValues.length - 1) === ',') {
    stringValues = stringValues.slice(0, -1);
  }
  return stringValues;
};

const ExcelJS = require('exceljs');
const moment = require('moment-timezone');

exports.exportEmpInfoToExcel = (empList, premiseInfo) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Employee_name', {
    pageSetup: { paperSize: 9, orientation: 'landscape' },
  });

  const parsedDepartments = [];
  _.forEach(empList, (empInfo) => {
    const cloneInfo = _.cloneDeep(empInfo);
    !cloneInfo.icNo ? _.set(cloneInfo, 'icNo', '-nil') : null;
    !cloneInfo.phoneNumber ? _.set(cloneInfo, 'phoneNumber', '-nil') : null;
    const departmentInfo = convertMapToString(cloneInfo.departments);
    _.set(cloneInfo, 'departments', departmentInfo);
    parsedDepartments.push(cloneInfo);
  });

  /* Column headers */
  worksheet.getRow(5).values = [
    '',
    'Full Name',
    'Ic No',
    'Department',
    'Designation',
    'Phone Number',
    // 'Other names',
    // 'Photo'
  ];

  worksheet.columns = [
    { key: 'index', width: 5 },
    { key: 'fullName', width: 30 },
    { key: 'icNo', width: 15 },
    { key: 'departments', width: 20 },
    { key: 'designation', width: 20 },
    { key: 'phoneNumber', width: 16 },
    // { key: 'adminDefinedName', width: 15, },
    // { key: 'photoUrl', width: 20, },
  ];

  _.forEach(parsedDepartments, (row, index) => {
    _.set(row, 'index', index + 1);
    worksheet.addRow(row);
    // console.log(worksheet.getRow())
  });

  worksheet.eachRow((row, number) => {
    row.eachCell((cell, colNumber) => {
      cell.alignment = { vertical: 'middle' };
      if (number > 4) {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      }
    });
  });

  return workbook;
};

exports.oneEmployeeScansToExcel = (scans, userInfo) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Employee_name', {
    pageSetup: { paperSize: 9, orientation: 'landscape' },
  });

  _.map(scans, (scan, idx) => {
    const unixScanTime = _.get(scan, 'createdAt._seconds');
    const timeMoment = moment.unix(unixScanTime);
    // _.set(scan, 'index', idx+1);
    _.set(scan, 'day', moment(timeMoment).format('DD MMMM YYYY'));
    _.set(scan, 'time', moment(timeMoment).format('hh:mm A'));
  });

  /* Column headers */
  worksheet.getRow(4).values = [
    // '',
    'Day',
    'Time',
    'Type',
    'Premise',
    'Device',
  ];

  worksheet.columns = [
    // { key: 'index', width: 5 },
    { key: 'day', width: 15 },
    { key: 'time', width: 15 },
    { key: 'checkType', width: 15 },
    { key: 'premiseId' },
    { key: 'serialNumber', width: 15 },
    // { key: 'adminDefinedName', width: 15, },
    // { key: 'photoUrl', width: 20, },
  ];

  worksheet.getCell('A1').value = 'Fullname';
  worksheet.getCell('B1').value = _.get(userInfo, 'fullName', '');
  worksheet.getCell('A2').value = 'User Id';
  worksheet.getCell('B2').value = _.get(userInfo, 'id', '');

  _.forEach(scans, (data, index) => {
    worksheet.addRow(data);
  });

  worksheet.eachRow((row, number) => {
    row.eachCell((cell, colNumber) => {
      cell.alignment = { vertical: 'middle' };
      if (number > 4) {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      }
    });
  });

  return workbook;
};

exports.getAllPremiseDepartments = async (premiseList) => {
  const allPremiseDepartment = [];
  const departmentRef = db.collection('departments');
  const snapshot = await departmentRef.where('premise', 'in', premiseList).get();
  if (snapshot.empty) {
    console.log('No matching documents.');
    return;
  }
  snapshot.forEach((doc) => {
    const cloneDoc = _.set(doc.data(), 'docID', doc.id);
    allPremiseDepartment.push(cloneDoc);
  });
  return allPremiseDepartment;
};
