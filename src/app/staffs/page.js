"use client";

import DefaultLayout from "../../components/Layouts/DefaultLayout";
import { clsx } from "clsx";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { fetch } from "../../helpers";
import _ from "lodash";
import moment from "moment/moment";

const StaffsPage = () => {
  const [staffs, setStaffs] = useState([]);

  useEffect(() => {
    fetch("/users")
      .then((res) => {
        console.log("staff result", res);
        setStaffs(res.data);
      })
      .catch((err) => {
        console.log("error when getting sales", err);
      });
  }, []);

  return (
    <DefaultLayout>
      <div className={clsx("font-semibold", "capitalize", "text-2xl")}>
        My staffs page
      </div>
      <AddStaffButton />
      {_.isEmpty(staffs) ? (
        <div>No staff yet.</div>
      ) : (
        <StaffsTable staffs={staffs} />
      )}
    </DefaultLayout>
  );
};

const AddStaffButton = () => {
  const router = useRouter();

  return (
    <div className={clsx("flex", "justify-end")}>
      <button
        onClick={() => router.push("/staffs/add")}
        className={clsx(
          "bg-primary",
          "text-white",
          "rounded-md",
          "px-6",
          "py-2",
          "mt-1",
        )}
      >
        Add Staff
      </button>
    </div>
  );
};

const StaffsTable = ({ staffs }) => {
  return (
    <div className="overflow-x-auto">
      <table className="table mt-8 bg-white">
        {/* head */}
        <thead>
          <tr>
            <th>
              <label>
                <input type="checkbox" className="checkbox" />
              </label>
            </th>
            <th>Name</th>
            <th>Email</th>
            <th>Phone Number</th>
            <th>Roles</th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          {_.map(staffs, (staff) => (
            <tr>
              <th>
                <label>
                  <input type="checkbox" className="checkbox" />
                </label>
              </th>
              <td>
                <div className="flex items-center gap-3">
                  <div>
                    <div className="font-bold">{staff.name}</div>
                    <div className="text-sm opacity-50">VB Motor</div>
                  </div>
                </div>
              </td>
              <td>{staff.email}</td>
              <td className={"capitalize"}>{staff.phoneNumber}</td>
              <td className={"capitalize"}>{_.join(staff?.roles, ", ")}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default StaffsPage;
