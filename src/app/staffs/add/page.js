"use client";

import DefaultLayout from "../../../components/Layouts/DefaultLayout";
import BackButton from "../../../components/BackButton/BackButton";
import { clsx } from "clsx";
import _ from "lodash";
import { fetch } from "../../../helpers";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

const AddStaffPage = () => {
  const [staffObj, setStaffObj] = useState({ role: "admin" });
  const [showAlert, setShowAlert] = useState(false);
  const router = useRouter();

  const addStaff = async () => {
    console.log("staff object:", staffObj);
    await fetch("/user", "post", staffObj)
      .then(async (res) => {
        console.log("staff created:", await res);
        setShowAlert(true);
        setTimeout(() => {
          setShowAlert(false);
          router.back();
        }, 5000);
      })
      .catch((err) => {
        console.log("error when adding staff:", err);
      });
  };

  // monitor staff object
  useEffect(() => {
    console.log("staff object:", staffObj);
  }, [staffObj]);

  return (
    <DefaultLayout>
      <BackButton />
      <div className={clsx("font-semibold", "capitalize", "text-2xl", "mt-4")}>
        Add Staff
      </div>
      {_.map(
        [
          {
            name: "Name",
            field: "name",
          },
          {
            name: "Email",
            field: "email",
          },
          {
            name: "Phone Number",
            field: "phoneNumber",
          },
        ],
        (inputField) => (
          <label className="form-control mt-4 w-full">
            <div className="label">
              <span className="label-text">{inputField?.name}:</span>
            </div>
            <input
              onChange={(e) => {
                const cloned = _.cloneDeep(staffObj);
                cloned[inputField?.field] = e.target.value;
                setStaffObj(cloned);
              }}
              // value={inputField.field}
              type="text"
              placeholder="Type here"
              className="input input-bordered w-full"
            />
          </label>
        ),
      )}
      <label className="form-control mt-4 w-full">
        <div className="label">
          <span className="label-text">Role:</span>
        </div>
        <select
          onChange={(e) => {
            const cloned = _.cloneDeep(staffObj);
            cloned["role"] = e.target.value;
            setStaffObj(cloned);
          }}
          className="select select-bordered w-full"
        >
          {_.map(
            [
              {
                name: "Admin",
                role: "admin",
              },
              {
                name: "Salesman",
                role: "salesman",
              },
              {
                name: "Serve",
                role: "serve",
              },
              {
                name: "Installer",
                role: "installer",
              },
            ],
            (role) => (
              <option value={role?.role} className={clsx("max-w-1")}>
                {role?.name}
              </option>
            ),
          )}
        </select>
      </label>
      <AddStaffButton onClick={addStaff} />
      {showAlert && <Alert text={"Your order has been confirmed!"} />}
    </DefaultLayout>
  );
};

const AddStaffButton = ({ onClick }) => {
  return (
    <div className={"flex w-full justify-end"}>
      <button
        onClick={onClick}
        className={clsx(
          "btn",
          "bg-primary",
          "text-white",
          "px-6",
          "py-2",
          "mt-4",
        )}
      >
        Add Staff
      </button>
    </div>
  );
};

const Alert = () => {
  return (
    <div
      role="alert"
      className="alert alert-success fixed left-80 right-10 top-30 z-9 mt-4 max-w-sm"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-6 w-6 shrink-0 stroke-current"
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
      <span>Your staff has been created!</span>
    </div>
  );
};

export default AddStaffPage;
