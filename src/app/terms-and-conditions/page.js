'use client'

import React from 'react';
import TermsAndConditions from '../../components/TermsAndConditions';
import DefaultLayout from "../../components/Layouts/DefaultLayout";
// import { Metadata } from "next";

// export const metadata = {
// 	title: "Terms and Conditions - VB Motor",
// 	description: "Terms and Conditions for VB Motor Sales Management System",
// };

const TermsAndConditionsPage = () => {
	return (
		<>
			<DefaultLayout>
				<div className="grid grid-cols-1 gap-4 md:gap-6 2xl:gap-7.5">
					<div className="rounded-sm border border-stroke bg-white px-5 py-6 shadow-default dark:border-strokedark dark:bg-boxdark">
						<h2 className="text-title-md2 font-semibold text-black dark:text-white mb-4">Terms and Conditions</h2>
						<TermsAndConditions />
					</div>
				</div>
			</DefaultLayout>
		</>
	);
};

export default TermsAndConditionsPage;