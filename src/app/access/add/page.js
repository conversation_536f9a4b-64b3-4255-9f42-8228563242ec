"use client";

import DefaultLayout from "../../../components/Layouts/DefaultLayout";
import Input from "../../../components/Input/Input";
import { clsx } from "clsx";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { fetch } from "../../../helpers";
import Alert from "../../../components/Alert/Alert";

const AddUserPage = () => {
  const [userObj, setUserObj] = useState({
    roles: [],
  });
  const [showAlert, setShowAlert] = useState(false);

  // monitor data
  useEffect(() => {
    console.log("user object:", userObj);
  }, [userObj]);

  const addUser = async () => {
    await fetch("/user", "post", userObj)
      .then(async (res) => {
        setShowAlert(true);
        const data = await res.data;
        console.log("user created:", data);
        setTimeout(() => {
          setShowAlert(false);
        }, 5000);
      })
      .catch((err) => {
        console.log("error when creating user:", err);
      });
  };

  const addRemoveRoles = (role, value) => {
    const clone = _.cloneDeep(userObj?.roles);

    if (value) {
      clone.push(role);
    } else {
      _.pull(clone, role);
    }

    setUserObj((prev) => ({
      ...prev,
      roles: clone,
    }));
  };

  return (
    <DefaultLayout>
      <div className={clsx("font-semibold", "capitalize", "text-2xl")}>
        Add User
      </div>
      <div className={"mt-4 rounded-lg bg-white p-4"}>
        <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
          <div className={"w-full sm:m-2"}>
            <Input
              returnedValue={(value) => {
                setUserObj((prev) => ({
                  ...prev,
                  name: value,
                }));
              }}
              inputField={{
                name: "Name",
                value: userObj?.name,
              }}
            />
          </div>
          <div className={"w-full sm:m-2"}>
            <Input
              returnedValue={(value) => {
                setUserObj((prev) => ({
                  ...prev,
                  email: value,
                }));
              }}
              inputField={{
                name: "Email",
                value: userObj?.email,
              }}
            />
          </div>
        </div>
        <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
          <div className={"w-full sm:m-2"}>
            <Input
              returnedValue={(value) => {
                setUserObj((prev) => ({
                  ...prev,
                  phoneNumber: value,
                }));
              }}
              value={userObj?.phoneNumber}
              inputField={{
                name: "Phone Number",
                type: "number",
              }}
            />
          </div>
          <div className={"w-full sm:m-2"}>
            <Input
              returnedValue={(value) => {
                setUserObj((prev) => ({
                  ...prev,
                  password: value,
                }));
              }}
              inputField={{
                name: "Password",
                value: userObj?.password,
              }}
            />
          </div>
        </div>
        {/* roles */}
        <div className={clsx("sm:m-2")}>
          <div className="label">
            <span className="label-text">Roles:</span>
          </div>
          <div className={clsx("flex")}>
            <Checkbox
              returnedValue={(value) => {
                addRemoveRoles("admin", value);
              }}
              value={_.includes(userObj?.roles, "admin")}
              text={"Admin"}
            />{" "}
            <Checkbox
              returnedValue={(value) => {
                addRemoveRoles("salesman", value);
              }}
              value={_.includes(userObj?.roles, "salesman")}
              text={"Salesman"}
            />{" "}
            <Checkbox
              returnedValue={(value) => {
                addRemoveRoles("installer", value);
              }}
              value={_.includes(userObj?.roles, "installer")}
              text={"Installer"}
            />
          </div>
        </div>
      </div>
      <AddUserButton
        onClick={() => {
          addUser();
        }}
      />
      {showAlert && <Alert text={"Your user has been created!"} />}
    </DefaultLayout>
  );
};

const Checkbox = ({ returnedValue, value, text }) => {
  return (
    <div className={clsx("flex", "mr-4")}>
      <input
        onChange={(e) => {
          returnedValue(e.target.checked);
        }}
        type="checkbox"
        checked={value}
        className="checkbox"
      />
      <div className={"ml-2"}>{text}</div>
    </div>
  );
};

const AddUserButton = ({ onClick }) => {
  const router = useRouter();

  return (
    <div className={clsx("flex", "justify-end", "mt-4")}>
      <button
        onClick={onClick}
        className={clsx(
          "bg-primary",
          "text-white",
          "rounded-md",
          "px-6",
          "py-2",
          "mt-1",
          "w-40",
        )}
      >
        Add
      </button>
    </div>
  );
};

export default AddUserPage;
