"use client";

import react, { useEffect, useState } from "react";
import DefaultLayout from "../../components/Layouts/DefaultLayout";
import { fetch } from "../../helpers";
import _ from "lodash";
import { useRouter } from "next/navigation";
import { clsx } from "clsx";

const StaffAccessPage = () => {
  return (
    <DefaultLayout>
      <AddUserButton />
      <Table />
    </DefaultLayout>
  );
};

const Table = () => {
  const [users, setUsers] = useState([]);

  useEffect(() => {
    getUsers();
  }, []);

  const getUsers = async () => {
    await fetch("/users")
      .then(async (res) => {
        const data = await res.data;
        console.log("users:", data);
        setUsers(data);
      })
      .catch((err) => {
        console.log("error when getting users:", err);
      });
  };

  const getRoles = (roles) => {
    const userMap = {
      "platform-admin": "Platform Admin",
      admin: "Admin",
      salesman: "Salesman",
      serve: "Serve",
      installer: "Installer",
      user: "User",
    };

    // to prettify the displayed text, then join them together if more than one.
    const formatted = _.map(roles, (r) => userMap[r]);
    return _.join(formatted, ",");
  };

  return (
    <table className={"table mt-4 bg-white"}>
      {/*head*/}
      <thead>
        <tr>
          <th>Name</th>
          <th>Email</th>
          <th>Phone Number</th>
          <th>Role</th>
          {/*<th>Actions</th>*/}
        </tr>
      </thead>
      <tbody>
        {_.map(users, (user) => (
          <tr>
            <td className={"capitalize"}>
              {!_.isEmpty(user?.name) ? user.name : "N/A"}
            </td>
            <td>{!_.isEmpty(user?.email) ? user.email : "N/A"}</td>
            <td>{!_.isEmpty(user?.phoneNumber) ? user.phoneNumber : "N/A"}</td>
            <td>{getRoles(user?.roles)}</td>
          </tr>
        ))}
      </tbody>
    </table>
  );
};

const AddUserButton = () => {
  const router = useRouter();

  return (
    <div className={clsx("flex", "justify-end")}>
      <button
        onClick={() => router.push("/access/add")}
        className={clsx(
          "bg-primary",
          "text-white",
          "rounded-md",
          "px-6",
          "py-2",
          "mt-1",
        )}
      >
        Add User
      </button>
    </div>
  );
};

export default StaffAccessPage;
