'use client'

import { IoEyeOutline } from "react-icons/io5";

import { useEffect, useState } from "react";
import DefaultLayout from "../../../components/Layouts/DefaultLayout"; // Assuming you have a SalesTable component
import clsx from "clsx";
import { fetch } from "../../../helpers";
import moment from 'moment';
import _ from "lodash"; // Importing the missing library

const CheckedOrdersPage = () => {
	const [checkedOrders, setCheckedOrders] = useState([]);

	useEffect(() => {
		fetch("/orders/by/installer-checker")
			.then((res) => {
				console.log("checked orders result", res);
				setCheckedOrders(res.data);
			})
			.catch((err) => {
				console.log("error when getting checked orders", err);
			});
	}, []);

	return (
		<DefaultLayout>
			<div>
				<div className={clsx("font-semibold", "capitalize", "text-2xl")}>
					Checked Orders
				</div>
				<SalesTable sales={checkedOrders} />
			</div>
		</DefaultLayout>
	);
};

const SalesTable = ({ sales }) => {
	return (
		<div className="overflow-x-scroll">
			<table className={"table mt-4 bg-white"}>
				{/* head */}
				<thead>
					<tr>
						<th>
							<label>
								<input type="checkbox" className="checkbox" />
							</label>
						</th>
						<th>Salesman</th>
						<th>Car Plate No</th>
						<th>Service Type</th>
						<th>Final Price</th>
						<th>Created At</th>
						<th>Status</th>
						<th>Actions</th>
					</tr>
				</thead>
				<tbody>
					{_.map(sales, (sale) => (
						<tr key={sale._id}>
							<th>
								<label>
									<input type="checkbox" className="checkbox" />
								</label>
							</th>
							<td>
								<div className="flex items-center gap-3">
									<div>
										<div className="font-bold capitalize">
											{sale.salesman.name}
										</div>
										<div className="text-sm opacity-50">VB Motor</div>
									</div>
								</div>
							</td>
							<td>{sale.carPlateNo}</td>
							<td className={"capitalize"}>{sale.serviceType}</td>
							<td>{"RM " + parseFloat(sale.finalPrice).toFixed(2)}</td>
							<td>{moment(sale.createdAt).format("lll")}</td>
							<td className={"capitalize"}>{sale.status}</td>
							<td className={"capitalize"}>
								<button
									onClick={() => {
										window.location.href = `/installer/${sale._id}`;
									}}
									className="btn btn-ghost p-0"
								>
									<IoEyeOutline style={{ fontSize: 20 }} />
								</button>
							</td>
						</tr>
					))}
				</tbody>
			</table>
		</div>
	);
};

export default CheckedOrdersPage;
