"use client";

import DefaultLayout from "../../components/Layouts/DefaultLayout";
import { clsx } from "clsx";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { fetch } from "../../helpers/index";
import _ from "lodash";
import OrdersTable from "../../components/InstallerChecker/OrdersTable";

const InstallerCheckerPage = () => {
	const [activeTab, setActiveTab] = useState("dashboard");
	const [orders, setOrders] = useState([]);
	const [checkedOrders, setCheckedOrders] = useState([]);
	const [ordersLength, setOrdersLength] = useState(0);
	const [checkedOrdersLength, setCheckedOrdersLength] = useState(0);
	const [formErrors, setFormErrors] = useState(null);

	const getOrders = async (limit = 5, page = 1) => {
		try {
			const res = await fetch("/orders" + `?page=${page}&limit=${limit}&status=in_progress`);
			console.log("current orders result", res);
			setOrders(res.data.orders);
			setOrdersLength(res.data.numberOfOrders);
		} catch (err) {
			console.log("error when getting current orders", err);
			if (err.response?.data?.message) {
				setFormErrors(err.response.data.message);
			}
		}
	};

	const getCheckedOrders = async (limit = 5, page = 1) => {
		try {
			const res = await fetch("/orders" + `?page=${page}&limit=${limit}&checkedByInstaller=true`);
			console.log("checked orders result", res);
			setCheckedOrders(res.data.orders);
			setCheckedOrdersLength(res.data.numberOfOrders);
		} catch (err) {
			console.log("error when getting checked orders", err);
			if (err.response?.data?.message) {
				setFormErrors(err.response.data.message);
			}
		}
	};

	useEffect(() => {
		if (activeTab === "dashboard") {
			getOrders();
			getCheckedOrders();
		} else if (activeTab === "checkedOrders") {
			getCheckedOrders();
		} else if (activeTab === "currentOrders") {
			getOrders();
		}
	}, [activeTab]);

	return (
		<DefaultLayout>
			<div>
				<div className={clsx("font-semibold", "capitalize", "text-2xl", "mb-4")}>
					Installer Checker
				</div>

				<div className="tabs mb-6">
					<a
						className={`tab tab-bordered ${activeTab === "dashboard" ? "tab-active" : ""}`}
						onClick={() => setActiveTab("dashboard")}
					>
						Dashboard
					</a>
					<a
						className={`tab tab-bordered ${activeTab === "checkedOrders" ? "tab-active" : ""}`}
						onClick={() => setActiveTab("checkedOrders")}
					>
						Checked Orders
					</a>
					<a
						className={`tab tab-bordered ${activeTab === "currentOrders" ? "tab-active" : ""}`}
						onClick={() => setActiveTab("currentOrders")}
					>
						Current Orders
					</a>
				</div>

				{activeTab === "checkedOrders" && (
					<OrdersTable
						orders={checkedOrders}
						getOrders={getCheckedOrders}
						ordersLength={checkedOrdersLength}
						isCheckedList={true}
						error={formErrors}
					/>
				)}

				{activeTab === "currentOrders" && (
					<OrdersTable
						orders={orders}
						getOrders={getOrders}
						ordersLength={ordersLength}
						isCheckedList={false}
						error={formErrors}
					/>
				)}
			</div>
		</DefaultLayout>
	);
};

export default InstallerCheckerPage; 