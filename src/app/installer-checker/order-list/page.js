"use client";

import DefaultLayout from "../../../components/Layouts/DefaultLayout";
import { clsx } from "clsx";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { fetch } from "../../../helpers";
import _ from "lodash";
import moment from "moment";
import { IoEyeOutline } from "react-icons/io5";

const InstallerPage = () => {
  const [sales, setSales] = useState([]);

  useEffect(() => {
    fetch("/orders")
      .then((res) => {
        console.log("sales result", res);
        setSales(res.data.orders);
      })
      .catch((err) => {
        console.log("error when getting sales", err);
      });
  }, []);

  return (
    <DefaultLayout>
      <div>
        <div className={clsx("font-semibold", "capitalize", "text-2xl")}>
          Sales
        </div>
        <SalesTable sales={sales} />
      </div>
    </DefaultLayout>
  );
};

const AddSalesButton = () => {
  const router = useRouter();

  return (
    <div className={clsx("flex", "justify-end")}>
      <button
        onClick={() => router.push("/sales/add")}
        className={clsx(
          "bg-primary",
          "text-white",
          "rounded-md",
          "px-6",
          "py-2",
          "mt-1",
        )}
      >
        Create Sales
      </button>
    </div>
  );
};

const SalesTable = ({ sales }) => {
  return (
    <div className="overflow-x-scroll">
      <table className={"table mt-4 bg-white"}>
        {/* head */}
        <thead>
          <tr>
            <th>
              <label>
                <input type="checkbox" className="checkbox" />
              </label>
            </th>
            <th>Salesman</th>
            <th>Car Plate No</th>
            <th>Service Type</th>
            <th>Final Price</th>
            <th>Created At</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {_.map(sales, (sale) => (
            <tr>
              <th>
                <label>
                  <input type="checkbox" className="checkbox" />
                </label>
              </th>
              <td>
                <div className="flex items-center gap-3">
                  <div>
                    <div className="font-bold capitalize">
                      {sale.salesman?.name}
                    </div>
                    <div className="text-sm opacity-50">VB Motor</div>
                  </div>
                </div>
              </td>
              <td>{sale.carPlateNo}</td>
              <td className={"capitalize"}>{sale.serviceType}</td>
              <td>{"RM " + parseFloat(sale.finalPrice).toFixed(2)}</td>
              <td>{moment(sale.createdAt).format("lll")}</td>
              <td className={"capitalize"}>{sale.status}</td>
              <td className={"capitalize"}>
                <button
                  onClick={() => {
                    window.location.href = `/installer/${sale._id}`;
                  }}
                  className="btn btn-ghost p-0"
                >
                  <IoEyeOutline style={{ fontSize: 20 }} />
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default InstallerPage;
