"use client";

import DefaultLayout from "../../components/Layouts/DefaultLayout";
import Link from "next/link";
import React, { useContext, useEffect, useState } from "react";
import Input from "../../components/Input/Input";
import { clsx } from "clsx";
import UserContext from "../../Context/UserContext";
import _ from "lodash";
import { fetch } from "@/helpers";

const Profile = () => {
  const context = useContext(UserContext);
  const [userObj, setUserObj] = useState({});

  useEffect(() => {
    const fetchUserData = async () => {
      const response = await fetch(`/self`, "get");
      if (response) {
        console.log("response:", response);
        setUserObj(response.data);
      }
    };
    fetchUserData();
  }, [context.user._id]);

  const showFields = () => {
    const user = context?.user;
    const roles = user?.roles;
    if (_.intersection(roles, ["salesman"])) {
      return true;
    }
  };

  const updateSalesman = async () => {
    await fetch("/user", "put", {
      userId: context.user._id,
      ...userObj,
    }).then(async (res) => {
      alert("update successfully!");
    });
  };

  return (
    <DefaultLayout>
      {showFields() && (
        <div
          className={clsx(
            "bg-white",
            "p-8",
            "rounded-2xl",
            "mt-4",
            "shadow-3",
            "shadow-gray-300",
          )}
        >
          <Input
            disabled={false}
            returnedValue={(value) => {
              const clone = _.cloneDeep(userObj);
              clone.businessCard = value;
              setUserObj({ ...clone });
            }}
            inputField={{
              name: "Business Card",
              type: "text",
              value: _.get(userObj, "businessCard", ""),
            }}
          />
          <Input
            disabled={false}
            returnedValue={(value) => {
              const clone = _.cloneDeep(userObj);
              clone.NRIC = value;
              setUserObj({ ...clone });
            }}
            inputField={{
              name: "IC",
              type: "text",
              value: _.get(userObj, "NRIC", ""),
            }}
          />
          <Input
            disabled={false}
            returnedValue={(value) => {
              const clone = _.cloneDeep(userObj);
              clone.bankAccountNo = value;
              setUserObj({ ...clone });
            }}
            inputField={{
              name: "Bank Account Number",
              type: "text",
              value: _.get(userObj, "bankAccountNo", ""),
            }}
          />
          <button
            onClick={updateSalesman}
            className={clsx(
              "bg-primary",
              "text-white",
              "rounded-md",
              "px-6",
              "py-2",
              "mt-1",
              "w-40",
            )}
          >
            Update
          </button>
        </div>
      )}
    </DefaultLayout>
  );
};

export default Profile;
