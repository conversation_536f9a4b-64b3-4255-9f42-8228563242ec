"use client";

import DefaultLayout from "../../components/Layouts/DefaultLayout";
import { clsx } from "clsx";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { fetch } from "../../helpers/index";
import _ from "lodash";
import moment from "moment";
import { IoEyeOutline } from "react-icons/io5";
import Input from "../../components/Input/Input";
import { auth } from "@/configs/firebase";
import Alert from "../../components/Alert/Alert"; // Import Alert component

const StaffKPIListPage = () => {
	const router = useRouter(); // Initialize useRouter
	const [staffKPI, setStaffKPI] = useState([]);
	const [showAlert, setShowAlert] = useState(false); // State for alert visibility
	const [userRoles, setUserRoles] = useState([]); // State to hold user role

	useEffect(() => {
		// Fetch user role (this is a placeholder, replace with actual role fetching logic)
		const fetchUserRole = async () => {
			const token = await auth.currentUser.getIdToken(true);
			const userInfo = await fetch("/self", "get", null, {
				headers: {
					"Authorization": token,
				},
			});

			console.log("user info:", userInfo);
			setUserRoles(userInfo?.data?.roles); // Assuming userInfo contains the role
		};

		fetchUserRole();

		fetch("/salesman/order-and-kpi")
			.then((res) => {
				console.log("staff kpi result", res);
				const sortedKPIData = res.data.sort((a, b) => moment(b.createdAt).valueOf() - moment(a.createdAt).valueOf());
				setStaffKPI(sortedKPIData);
			})
			.catch((err) => {
				console.log("error when getting staff kpi", err);
			});
	}, []);

	return (
		<DefaultLayout>
			<div>
				<div className={clsx("font-semibold", "capitalize", "text-2xl")}>
					Staff KPI List
				</div>
				{userRoles.includes('platform-admin') && ( // Check user role before displaying the button
					<button
						onClick={() => router.push('/staff-kpi/add')} // Redirect to the specified page
						className="btn bg-blue-500 text-white mt-4" // Updated button color
					>
						Add Staff KPI
					</button>
				)}
				<StaffKPITable staffKPI={staffKPI} setShowAlert={setShowAlert} /> {/* Pass setShowAlert to StaffKPITable */}
				{showAlert && <Alert text={"Upload receipt successful!"} />} {/* Show alert when receipt is uploaded */}
			</div>
		</DefaultLayout>
	);
};

const StaffKPITable = ({ staffKPI, setShowAlert }) => {
	const [selectedStaff, setSelectedStaff] = useState({});

	return (
		<div className="overflow-x-scroll">
			<table className={"table mt-4 bg-white"}>
				{/* head */}
				<thead>
					<tr>
						<th>Salesman</th>
						<th>Month</th>
						<th>KPI</th>
						<th>Commission Rate</th> {/* New Rate column */}
						<th>Achieved Sales</th>
						<th>Status</th>
						<th>Actions</th> {/* New Actions column */}
					</tr>
				</thead>
				<tbody>
					{_.map(staffKPI, (staff) => {
						const achievedPercentage = Math.min((staff.total / staff.kpi) * 100, 100);
						let progressColor = achievedPercentage >= 60 ? "green" : achievedPercentage >= 50 ? "orange" : "red";
						return (
							<tr key={staff._id}>
								<td>
									<div className="flex items-center gap-3">
										<div>
											<div className="font-bold capitalize">
												{staff?.staff?.name}
											</div>
										</div>
									</div>
								</td>
								<td>{moment().month(staff?.month).format("MMM") + ' / ' + staff?.year.toString()}</td>
								<td>{"RM " + parseFloat(staff?.kpi).toFixed(2)}</td>
								<td>{parseFloat(staff?.rate).toFixed(2)} %</td> {/* Displaying Rate */}
								<td>{"RM " + parseFloat(staff?.total).toFixed(2)}</td>
								<td>
									<span style={{ color: progressColor, marginRight: "10px" }}>{achievedPercentage.toFixed(2)}%</span>
									<div style={{ width: "100%", backgroundColor: "#ccc", borderRadius: "100px", overflow: "hidden" }}>
										<div style={{ width: `${achievedPercentage}%`, backgroundColor: progressColor, height: "10px", borderRadius: "100px" }}></div>
									</div>
								</td>
								<td>
									{staff.status === 'paid' ? (
										<div style={{ display: 'flex', alignItems: 'center' }}>
											<span style={{ color: 'green' }}>Paid</span>
											{staff.receiptUrl && (
												<a href={staff.receiptUrl} target="_blank" rel="noopener noreferrer" className="btn btn-link ml-2">
													View Receipt <IoEyeOutline />
												</a>
											)}
										</div>
									) : (
										<button
											onClick={() => {
												setSelectedStaff(staff); // Set the selected sale for payout
												document.getElementById("uploadReceiptModal")?.showModal(); // Show the modal
											}}
											className="btn btn-primary"
										>
											Payout
										</button>
									)}
								</td>
							</tr>
						);
					})}
				</tbody>
			</table>
			<UploadReceiptModal selectedStaff={selectedStaff} setShowAlert={setShowAlert} /> {/* Pass setShowAlert to UploadReceiptModal */}
		</div>
	);
};

const UploadReceiptModal = ({ selectedStaff, setShowAlert }) => {
	const [receipt, setReceipt] = useState({});

	const pay = async () => {
		const form = new FormData();
		form.append("staffId", selectedStaff.staffId?._id);
		form.append("receipt", receipt);

		const token = await auth.currentUser.getIdToken(true);
		await fetch("/staff/payout", "post", form, {
			headers: {
				"Content-Type": "multipart/form-data",
				"Authorization": token,
			},
		})
			.then(async (res) => {
				console.log("upload receipt successfully!");
				setShowAlert(true); // Show alert on successful upload
				closeModal(); // Close the modal
			})
			.catch((err) => {
				console.log("error when uploading receipt:", err);
			});
	};

	const closeModal = () => {
		document.getElementById("uploadReceiptModal")?.close();
	};

	return (
		<dialog id="uploadReceiptModal" className="modal" onClick={closeModal}>
			<div className="modal-box" onClick={(e) => e.stopPropagation()}>
				<h3 className="text-lg font-bold">Make Payment</h3>
				<p className="mt-4">Selected Staff ID: {selectedStaff?.staffId?._id}</p>
				<Input
					disabled={true}
					returnedValue={(value) => { }}
					inputField={{
						name: "Amount",
						value: "RM " + (selectedStaff?.total * (selectedStaff?.rate || 0)).toFixed(2).toString(),
					}}
				/>
				<input
					onChange={(e) => {
						setReceipt(e.target.files[0]);
					}}
					type="file"
					className="file-input mt-6 w-full max-w-xs"
				/>
				<div className="modal-action">
					<button
						onClick={() => {
							pay();
						}}
						className="btn"
					>
						Submit
					</button>
				</div>
			</div>
		</dialog>
	);
};

export default StaffKPIListPage;
