"use client";

import DefaultLayout from "../../components/Layouts/DefaultLayout";
import { useEffect, useState } from "react";
import { fetch } from "../../helpers";
import _ from "lodash";
import { clsx } from "clsx";
import moment from "moment";
import { IoEyeOutline } from "react-icons/io5";

const InstallerPage = () => {
	const [installers, setInstallers] = useState([]);

	useEffect(() => {
		fetchInstallers();
	}, []);

	const fetchInstallers = async () => {
		await fetch("/installer-with-order-stats", 'get') // Adjust the endpoint as necessary
			.then(async (res) => {
				const data = await res.data;
				console.log('installer data:', data)
				setInstallers(data);
			})
			.catch((err) => {
				console.log("error when getting installers", err);
			});
	};

	return (
		<DefaultLayout>
			<div>
				<div className={clsx("font-semibold", "capitalize", "text-2xl")}>
					Installers
				</div>
				<InstallersTable installers={installers} />
			</div>
		</DefaultLayout>
	);
};

const InstallersTable = ({ installers }) => {
	return (
		<div className="overflow-x-scroll">
			<table className={"table mt-4 bg-white"}>
				{/* head */}
				<thead>
					<tr>
						<th>Name</th>
						<th>Claimed Orders</th>
						<th>Completed Orders</th>
						<th>Actions</th>
					</tr>
				</thead>
				<tbody>
					{_.map(installers, (installer) => (
						<tr key={installer._id}>
							<td>{installer.name}</td>
							<td>{installer.claimedOrders}</td>
							<td>{installer.completedOrders}</td>
							<td className={"capitalize"}>
								<button
									onClick={() => {
										window.location.href = `/installer/details/order?id=${installer.installerId}`;
									}}
									className="btn btn-ghost p-0"
								>
									<IoEyeOutline style={{ fontSize: 20 }} />
								</button>
							</td>
						</tr>
					))}
				</tbody>
			</table>
		</div>
	);
};

export default InstallerPage; 