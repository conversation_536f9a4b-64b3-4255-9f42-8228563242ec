"use client";

import DefaultLayout from "@/components/Layouts/DefaultLayout";
import { useEffect, useState } from "react";
import { fetch } from "@/helpers";
import { useSearchParams } from "next/navigation";
import moment from "moment";

const InstallerDetailsPage = () => {
	const searchParams = useSearchParams();
	const [installer, setInstaller] = useState(null);
	const [orders, setOrders] = useState([]);
	const [installerId, setInstallerId] = useState(null);

	useEffect(() => {
		const id = searchParams.get("id");
		if (id) {
			setInstallerId(id); // Set state to installer ID from search params
		}
	}, [searchParams]);

	useEffect(() => {
		if (installerId) {
			fetchInstallerDetails(installerId);
			fetchOrders(installerId);
		}
	}, [installerId]);

	const fetchInstallerDetails = async (id) => {
		await fetch(`/installer/${id}`, 'get')
			.then((res) => {
				console.log("Installer details:", res.data);
				setInstaller(res.data);
			})
			.catch((err) => {
				console.log("Error fetching installer details:", err);
			});
	};

	const fetchOrders = async (id) => {
		await fetch(`/orders/by/installer/${id}`, 'get')
			.then((res) => {
				console.log("Orders:", res.data);
				setOrders(res.data.orders);
			})
			.catch((err) => {
				console.log("Error fetching orders:", err);
			});
	};

	return (
		<DefaultLayout>
			<div>
				{installer && (
					<div>
						<div className="border rounded-[14px] p-4 shadow-md bg-white">
							<h2 className="text-2xl font-semibold capitalize">{installer.name}</h2>
							<p>Email: {installer.email}</p>
							<p>Phone Number: {installer.phoneNumber}</p>
							<p>Created At: {moment(installer.createdAt).format("DD MMM YYYY")}</p>
						</div>
						<OrdersTable orders={orders} />
					</div>
				)}
			</div>
		</DefaultLayout>
	);
};

const OrdersTable = ({ orders }) => {
	return (
		<div className="overflow-x-scroll">
			<table className={"table mt-4 bg-white rounded-[14px]"}> {/* Removed shadow class to prevent it from being covered */}
				<thead>
					<tr>
						<th>Order ID</th>
						<th>Order Date</th>
						<th>Status</th>
						<th>Actions</th>
					</tr>
				</thead>
				<tbody>
					{orders.length > 0 ? (
						orders.map((order) => (
							<tr key={order._id}>
								<td>{order._id}</td>
								<td>{moment(order.createdAt).format("lll")}</td>
								<td>{order.status}</td>
								<td>
									<button
										className="btn btn-ghost p-0"
										onClick={() => {
											window.location.href = `/sales/${order._id}`; // Redirect to the sales page
										}}
									>
										View Details
									</button>
								</td>
							</tr>
						))
					) : (
						<tr>
							<td colSpan={4} className="text-center">N/A</td>
						</tr>
					)}
				</tbody>
			</table>
		</div>
	);
};

export default InstallerDetailsPage; 