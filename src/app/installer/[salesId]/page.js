"use client";

import DefaultLayout from "../../../components/Layouts/DefaultLayout";
import BackButton from "../../../components/BackButton/BackButton";
import { clsx } from "clsx";
import _ from "lodash";
import { useEffect, useState } from "react";
import { fetch } from "../../../helpers/index";
import { useParams, useRouter } from "next/navigation";
import DatePicker from "react-tailwindcss-datepicker";
import Alert from "../../../components/Alert/Alert";
import OrderDetailsCard from "../../../components/AddSalesPage/OrderDetailsCard";
import OwnerDetailsCard from "../../../components/AddSalesPage/OwnerDetailsCard";
import VehicleDetailsCard from "../../../components/AddSalesPage/VehicleDetailsCard";
import VehicleTintInspectionCard from "../../../components/AddSalesPage/VehicleTintInspectionCard";
import VehicleCoatingInspectionCard from "../../../components/AddSalesPage/VehicleCoatingInspectionCard";
import CreateSalesButton from "../../../components/AddSalesPage/CreateSalesButton";
import AddOnDetailsCard from "../../../components/AddSalesPage/AddOnDetailsCard";
import SignatureCard from "@/components/SignatureCard/SignatureCard";
import InstallerCheckerCard from "../../../components/AddSalesPage/InstallerCheckerCard"; // Import the InstallerCheckerCard

const AddInstallerPage = ({ params }) => {
  const [showAlert, setShowAlert] = useState(false);
  const router = useRouter();
  const { salesId } = useParams();
  const [packageOptions, setPackageOptions] = useState([]);
  const [productOptions, setProductOptions] = useState([]);
  const [vehicleOptions, setVehicleOptions] = useState([]);
  const [users, setUsers] = useState([]);
  const [salesObj, setSalesObj] = useState({
    serviceType: "tint",
    paymentMethod: "Card",
    finalPrice: 0.0,
    vehicleInspection: {
      changeDarkness: false,
      removeOldFilmScreen: false,
      ownerWaiting: false,
      windscreenCoating: {
        front: false,
        rear: false,
        side: false,
        sunroof: false,
      },
      interiorCoating: {
        plastic: false,
        leather: false,
        fabric: false,
      },
      exteriorCoating: {
        plastic: false,
        rubber: false,
      },
    },
  });

  // set car model
  useEffect(() => {
    getVehicles();
    getSale();
    getUsers();
  }, []);

  // set product options
  useEffect(() => {
    getProductOptions();
  }, [salesObj.package]);

  // set package options based on the service type selected
  useEffect(() => {
    getPackages();
  }, [salesObj.serviceType]);

  const getUsers = async () => {
    await fetch("/users")
      .then(async (res) => {
        const data = await res.data;
        console.log("users:", data);
        setUsers(data);
      })
      .catch((err) => {
        console.log("error when getting users:", err);
      });
  };

  const getProductOptions = async () => {
    const foundPackage = _.find(packageOptions, { _id: salesObj.package });
    const pOptions = _.get(foundPackage, "products", []).map((product) => ({
      name: product.name,
      value: product.name,
      types: product.types,
    }));
    setProductOptions(pOptions);
  };

  const getVehicles = async () => {
    await fetch("/vehicles")
      .then(async (res) => {
        const data = await res.data;
        console.log("vehicles:", data);
        setVehicleOptions(data);
        setSalesObj((prev) => ({
          ...prev,
          vehicleBrand: data[0].name,
          vehicleModel: data[0].models[0].name,
        }));
      })
      .catch((err) => {
        console.log("error while getting vehicles:", err);
      });
  };

  const getPackages = async () => {
    await fetch("/packages")
      .then(async (res) => {
        const data = await res.data;
        console.log("packages:", data);
        const filteredData = _.filter(data, {
          serviceType: salesObj?.serviceType,
        });
        setPackageOptions([...filteredData]);
        if (_.isEmpty(salesObj?.package)) {
          setSalesObj((prev) => ({
            ...prev,
            package: filteredData[0]?._id,
          }));
        } else {
          setSalesObj((prev) => ({
            ...prev,
            package: filteredData[0]?._id,
          }));
        }
      })
      .catch((err) => {
        console.log("error while getting packages:", err);
      });
  };

  const getSale = async () => {
    await fetch(`/orders?orderId=${salesId}`)
      .then(async (res) => {
        const data = await res.data.orders;
        console.log("data:", data);
        setSalesObj({ ...data[0] });
      })
      .catch(async (err) => {
        console.log("error while getting sale:", err);
      });
  };

  const createSales = async () => {
    console.log("sales object:", { orderId: salesId, ...salesObj });
    await fetch("/order", "put", {
      orderId: salesId,
      ...salesObj,
      salesman: salesObj?.salesman._id,
    })
      .then(async (res) => {
        console.log("order created:", await res);
        setShowAlert(true);
        setTimeout(() => {
          setShowAlert(false);
          router.refresh();
        }, 5000);
      })
      .catch((err) => {
        console.log("error when adding order:", err);
      });
  };

  // monitor sales object
  useEffect(() => {
    console.log("sales object:", salesObj);
  }, [salesObj]);

  return (
    <DefaultLayout>
      <BackButton />
      <OrderDetailsCard
        disabled={true}
        packageOptions={packageOptions}
        setSalesObj={setSalesObj}
        salesObj={salesObj}
        users={users}
      />
      <OwnerDetailsCard
        disabled
        salesObj={salesObj}
        setSalesObj={setSalesObj}
      />
      <VehicleDetailsCard
        disabled
        vehicleOptions={vehicleOptions}
        salesObj={salesObj}
        setSalesObj={setSalesObj}
      />
      {/* <AddOnDetailsCard
        salesObj={salesObj}
        setSalesObj={setSalesObj}
        productOptions={productOptions}
      /> */}
      {salesObj?.serviceType === "tint" ? (
        <VehicleTintInspectionCard
          salesObj={salesObj}
          setSalesObj={setSalesObj}
          productOptions={productOptions}
          disabled
        />
      ) : (
        <VehicleCoatingInspectionCard
          salesObj={salesObj}
          setSalesObj={setSalesObj}
          productOptions={productOptions}
          disabled
        />
      )}
      <InstallerCheckerCard salesObj={salesObj} setSalesObj={setSalesObj} /> {/* Added InstallerCheckerCard */}
      <SignatureCard
        salesObj={salesObj}
        setSalesObj={setSalesObj}
      />
      <UpdateSalesButton onClick={createSales} />
      {showAlert && <Alert text={"Your order has been updated!"} />}
    </DefaultLayout>
  );
};

const UpdateSalesButton = ({ onClick }) => {
  return (
    <div className={"flex w-full justify-end"}>
      <button
        onClick={onClick}
        className={clsx(
          "btn",
          "bg-primary",
          "text-white",
          "px-6",
          "py-2",
          "mt-4",
        )}
      >
        Update Sales
      </button>
    </div>
  );
};

export default AddInstallerPage;
