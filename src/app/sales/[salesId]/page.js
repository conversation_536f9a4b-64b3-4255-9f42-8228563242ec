/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import DefaultLayout from "../../../components/Layouts/DefaultLayout";
import { clsx } from "clsx";
import _ from "lodash";
import { useContext, useEffect, useState } from "react";
import { fetch } from "../../../helpers/index";
import { usePara<PERSON>, useRouter } from "next/navigation";
import DatePicker from "react-tailwindcss-datepicker";
import Alert from "../../../components/Alert/Alert";
import OrderDetailsCard from "../../../components/AddSalesPage/OrderDetailsCard";
import OwnerDetailsCard from "../../../components/AddSalesPage/OwnerDetailsCard";
import VehicleDetailsCard from "../../../components/AddSalesPage/VehicleDetailsCard";
import VehicleTintInspectionCard from "../../../components/AddSalesPage/VehicleTintInspectionCard";
import VehicleCoatingInspectionCard from "../../../components/AddSalesPage/VehicleCoatingInspectionCard";
import OrderTypeCard from "../../../components/AddSalesPage/OrderTypeCard";
import BuildingDetailsCard from "../../../components/AddSalesPage/BuildingDetailsCard";
import ReviewSection from '@/components/ReviewSection';
import InstallerCheckerCard from "../../../components/AddSalesPage/InstallerCheckerCard";
import UserContext from "@/Context/UserContext";

const EditSalesPage = () => {
	const [showAlert, setShowAlert] = useState(false);
	const router = useRouter();
	const { salesId } = useParams();
	const [packageOptions, setPackageOptions] = useState([]);
	const [productOptions, setProductOptions] = useState([]);
	const [vehicleOptions, setVehicleOptions] = useState([]);
	const [installers, setInstallers] = useState([])
	const [users, setUsers] = useState([]);
	const [salesObj, setSalesObj] = useState({
		serviceType: "coating",
		paymentMethod: "Card",
		finalPrice: 0.0,
		customerLead: "Walk In",
		vehicleInspection: {
			changeDarkness: false,
			removeOldFilmScreen: false,
			ownerWaiting: false,
			windscreenCoating: {
				front: false,
				rear: false,
				side: false,
				sunroof: false,
			},
			interiorCoating: {
				plastic: false,
				leather: false,
				fabric: false,
			},
			exteriorCoating: {
				plastic: false,
				rubber: false,
			},
		},
		orderType: "car", // Default order type
	});

	// get installers
	const getAllInstallers = async () => {

		const response = await fetch("/users");
		const data = await response.data;
		const installers = await data

		const installersData = [
			{
				name: "Please select a user",
				value: "",
			},
			...installers.map(installer => ({
				name: installer.name,
				value: installer._id
			}))
		];

		setInstallers(installersData)
	};

	// context
	const context = useContext(UserContext);

	// set car model
	useEffect(() => {
		getVehicles();
		getSale();
		getUsers();
		getAllInstallers()
	}, []);

	// set product options
	useEffect(() => {
		console.log('sales package:', salesObj.package)
		getProductOptions();
	}, [salesObj.package]);

	// set package options based on the service type selected
	useEffect(() => {
		getPackages();
	}, [salesObj.serviceType]);

	const getUsers = async () => {
		await fetch("/users")
			.then(async (res) => {
				const data = await res.data;
				setUsers(data);
			})
			.catch((err) => {
				console.log("error when getting users:", err);
			});
	};

	const getProductOptions = async (fd) => {
		const foundPackage = _.find(fd, { _id: salesObj.package });
		const pOptions = _.get(foundPackage, "products", []).map((product) => ({
			name: product.name,
			value: product.name,
			types: product.types,
			serviceType: product.serviceType
		}));
		console.log("product options:", packageOptions);
		setProductOptions(pOptions);
	};

	const getVehicles = async () => {
		await fetch("/vehicles")
			.then(async (res) => {
				const data = await res.data;
				setVehicleOptions(data);
				setSalesObj((prev) => ({
					...prev,
					vehicleBrand: data[0].name,
					vehicleModel: data[0].models[0].name,
				}));
			})
			.catch((err) => {
				console.log("error while getting vehicles:", err);
			});
	};

	const getPackages = async () => {
		await fetch("/packages")
			.then(async (res) => {
				const data = await res.data;
				const filteredData = _.filter(data, {
					serviceType: salesObj?.serviceType,
				});
				console.log("filtered data:", filteredData);
				getProductOptions(filteredData)
				setPackageOptions([...filteredData]);
				if (_.isEmpty(salesObj?.package)) {
					setSalesObj((prev) => ({
						...prev,
						package: filteredData[0]?._id,
					}));
				} else {
					setSalesObj((prev) => ({
						...prev,
					}));
				}
				// Set order type based on service type
				if (salesObj.serviceType === "building-tint") {
					setSalesObj((prev) => ({
						...prev,
						orderType: "building",
					}));
				} else {
					setSalesObj((prev) => ({
						...prev,
						orderType: "car",
					}));
				}
			})
			.catch((err) => {
				console.log("error while getting packages:", err);
			});
	};

	const getSale = async () => {
		await fetch(`/orders?orderId=${salesId}`)
			.then(async (res) => {
				const data = await res.data.orders;
				setSalesObj({ ...data[0] });
			})
			.catch(async (err) => {
				console.log("error while getting sale:", err);
			});
	};

	const updateSales = async () => {
		console.log("sales object:", salesObj);
		await fetch("/order", "put", { orderId: salesId, ...salesObj, salesman: salesObj?.salesman._id })
			.then(async (res) => {
				console.log("order updated:", await res);
				setShowAlert(true);
				setTimeout(() => {
					setShowAlert(false);
					router.refresh();
				}, 5000);
			})
			.catch((err) => {
				console.log("error when updating order:", err);
			});
	};

	// monitor sales object
	useEffect(() => {
		console.log("sales object:", salesObj);
	}, [salesObj]);

	return (
		<DefaultLayout>
			<div className={"flex justify-between mb-4"}>
				<button
					onClick={() => router.back()}
					className={clsx(
						"btn",
						"bg-blue-500",
						"text-white",
						"px-6",
						"py-2",
						"mt-1",
					)}
				>
					Back
				</button>
			</div>
			<OrderTypeCard
				salesObj={salesObj}
				setSalesObj={setSalesObj}
			/>
			{salesObj.serviceType === "building-tint" ? (
				<BuildingDetailsCard
					packageOptions={packageOptions}
					productOptions={productOptions}
					salesObj={salesObj}
					setSalesObj={setSalesObj}
					users={users}
				/>
			) : (
				<OrderDetailsCard
					packageOptions={packageOptions}
					setSalesObj={setSalesObj}
					salesObj={salesObj}
					users={users}
				/>
			)}
			<OwnerDetailsCard salesObj={salesObj} setSalesObj={setSalesObj} />
			<InstallerCheckerCard
				salesObj={salesObj} setSalesObj={setSalesObj}
				disabled={!context?.user?.roles?.includes("installer")} />
			{(salesObj.serviceType === "tint" || salesObj.serviceType === "coating") && (
				<>
					<VehicleDetailsCard
						vehicleOptions={vehicleOptions}
						salesObj={salesObj}
						setSalesObj={setSalesObj}
					/>
					{/* <AddOnDetailsCard
						salesObj={salesObj}
						setSalesObj={setSalesObj}
						productOptions={productOptions}
					/> */}
					{salesObj?.serviceType === "tint" ? (
						<VehicleTintInspectionCard
							salesObj={salesObj}
							setSalesObj={setSalesObj}
							productOptions={productOptions}
							installers={installers}
						/>
					) : (
						<VehicleCoatingInspectionCard
							salesObj={salesObj}
							setSalesObj={setSalesObj}
							productOptions={productOptions}
							installers={installers}
						/>
					)}
				</>
			)}
			{showAlert && <Alert type="success" text={"Your order has been updated!"} />}
			<ReviewSection
				salesObj={salesObj}
				setSalesObj={setSalesObj}
			/>
			{salesObj.status !== "paid" && <UpdateSalesButton onClick={updateSales} />}
		</DefaultLayout>
	);
};

const UpdateSalesButton = ({ onClick }) => {
	return (
		<div className={"flex w-full justify-end"}>
			<button
				onClick={onClick}
				className={clsx(
					"btn",
					"bg-primary",
					"text-white",
					"px-6",
					"py-2",
					"mt-4",
				)}
			>
				Update Sales
			</button>
		</div>
	);
};

export default EditSalesPage;
