"use client";

import DefaultLayout from "../../../components/Layouts/DefaultLayout";
import BackButton from "../../../components/BackButton/BackButton";
import { clsx } from "clsx";
import _ from "lodash";
import { useContext, useEffect, useState } from "react";
import { fetch } from "../../../helpers/index";
import { useRouter } from "next/navigation";
import DatePicker from "react-tailwindcss-datepicker";
import Alert from "../../../components/Alert/Alert";
import OrderDetailsCard from "../../../components/AddSalesPage/OrderDetailsCard";
import OwnerDetailsCard from "../../../components/AddSalesPage/OwnerDetailsCard";
import VehicleDetailsCard from "../../../components/AddSalesPage/VehicleDetailsCard";
import VehicleTintInspectionCard from "../../../components/AddSalesPage/VehicleTintInspectionCard";
import VehicleCoatingInspectionCard from "../../../components/AddSalesPage/VehicleCoatingInspectionCard";
import CreateSalesButton from "../../../components/AddSalesPage/CreateSalesButton";
import AddOnDetailsCard from "../../../components/AddSalesPage/AddOnDetailsCard";
import OrderTypeCard from "../../../components/AddSalesPage/OrderTypeCard";
import BuildingDetailsCard from "../../../components/AddSalesPage/BuildingDetailsCard";
import InstallerCheckerCard from "../../../components/AddSalesPage/InstallerCheckerCard";
import UserContext from "@/Context/UserContext";

const CreateSalesPage = () => {
  const [showAlert, setShowAlert] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [errors, setErrors] = useState({});
  const router = useRouter();
  const [packageOptions, setPackageOptions] = useState([]);
  const [productOptions, setProductOptions] = useState([]);
  const [vehicleOptions, setVehicleOptions] = useState([]);
  const [installers, setInstallers] = useState([])
  const [users, setUsers] = useState([]);
  const [salesObj, setSalesObj] = useState({
    serviceType: "tint",
    paymentMethod: "Card",
    finalPrice: null,
    vehicleInspection: {
      changeDarkness: false,
      removeOldFilmScreen: false,
      ownerWaiting: false,
      windscrenCoating: {
        front: false,
        rear: false,
        side: false,
        sunroof: false,
      },
      interiorCoating: {
        plastic: false,
        leather: false,
        fabric: false,
      },
      exteriorCoating: {
        plastic: false,
        rubber: false,
      },
    },
    orderType: "car", // Default order type
  });

  const context = useContext(UserContext);

  // set car model
  useEffect(() => {
    getVehicles();
    getUsers();
    getAllInstallers()
  }, []);

  // get installers
  const getAllInstallers = async () => {

    const response = await fetch("/users");
    const data = await response.data;
    const installers = await data

    const installersData = [
      {
        name: "Please select a user",
        value: "",
      },
      ...installers.map(installer => ({
        name: installer.name,
        value: installer._id
      }))
    ];

    setInstallers(installersData)
  };

  // set product options
  useEffect(() => {
    getProductOptions();
  }, [salesObj.package]);

  // set package options based on the service type selected
  useEffect(() => {
    getPackages();
  }, [salesObj.serviceType]);

  const getUsers = async () => {
    await fetch("/users")
      .then(async (res) => {
        const data = await res.data;
        console.log("users:", data);
        setUsers(data);
        // setSalesObj((prev) => ({
        //   ...prev,
        //   salesman: data[0]._id,
        // }));
      })
      .catch((err) => {
        console.log("error when getting users:", err);
      });
  };


  const getProductOptions = async () => {
    const foundPackage = _.find(packageOptions, { _id: salesObj.package });
    const pOptions = _.get(foundPackage, "products", []).map((product) => ({
      name: product.name,
      value: product.name,
      types: product.types,
      serviceType: product.serviceType
    }));
    setProductOptions(pOptions);
  };

  const getVehicles = async () => {
    await fetch("/vehicles")
      .then(async (res) => {
        const data = await res.data;
        console.log("vehicles:", data);
        setVehicleOptions(data);
        setSalesObj((prev) => ({
          ...prev,
          vehicleBrand: data[0].name,
          vehicleModel: data[0].models[0].name,
        }));
      })
      .catch((err) => {
        console.log("error while getting vehicles:", err);
      });
  };

  const getPackages = async () => {
    await fetch("/packages")
      .then(async (res) => {
        const data = await res.data;
        console.log("packages:", data);
        const filteredData = _.filter(data, {
          serviceType: salesObj?.serviceType,
        });
        setPackageOptions([...filteredData]);
        if (_.isEmpty(salesObj?.package)) {
          setSalesObj((prev) => ({
            ...prev,
            package: filteredData[0]?._id,
          }));
        } else {
          setSalesObj((prev) => ({
            ...prev,
            package: filteredData[0]?._id,
          }));
        }
      })
      .catch((err) => {
        console.log("error while getting packages:", err);
      });
  };

  const createSales = async () => {
    console.log("sales object:", salesObj);
    setErrors({});
    setErrorMessage(null);

    await fetch("/order", "post", salesObj)
      .then(async (res) => {
        console.log("order created:", await res);
        setShowAlert(true);
        setErrorMessage(null);
      })
      .catch((err) => {
        console.log("error when adding order:", err);
        if (err?.data?.message) {
          setErrorMessage(JSON.stringify(err.data.message));
          setErrors(err.data.message);
        }
      });
  };

  // monitor sales object
  useEffect(() => {
    console.log("sales object:", salesObj);
  }, [salesObj]);

  return (
    <DefaultLayout>
      <BackButton />
      <OrderTypeCard
        salesObj={salesObj}
        setSalesObj={setSalesObj}
        errors={errors}
      />
      {salesObj.serviceType === "building-tint" ? (
        <BuildingDetailsCard
          packageOptions={packageOptions}
          productOptions={productOptions}
          salesObj={salesObj}
          setSalesObj={setSalesObj}
          errors={errors}
          users={users}
        />
      ) : (
        <OrderDetailsCard
          packageOptions={packageOptions}
          setSalesObj={setSalesObj}
          salesObj={salesObj}
          users={users}
          errors={errors}
        />
      )}
      <OwnerDetailsCard
        salesObj={salesObj}
        setSalesObj={setSalesObj}
        errors={errors}
      />
      {(salesObj.serviceType === "coating" || salesObj.serviceType === "tint") && (
        <>
          <VehicleDetailsCard
            vehicleOptions={vehicleOptions}
            salesObj={salesObj}
            setSalesObj={setSalesObj}
            errors={errors}
          />
          <AddOnDetailsCard
            salesObj={salesObj}
            setSalesObj={setSalesObj}
            productOptions={productOptions}
            errors={errors}
          />
          {salesObj?.serviceType === "tint" && (
            <VehicleTintInspectionCard
              salesObj={salesObj}
              setSalesObj={setSalesObj}
              productOptions={productOptions}
              errors={errors}
              installers={installers}
            />
          )}
          {salesObj?.serviceType === "coating" && (
            <VehicleCoatingInspectionCard
              salesObj={salesObj}
              setSalesObj={setSalesObj}
              productOptions={productOptions}
              errors={errors}
              installers={installers}
            />
          )}
        </>
      )}
      <InstallerCheckerCard
        salesObj={salesObj}
        setSalesObj={setSalesObj}
        errors={errors}
        disabled={!context?.user?.roles?.includes("installer")}
      />
      <CreateSalesButton onClick={createSales} />
      {showAlert && <Alert type='success' text={"You have created the order successfully!"} />}
      {/* {errorMessage && <Alert text={errorMessage} type="error" />} */}
    </DefaultLayout>
  );
};

export default CreateSalesPage;
