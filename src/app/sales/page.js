"use client";

import DefaultLayout from "../../components/Layouts/DefaultLayout";
import { clsx } from "clsx";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { fetch } from "../../helpers/index";
import _ from "lodash";
import moment from "moment";
import { IoEyeOutline } from "react-icons/io5";
import Input from "../../components/Input/Input";

const SalesPage = () => {
	const [sales, setSales] = useState([]);
	const [ordersLength, setOrdersLength] = useState();
	const [payment, setPayment] = useState({});

	const getOrders = async (limit = 5, page = 1) => {
		await fetch("/orders" + `?page=${page}&limit=${limit}`)
			.then((res) => {
				console.log("sales result", res);
				console.log("order length:", res.data.numberOfOrders);
				setSales(res.data.orders);
				setOrdersLength(res.data.numberOfOrders);
			})
			.catch((err) => {
				console.log("error when getting sales", err);
			});
	};

	return (
		<DefaultLayout>
			<div>
				<div className={clsx("font-semibold", "capitalize", "text-2xl")}>
					Sales
				</div>
				<AddSalesButton />
				<SalesTable
					sales={sales}
					getOrders={getOrders}
					ordersLength={ordersLength}
				/>
			</div>
		</DefaultLayout>
	);
};

const AddSalesButton = () => {
	const router = useRouter();

	return (
		<div className={clsx("flex", "justify-end")}>
			<button
				onClick={() => router.push("/sales/add")}
				className={clsx(
					"bg-primary",
					"text-white",
					"rounded-md",
					"px-6",
					"py-2",
					"mt-1",
				)}
			>
				Create Sales
			</button>
		</div>
	);
};

const SalesTable = ({ sales, getOrders, ordersLength }) => {
	const [sale, setSale] = useState({});
	const [page, setPage] = useState(0);

	useEffect(() => {
		getOrders(5, page);
	}, [page]);

	useEffect(() => {
		console.log("orders length:", ordersLength);
	}, [ordersLength]);

	return (
		<div className="overflow-x-scroll">
			<table className={"table mt-4 bg-white"}>
				{/* head */}
				<thead>
					<tr>
						<th>
							<label>
								<input type="checkbox" className="checkbox" />
							</label>
						</th>
						<th>Salesman</th>
						<th>Car Plate No</th>
						<th>Service Type</th>
						<th>Lead Source</th>
						<th>Final Price</th>
						<th>Created At</th>
						<th>Status</th>
						<th>Actions</th>
					</tr>
				</thead>
				<tbody>
					{_.map(sales, (sale) => (
						<tr key={sale._id}>
							<th>
								<label>
									<input type="checkbox" className="checkbox" />
								</label>
							</th>
							<td>
								<div className="flex items-center gap-3">
									<div>
										<div className="font-bold capitalize">
											{sale.salesman?.name}
										</div>
										<div className="text-sm opacity-50">VB Motor</div>
									</div>
								</div>
							</td>
							<td>{sale.carPlateNo}</td>
							<td className={"capitalize"}>{sale.serviceType}</td>
							<td>{sale.customerLead || "Walk In"}</td>
							<td>{"RM " + parseFloat(sale.finalPrice)?.toFixed(2)}</td>
							<td>{moment(sale.createdAt).format("lll")}</td>
							<td className={"capitalize"}>{sale.status}</td>
							<td className={clsx("capitalize", "flex", "justify-between")}>
								<button
									onClick={() => {
										window.location.href = `/sales/${sale._id}`;
									}}
									className="btn btn-ghost p-0"
								>
									<IoEyeOutline style={{ fontSize: 20 }} />
								</button>
								{sale.status !== "paid" && (
									<button
										onClick={() => {
											document
												.getElementById("uploadReceiptModal")
												?.showModal();
											setSale(sale);
										}}
										className="text- btn btn-ghost p-0 text-orange-400"
									>
										<div>Pay</div>
									</button>
								)}
							</td>
						</tr>
					))}
				</tbody>
			</table>
			<div className="join mt-2 flex justify-end">
				{_.times(Math.ceil(ordersLength / 5), (index) => (
					<button
						onClick={() => {
							setPage(index);
						}}
						className={`btn join-item btn-sm bg-white ${page === index && "btn-active"}`}
					>
						{index + 1}
					</button>
				))}
			</div>
			<UploadReceiptModal sale={sale} />
		</div>
	);
};

const UploadReceiptModal = ({ sale }) => {
	const [receipt, setReceipt] = useState({});

	const pay = async () => {
		const form = new FormData();
		form.append("orderId", sale._id);
		form.append("receipt", receipt);

		await fetch("/pay", "post", form, {
			headers: {
				"Content-Type": "multipart/form-data",
			},
		})
			.then(async (res) => {
				console.log("upload receipt successfully!");
			})
			.catch((err) => {
				console.log("error when uploading receipt:", err);
			});
	};

	const closeModal = () => {
		document.getElementById("uploadReceiptModal")?.close();
	};

	return (
		<dialog id="uploadReceiptModal" className="modal" onClick={closeModal}>
			<div className="modal-box" onClick={(e) => e.stopPropagation()}>
				<h3 className="text-lg font-bold">Make Payment</h3>
				<Input
					disabled={true}
					returnedValue={(value) => { }}
					inputField={{
						name: "Amount",
						value: "RM " + sale?.finalPrice?.toFixed(2).toString(),
						className: "w-full border border-gray-300" // Set outline to none
					}}
				/>
				<div className="flex items-center">
					<input
						onChange={(e) => {
							const file = e.target.files[0];
							setReceipt(file);
						}}
						type="file"
						className="file-input mt-6 w-full border border-gray-300 focus:outline-none" // Set outline to none
						onClick={(e) => {
							e.target.value = null; // Reset the input value to allow re-selection of the same file
						}}
					/>
					{receipt && (
						<button
							type="button"
							className="ml-2 text-red-500"
							onClick={() => {
								setReceipt(null); // Deselect the file
							}}
						>
							&times; {/* X button */}
						</button>
					)}
				</div>
				<div className="modal-action">
					{/* if there is a button in form, it will close the modal */}
					<button
						onClick={() => {
							pay();
						}}
						className="btn"
					>
						Submit
					</button>
				</div>
			</div>
		</dialog>
	);
};

export default SalesPage;
