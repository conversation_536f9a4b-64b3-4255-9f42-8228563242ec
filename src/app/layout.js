"use client";
import "jsvectormap/dist/jsvectormap.css";
import "flatpickr/dist/flatpickr.min.css";
import "@/css/satoshi.css";
import "@/css/style.css";
import React, { useEffect, useState } from "react";
import Loader from "@/components/common/Loader";
import { auth } from "@/configs/firebase";
import _ from "lodash";
import { usePathname, useRouter } from "next/navigation";
import { clsx } from "clsx";
import UserContext from "@/Context/UserContext";
import { fetch } from "@/helpers";

export default function RootLayout({ children }) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [fbUser, setFBUser] = useState({});

  const router = useRouter();
  const pathName = usePathname();

  // monitor user data
  useEffect(() => {
    console.log("user data:", fbUser);
  }, [fbUser]);

  useEffect(() => {
    // only do this checking if it does not belong to sign up and sign in path.
    const excludedRoutes = ["/auth/signin", "/auth/signup"];
    if (!_.includes(excludedRoutes, pathName)) {
      auth.onAuthStateChanged(async (user) => {
        console.log("user:", user);
        if (_.isEmpty(user)) {
          await router.push("/auth/signin");
        }

        if (!_.isEmpty(user)) {
          await fetch(`/self?firebaseUserId=${user?.uid}`).then(async (res) => {
            const data = await res.data;

            setFBUser({ ...user, ...data });
          });
        }

        if (
          !_.isEmpty(user) &&
          _.includes([...excludedRoutes, "/"], pathName)
        ) {
          router.push("/");
        }
        setLoading(false);
      });
    } else {
      setLoading(false);
    }
  }, [pathName, auth]);

  return (
    <html style={{}} lang="en">
      <UserContext.Provider value={{ user: fbUser }}>
        <body
          className={clsx("min-h-full")}
          suppressHydrationWarning={true}
          style={{
            minHeight: "100vh",
          }}
        >
          <div
            className="
                min-w-screen
                min-h-screen
          dark:bg-boxdark-2 dark:text-bodydark"
          >
            {loading ? <Loader /> : children}
          </div>
        </body>
      </UserContext.Provider>
    </html>
  );
}
