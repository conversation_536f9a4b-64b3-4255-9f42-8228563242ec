"use client";

import DefaultLayout from "../../components/Layouts/DefaultLayout";
import { useEffect, useState } from "react";
import { fetch } from "../../helpers";
import _ from "lodash";
import { clsx } from "clsx";
import { useRouter } from "next/navigation";
import moment from "moment";

const PackagesPage = () => {
  return (
    <DefaultLayout>
      <div className={clsx("font-semibold", "capitalize", "text-2xl")}>
        Packages
      </div>
      <AddPackageButton />
      <Table />
    </DefaultLayout>
  );
};

const Table = () => {
  const [packages, setPackages] = useState([]);

  useEffect(() => {
    console.log("table");
    getPackages();
  }, []);

  const getPackages = async () => {
    await fetch("/packages")
      .then(async (res) => {
        const data = await res.data;
        console.log("packages:", data);
        setPackages(data);
      })
      .catch((err) => {
        console.log("error when getting packages:", err);
      });
  };

  return (
    <table className={"table mt-4 bg-white"}>
      {/*head*/}
      <thead>
        <tr>
          <th>Name</th>
          <th>Service Type</th>
          <th>Pricing (RM)</th>
          <th>Created At</th>
        </tr>
      </thead>
      <tbody>
        {_.map(packages, (pkg) => (
          <tr>
            <td className={"capitalize"}>
              {!_.isEmpty(pkg?.name) ? pkg.name : "N/A"}
            </td>
            <td className={"capitalize"}>
              {!_.isEmpty(pkg?.serviceType) ? pkg.serviceType : "N/A"}
            </td>
            <td>{pkg.pricing.toFixed(2)}</td>
            <td>{moment(pkg.createdAt).format("lll")}</td>
          </tr>
        ))}
      </tbody>
    </table>
  );
};

const AddPackageButton = () => {
  const router = useRouter();

  return (
    <div className={clsx("flex", "justify-end")}>
      <button
        onClick={() => window.location.href = "/packages/add"}
        className={clsx(
          "bg-primary",
          "text-white",
          "rounded-md",
          "px-6",
          "py-2",
          "mt-1",
        )}
      >
        Create Package
      </button>
    </div>
  );
};

export default PackagesPage;
