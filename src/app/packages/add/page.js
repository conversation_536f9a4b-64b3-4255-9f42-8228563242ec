"use client";

import DefaultLayout from "../../../components/Layouts/DefaultLayout";
import Input from "../../../components/Input/Input";
import { clsx } from "clsx";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { fetch } from "../../../helpers";
import Alert from "../../../components/Alert/Alert";
import AddPackageButton from "../../../components/AddPackagePage/AddPackageButton";
import AddProducts from "../../../components/AddPackagePage/AddProducts";

const AddPackagePage = () => {
  const [packageObj, setPackageObj] = useState({
    serviceType: "tint",
    carType: "Sedan",
  });
  const [showAlert, setShowAlert] = useState(false);

  useEffect(() => {
    console.log("monitor package obj:", packageObj);
  }, [packageObj]);

  const createPackage = async () => {
    await fetch("/package", "post", packageObj)
      .then(async (res) => {
        const data = await res.data;
        console.log("created package:", data);
        setShowAlert(true);
      })
      .catch((err) => {
        console.log("error when creating package:", err);
      });
  };

  return (
    <DefaultLayout>
      <div className={clsx("font-semibold", "capitalize", "text-2xl")}>
        Add Package
      </div>
      <div className={"mt-4 rounded-lg bg-white p-4"}>
        <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
          <div className={"w-full sm:m-2"}>
            <Input
              returnedValue={(value) => {
                setPackageObj((prev) => ({
                  ...prev,
                  name: value,
                }));
              }}
              inputField={{
                name: "Name",
                value: packageObj?.name,
              }}
            />
          </div>
          {/* service type start*/}
          <div className={"w-full sm:m-2"}>
            <Input
              returnedValue={(value) => {
                setPackageObj((prev) => ({
                  ...prev,
                  serviceType: value,
                }));
              }}
              inputField={{
                name: "Service Type",
                type: "select",
                options: [
                  {
                    name: "Tint",
                    value: "tint",
                  },
                  {
                    name: "Coating",
                    value: "coating",
                  },
                ],
                value: packageObj?.serviceType,
              }}
            />
          </div>
          {/* service type end */}
          {/* vehicle type start*/}
          <div className={"w-full sm:m-2"}>
            <Input
              returnedValue={(value) => {
                setPackageObj((prev) => ({
                  ...prev,
                  carType: value,
                }));
              }}
              inputField={{
                name: "Car Type",
                type: "select",
                options: [
                  {
                    name: "Sedan",
                    value: "Sedan",
                  },
                  {
                    name: "SUV",
                    value: "SUV",
                  },
                  {
                    name: "MPV",
                    value: "MPV",
                  },
                ],
                value: packageObj?.carType,
              }}
            />
          </div>
          {/* vehicle type end */}
        </div>
        <AddProducts
          returnedValue={(products) => {
            setPackageObj((prev) => ({
              ...prev,
              products,
            }));
          }}
        />
        <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
          <div className={"w-full sm:m-2"}>
            <Input
              returnedValue={(value) => {
                setPackageObj((prev) => ({
                  ...prev,
                  pricing: value,
                }));
              }}
              value={packageObj?.pricing}
              inputField={{
                name: "Pricing",
                type: "number",
              }}
            />
          </div>
        </div>
      </div>
      <AddPackageButton
        onClick={() => {
          createPackage();
        }}
      />
      {showAlert && <Alert text={"Your package has been created!"} />}
    </DefaultLayout>
  );
};

export default AddPackagePage;
