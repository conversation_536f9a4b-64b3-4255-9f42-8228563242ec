"use client";

import DefaultLayout from "../../components/Layouts/DefaultLayout";
import { useEffect, useState } from "react";
import { fetch } from "../../helpers";
import _ from "lodash";
import { clsx } from "clsx";
import moment from "moment";
import { IoEyeOutline } from "react-icons/io5";


const ReviewsPage = () => {
	const [orders, setOrders] = useState([]);

	useEffect(() => {
		getReviews();
	}, []);

	const getReviews = async () => {
		await fetch("/reviews")
			.then(async (res) => {
				const data = await res.data;
				setOrders(data);
				console.log("reviews:", data);
			})
			.catch((err) => {
				console.log("error when getting reviews:", err);
			});
	};

	return (
		<DefaultLayout>
			<div>
				<div className={clsx("font-semibold", "capitalize", "text-2xl")}>
					Reviews
				</div>
				<ReviewsTable orders={orders} />
			</div>
		</DefaultLayout>
	);
};


const ReviewsTable = ({ orders }) => {
	return (
		<div className="overflow-x-scroll">
			<table className={"table mt-4 bg-white"}>
				{/* head */}
				<thead>
					<tr>
						<th>Review</th>
						<th>Reviewer</th>
						<th>Score</th>
						<th>Date</th>
						<th>Actions</th>
					</tr>
				</thead>
				<tbody>
					{_.map(orders, (order) => (
						<tr key={order.review._id}>
							<td className={""}>{_.get(order.review, 'review', order.review)}</td>
							<td>{_.get(order.review, 'by', order.review)}</td>
							<td>{_.get(order.review, 'score', order.review)}</td>
							<td>{moment(order.review.date).format("lll")}</td>
							<td className={clsx("capitalize", "flex", "justify-between")}>
								<button
									onClick={() => {
										window.location.href = `/sales/${order.orderId}`; // Updated to navigate to the order page
									}}
									className="btn btn-ghost p-0"
								>
									<IoEyeOutline style={{ fontSize: 20 }} />
								</button>
							</td>
						</tr>
					))}
				</tbody>
			</table>
		</div>
	);
};

export default ReviewsPage; 