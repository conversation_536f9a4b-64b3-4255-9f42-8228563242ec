"use client";

import DefaultLayout from "../../components/Layouts/DefaultLayout";
import { useRouter } from "next/navigation";
import { clsx } from "clsx";
import { useEffect, useState } from "react";
import _ from "lodash";
import moment from "moment";
import { IoEyeOutline } from "react-icons/io5";
import { fetch } from "../../helpers";

const StaffKPIPage = () => {
  const [staffKPI, setStaffKPI] = useState([]);

  useEffect(() => {
    getStaffKPI();
  }, []);

  useEffect(() => {
    console.log("monitor staff kpi:", staffKPI);
  }, [staffKPI]);

  const getStaffKPI = async () => {
    await fetch("/staff-kpi")
      .then(async (res) => {
        const data = await res.data;
        setStaffKPI(data);
        console.log("staff kpis:", data);
      })
      .catch((err) => {
        console.log("error when getting staff kpi:", err);
      });
  };

  return (
    <DefaultLayout>
      <div>
        <div className={clsx("font-semibold", "capitalize", "text-2xl")}>
          Staff KPI
        </div>
        <AddStaffKPIButton />
        <StaffKPITable staffKPI={staffKPI} />
      </div>
    </DefaultLayout>
  );
};

const AddStaffKPIButton = () => {
  const router = useRouter();

  return (
    <div className={clsx("flex", "justify-end")}>
      <button
        onClick={() => router.push("/staff-kpi/add")}
        className={clsx(
          "bg-primary",
          "text-white",
          "rounded-md",
          "px-6",
          "py-2",
          "mt-1",
        )}
      >
        Add Staff KPI
      </button>
    </div>
  );
};

const StaffKPITable = ({ staffKPI }) => {
  return (
    <div className="overflow-x-scroll">
      <table className={"table mt-4 bg-white"}>
        {/* head */}
        <thead>
          <tr>
            <th>Salesman</th>
            <th>Month</th>
            <th>KPI (RM)</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {_.map(staffKPI, (sKPI) => (
            <tr>
              <td>
                <div className="flex items-center gap-3">
                  <div>
                    <div className="font-bold capitalize">
                      {sKPI?.staff?.name}
                    </div>
                    <div className="text-sm opacity-50">VB Motor</div>
                  </div>
                </div>
              </td>
              <td className={"capitalize"}>
                {moment().month(sKPI?.month).format("MMM")}
              </td>
              <td className={"capitalize"}>{sKPI?.kpi}</td>
              <td className={clsx("capitalize", "flex", "justify-between")}>
                <button
                  onClick={() => {
                    window.location.href = `/staff-kpi/${sKPI?._id}`;
                  }}
                  className="btn btn-ghost p-0"
                >
                  <IoEyeOutline style={{ fontSize: 20 }} />
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default StaffKPIPage;
