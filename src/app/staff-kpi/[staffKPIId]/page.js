"use client";

import DefaultLayout from "../../../components/Layouts/DefaultLayout";
import BackButton from "../../../components/BackButton/BackButton";
import { clsx } from "clsx";
import _ from "lodash";
import { useEffect, useState } from "react";
import { fetch } from "../../../helpers/index";
import { useParams, useRouter } from "next/navigation";
import Alert from "../../../components/Alert/Alert";
import Input from "../../../components/Input/Input";

const EditStaffKPIPage = ({}) => {
  const [showAlert, setShowAlert] = useState(false);
  const router = useRouter();
  const { staffKPIId } = useParams();

  const [staffKPI, setStaffKPI] = useState({});

  // set car model
  useEffect(() => {
    getStaffKPI();
  }, []);

  const getStaffKPI = async () => {
    await fetch(`/staff-kpi?staffKPIId=${staffKPIId}`)
      .then(async (res) => {
        const data = await res.data;
        console.log("data:", data);
        setStaffKPI({ ...data[0] });
      })
      .catch(async (err) => {
        console.log("error while getting staff kpi:", err);
      });
  };

  const updateStaffKPI = async () => {
    await fetch("/staff-kpi", "put", {
      staffKPIId,
      staffId: staffKPI?.staff?._id,
      kpi: staffKPI?.kpi,
    })
      .then(async (res) => {
        console.log("staff kpi updated:", await res);
        setShowAlert(true);
        setTimeout(() => {
          setShowAlert(false);
          router.refresh();
        }, 5000);
      })
      .catch((err) => {
        console.log("error when update staff kpi:", err);
      });
  };

  // monitor staff kpi object
  useEffect(() => {
    console.log("staff kpi:", staffKPI);
  }, [staffKPI]);

  return (
    <DefaultLayout>
      <BackButton />
      <div className={clsx("font-semibold", "capitalize", "text-2xl")}>
        Update Staff KPI
      </div>
      <div className={"mt-4 rounded-lg bg-white p-4"}>
        <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
          <div className={"w-full sm:m-2"}>
            <Input
              returnedValue={(value) => {
                setStaffKPI((prev) => ({
                  ...prev,
                  name: value,
                }));
              }}
              disabled={true}
              inputField={{
                name: "Name",
                value: staffKPI?.staff?.name,
              }}
            />
          </div>
          <div className={"w-full sm:m-2"}>
            <Input
              returnedValue={(value) => {
                setStaffKPI((prev) => ({
                  ...prev,
                  kpi: value,
                }));
              }}
              value={staffKPI?.kpi}
              inputField={{
                name: "KPI (RM)",
                type: "number",
              }}
            />
          </div>
        </div>
      </div>
      <UpdateStaffKPIButton onClick={() => updateStaffKPI()} />
      {showAlert && <Alert text={"Your staff KPI has been updated!"} />}
    </DefaultLayout>
  );
};

const UpdateStaffKPIButton = ({ onClick }) => {
  return (
    <div className={"flex w-full justify-end"}>
      <button
        onClick={onClick}
        className={clsx(
          "btn",
          "bg-primary",
          "text-white",
          "px-6",
          "py-2",
          "mt-4",
        )}
      >
        Update Staff KPI
      </button>
    </div>
  );
};

export default EditStaffKPIPage;
