"use client";

import DefaultLayout from "../../../components/Layouts/DefaultLayout";
import Input from "../../../components/Input/Input";
import { clsx } from "clsx";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { fetch } from "../../../helpers";
import Alert from "../../../components/Alert/Alert";

const CreateStaffKPIPage = () => {
  const [staffKPI, setStaffKPI] = useState({
    staffId: "",
    kpi: "",
    rate: "", // Added rate field
  });
  const [users, setUsers] = useState([]);
  const [showAlert, setShowAlert] = useState(false);

  // init
  useEffect(() => {
    getUsers();
  }, []);

  // value monitor
  useEffect(() => {
    console.log("monitor staff kpi:", staffKPI);
  }, [staffKPI]);

  const getUsers = async () => {
    await fetch("/users")
      .then(async (res) => {
        const data = await res.data;
        console.log("users:", data);
        setUsers(data);
        setStaffKPI((prev) => ({
          ...prev,
          staffId: data[0]?._id,
        }));
      })
      .catch((err) => {
        console.log("error when getting users:", err);
      });
  };

  const createStaffKPI = async () => {
    await fetch("/staff-kpi", "post", staffKPI)
      .then(async (res) => {
        const data = await res.data;
        console.log("created staff kpi:", data);

        setShowAlert(true);
      })
      .catch((err) => {
        console.log("error when creating staff kpi:", err);
      });
  };

  return (
    <DefaultLayout>
      <div className={clsx("font-semibold", "capitalize", "text-2xl")}>
        Create Staff KPI
      </div>
      <div className={"mt-4 rounded-lg bg-white p-4"}>
        <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
          <div className={"w-full sm:m-2"}>
            <Input
              returnedValue={(value) => {
                setStaffKPI((prev) => ({
                  ...prev,
                  staffId: value,
                }));
              }}
              inputField={{
                name: "Name",
                type: "select",
                options: _.map(users, (user) => ({
                  name:
                    user?.name.charAt(0).toUpperCase() +
                    String(user?.name).slice(1),
                  value: user._id,
                })),
              }}
            />
          </div>
          <div className={"w-full sm:m-2"}>
            <Input
              returnedValue={(value) => {
                setStaffKPI((prev) => ({
                  ...prev,
                  kpi: value,
                }));
              }}
              value={staffKPI?.kpi}
              inputField={{
                name: "KPI (RM)",
                type: "number",
              }}
            />
          </div>
          <div className={"w-full sm:m-2"}>
            <Input
              returnedValue={(value) => {
                setStaffKPI((prev) => ({
                  ...prev,
                  rate: value, // Handle rate input
                }));
              }}
              value={staffKPI?.rate}
              inputField={{
                name: "Rate (%)",
                type: "number",
              }}
            />
          </div>
        </div>
      </div>
      <CreateStaffKPIButton
        onClick={() => {
          createStaffKPI();
        }}
      />
      {showAlert && <Alert text={"Your staff KPI has been created!"} />}
    </DefaultLayout>
  );
};

const CreateStaffKPIButton = ({ onClick }) => {
  const router = useRouter();

  return (
    <div className={clsx("flex", "justify-end", "mt-4")}>
      <button
        onClick={onClick}
        className={clsx(
          "bg-primary",
          "text-white",
          "rounded-md",
          "px-6",
          "py-2",
          "mt-1",
          "w-40",
        )}
      >
        Create KPI
      </button>
    </div>
  );
};

export default CreateStaffKPIPage;
