const { asyncJoiValidate } = require('#helpers/index');
const { getAttendanceRecords } = require('#services/attendance.service');
const StatisticService = require('#services/statistic.service');
const StatisticDTO = require('#dtos/statistic.dto');
const AccessModel = require('#models/Access.model');
const { Aggregate, default: mongoose } = require('mongoose');
const _ = require('lodash');
const UserModel = require('#models/User.model');

exports.getDashboardData = async (req, res, next) => {
  try {
    const response = await StatisticService.getDashboardData(
      req.params.premiseId,
    );
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.getAttendanceRecords = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate(
      req.query,
      StatisticDTO.getAttendanceRecords,
    );
    const response = await getAttendanceRecords(params);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.getAttendanceRates = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate(
      req.query,
      StatisticDTO.getAttendanceRates,
    );
    const response = await StatisticService.getAttendanceRates(params);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.getScanStatistic = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate(
      req.query,
      StatisticDTO.getScanStatistic,
    );
    const response = await StatisticService.getScanStatistic(params);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.removeUsersAndAccessDorm360 = async (req, res, next) => {
  try {
    const dorm360id = '654c931699313cac33e46652';
    const listId = [];
    const response = await AccessModel.find({ premiseId: dorm360id, isDeleted: true }).lean();
    _.map(response, (doc) => {
      listId.push(doc.uid);
    });
    const response2 = await UserModel.updateMany(
      {
        _id: { $in: listId },
      },
      {
        $set: { isDeleted: true },
      },
      {
        multi: true,
      },
    );

    return res.success({
      ids: listId,
      response1L: response.length,
      // response2L: response2.length,
      // response1: response,
      response2,
    });
  } catch (error) {
    return next(error);
  }
};
