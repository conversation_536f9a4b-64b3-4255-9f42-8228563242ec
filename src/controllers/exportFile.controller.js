// const functions = require('firebase-functions');
// const admin = require('firebase-admin');
// const cors = require('cors')({ origin: true });

// const db = admin.firestore();
// const os = require('os');
// const path = require('path');
// const moment = require('moment-timezone');
// const _ = require('lodash');
// const JSZip = require('jszip');
// const { PerformanceObserver, performance } = require('perf_hooks');
// const { createByEmployeeData } = require('../exportAttendanceExcel');
// const { exportAllByDayHilton } = require('./exportHiltonExcel');
// const { parseHiltonData } = require('./parseHilton');
// const { parseFirstLast } = require('./parseFirstLast');

// moment.tz.setDefault('Asia/Kuala_Lumpur');

// const runtimeOpts = {
//   timeoutSeconds: 540,
//   memory: '2GB',
// };

// // 🚩 check all collection and doc names again before putting into production code
// exports.scheduledCachingScans = async (context) => { // This will run on 12am everyday
//   const startDay = moment().subtract(1, 'hour').startOf('day').toDate(); // subtract 1 hour to get previous day
//   const endDay = moment().subtract(1, 'hour').endOf('day').toDate();
//   const scanData = [];
//   const scheduledInfoList = [];

//   const promiseScans = db.collection('facePassScans')
//     .where('createdAt', '>=', startDay)
//     .where('createdAt', '<=', endDay)
//     .get();

//   const scheduleReruns = [];
//   await db.collection('scheduling').where('done', '==', false).get().then((snaps) => {
//     // console.log(snaps.size)
//     snaps.forEach((doc) => {
//       const scan = doc.data();
//       const premiseID = _.get(scan, 'premise');
//       const reRunDateStart = moment.unix(_.get(scan, 'date._seconds')).startOf('day').toDate();
//       const reRunDateEnd = moment.unix(_.get(scan, 'date._seconds')).endOf('day').toDate();
//       scheduledInfoList.push({
//         date: reRunDateStart,
//         premise: premiseID,
//       });
//       const fetchRef = db.collection('facePassScans')
//         .where('createdAt', '>=', reRunDateStart)
//         .where('createdAt', '<=', reRunDateEnd)
//         .where('premiseId', '==', premiseID)
//         .get();
//       scheduleReruns.push(fetchRef);
//       db.collection('scheduling').doc(doc.id).update({
//         done: true,
//       });
//     });
//   });
//   const concatQueries = _.concat(scheduleReruns, promiseScans);
//   const rerunSnapshot = await Promise.all(concatQueries);

//   rerunSnapshot.forEach((collectionSnaps) => {
//     collectionSnaps.forEach((doc) => {
//       if (doc.data().uid) {
//         scanData.push({
//           createdAt: doc.data().createdAt,
//           uid: doc.data().uid,
//           name: doc.data().name,
//           temperature: doc.data().temperature,
//           id: doc.data().id,
//           checkType: doc.data().checkType,
//           premiseId: doc.data().premiseId,
//         });
//       }
//     });
//   });
//   // return 'done'
//   // const cacheStructure =
//   await parseAllScansByEmployee(scanData, scheduledInfoList, _.size(scheduleReruns) > 0);
//   // console.log('size of ')
//   // return cacheStructure;
//   return null;
// };

// const parseAllScansByEmployee = async (rawScans, scheduledInfoList, hasSchedule) => {
//   let cloneScans = _.cloneDeep(rawScans);
//   const scheduledDates = [];
//   const yesterdayDate = moment().subtract(1, 'day').startOf('day').toDate();
//   // const yesterdayDate = moment().startOf('day').toate();
//   scheduledDates.push(yesterdayDate);
//   if (hasSchedule) {
//     _.forEach(scheduledInfoList, (obj) => {
//       const dateMoment = moment(obj.date).toDate();
//       scheduledDates.push(dateMoment);
//     }); // console.log('cloneScans size', _.size(cloneScans))
//   } // console.log(`scheduled dates: ${scheduledDates}`)
//   const premiseList = [];
//   // const docCollectionUnsorted = [];
//   while (_.size(cloneScans) >= 1) {
//     const getPremise = _.get(cloneScans[0], 'premiseId'); // console.log('getpremise check: ', getPremise)
//     premiseList.push(getPremise);
//     cloneScans = _.filter(_.cloneDeep(cloneScans), (scans) => {
//       return !(scans.premiseId === getPremise);
//     }); // console.log('cloneSize after filter', _.size(cloneScans))
//   } // console.log('---PREMISE LIST: ', premiseList)
//   _.map(premiseList, async (premId) => { // console.log(`new premise: ${premId}`);
//     _.map(scheduledDates, (dateObj, indexDate) => {
//       const tempUids = []; // console.log(dateObj);
//       const dateFormat = moment(dateObj).format('DD-MM-YYYY');// console.log(dateFormat)
//       _.map(rawScans, (scanDoc, indexScan) => {
//         const uid = _.get(scanDoc, 'uid');
//         const newUserDoc1Day = {
//           uid,
//           name: _.get(scanDoc, 'name'),
//         };
//         if (scanDoc.premiseId === premId && !_.includes(tempUids, uid)) {
//           const newFilterScans = _.filter(_.cloneDeep(rawScans), (scanData) => {
//             return _.get(scanData, 'uid', false) === uid
//                 && _.get(scanData, 'premiseId', false) === premId
//                 && moment(dateObj).isSame(moment.unix(scanData.createdAt._seconds), 'day');
//           });
//           if (_.size(newFilterScans) > 0) {
//             _.set(newUserDoc1Day, 'scans', newFilterScans);
//             const writeToCache = db.collection('cache2').doc(premId).collection(uid).doc(dateFormat)
//               .set(newUserDoc1Day)
//               .then(() => {
//               })
//               .catch((err) => console.log(err));
//               // writePromises.push(writeToCache)
//             writeToCache.then(() => {
//               // console.log(`size of ${uid} in ${premId}: ${_.size(newFilterScans)}`)
//             }).catch((err) => console.log(err));
//             // docCollectionUnsorted.push(newUserDoc1Day);
//             tempUids.push(uid);
//           }
//         }
//       });
//     });
//   });
//   return null; // cache operation done
// };

// exports.exportAttendanceData = async (request, response) => {
//   cors(request, response, async () => {
//     try {
//       const {
//         start,
//         end,
//         type,
//         excelOption,
//         premise,
//         selectedDepartment,
//         employeeList,
//         premiseFullName,
//       } = request.body.data;

//       console.log('starting performance');
//       const begin = performance.now();

//       const startDate = moment.unix(start).startOf('day').toDate();
//       const endDate = moment.unix(end).add(1, 'day').endOf('day').toDate();
//       const unixAddEndDate = moment.unix(end).add(1, 'day').endOf('day').unix();

//       const listOfIds = [];
//       let filebuffer = null;

//       _.forEach(employeeList, (rowObj) => {
//         listOfIds.push(rowObj.userId);
//       });

//       // // TEST LINE REMOVE BEFORE PUBLISH
//       // const rawInfo = await getbyDate_PremiseID(startDate, endDate, premise, listOfIds);
//       // console.log('SIZE OF SCANS', _.size(rawInfo));
//       // // TEST LINE REMOVE BEFORE PUBLISH
//       // // return response.status(200).send({ data: rawInfo });

//       const scanData = await getRawScans(excelOption, startDate, endDate, premise, type, listOfIds, selectedDepartment); // THIS IS WAS FETCHING FROM RAWSCANS
//       // const duration = performance.now() - begin;
//       // console.log('Duration of get', duration / 1000);
//       console.log('SIZE OF SCANS', _.size(scanData));
//       // 🚧 Remove this line below jut a test //
//       // return response.status(200).send({ data: scanData });

//       if (excelOption === 'free') {
//         const parseFirstLastOnly = parseFirstLast(scanData, startDate, endDate, employeeList);
//         // return response.status(200).send({ data: parseFirstLastOnly });
//         // uses the same excel format as hilton
//         const workBook = exportAllByDayHilton(parseFirstLastOnly, start, end, premiseFullName);
//         filebuffer = await workBook.xlsx.writeBuffer();
//         response.set({
//           'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
//           'Content-Disposition': 'attachment;',
//         });
//         return response.status(200).send({ data: filebuffer });
//       }

//       if (excelOption === 'type1') {
//         const parsedDataToHilton = parseHiltonData(scanData, startDate, endDate, employeeList);
//         // return response.status(200).send({data: parsedDataToHilton});
//         const workBook = exportAllByDayHilton(parsedDataToHilton, start, end, premiseFullName);
//         filebuffer = await workBook.xlsx.writeBuffer();
//         response.set({
//           'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
//           'Content-Disposition': 'attachment;',
//         });
//         return response.status(200).send({ data: filebuffer });
//       }

//       const premiseInfo = getPremiseData(premise);
//       const promisesScansShifsHolidays = [scanData, premiseInfo];
//       const bothData = await Promise.all(promisesScansShifsHolidays).then((res) => res);
//       const parsedData = parseData(bothData[0], employeeList, start, end, bothData[1]);
//       const headers = generateHeaders(employeeList, start, end, premise);

//       const zip = new JSZip();
//       if (_.size(listOfIds) > 1) {
//         const promises = [];
//         _.map(headers, async (headerData, index) => {
//           // console.log(headerData.staffName)
//           promises.push(loopBuffer(headerData, parsedData, index));
//         });

//         await Promise.all(promises).then((files) => {
//           _.forEach(files, (buffer, idx) => {
//             const fileName = `${headers[idx].staffName}-${headers[idx].reportType}-attendance.xlsx`;
//             zip.file(fileName, buffer, { binary: true, date: new Date() });
//           });
//         });

//         filebuffer = await zip.generateAsync({ type: 'nodebuffer' });
//         response.set({
//           'Content-Type': 'application/zip',
//           'Content-Disposition': 'attachment; filename=Employees.zip',
//           // 'Content-Length': filebuffer.length,
//         });
//       } else {
//         console.log('USING THE SECOND GENERATING FUNCTION');
//         const fileName = `${headers[0].staffName}-${headers[0].reportType}-attendance.xlsx`;
//         const workBook = createByEmployeeData(headers[0], parsedData[0]);
//         filebuffer = await workBook.xlsx.writeBuffer();
//         response.set({
//           'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
//           'Content-Disposition': `attachment; filename=${fileName}.xlsx`,
//         });
//       }
//       // TODO: its better to upload to bucket and download from there
//       return response.status(200).send({ data: filebuffer });
//     } catch (error) {
//       console.log(error);
//       return response.status(500).send(error);
//     }
//   });
// };

// const getPremiseData = async (prem) => {
//   return await db.collection('premises').doc(prem).get().then((doc) => {
//     return {
//       shifts: _.get(doc.data(), 'weeklyShifts'),
//       holidays: _.get(doc.data(), 'publicHolidays'),
//     };
//   });
// };

// const loopBuffer = async (headerData, parsedData, idx) => {
//   const workBook = createByEmployeeData(headerData, parsedData[idx]);
//   return workBook.xlsx.writeBuffer();
// };

// const generateHeaders = (dataRow, start, end, premise) => {
//   const headersList = [];
//   _.forEach(dataRow, (row) => {
//     const header = {
//       reportType: 'Hourly',
//       // companyName: 'ArX',
//       staffName: row.fullName,
//       staffId: row.icNo,
//       department: parseDepartments(row.departments || {}),
//       project: '',
//       location: premise,
//       dateStart: moment.unix(start).format('DD/MM/YYYY'),
//       dateEnd: moment.unix(end).format('DD/MM/YYYY'),
//     };
//     // Process data and fill header
//     headersList.push(header);
//   });
//   return headersList;
// };

// const parseDepartments = (departmentObj) => {
//   let departmentString = '';
//   if (_.isEmpty(departmentObj)) {
//     return departmentString;
//   }
//   _.forOwn(departmentObj, (value, key) => {
//     const deptName = _.split(value, '/', 2)[1];
//     departmentString += `${deptName}, `;
//   });
//   console.log(departmentString);
//   return departmentString;
// };

// const parseData = (rawData, userList, start, end, shiftHolidays) => {
//   const arrayOfProcessedDataArray = [];
//   _.forEach(userList, (dataRow) => {
//     const arrayOfSession = [];
//     const filteredEmployee = _.filter(rawData, (scan) => {
//       return scan.uid === dataRow.userId;
//     });
//     // console.log(filterEmployee.length)
//     const parsedScans = parseEachDay(start, end, filteredEmployee, shiftHolidays);
//     arrayOfProcessedDataArray.push(parsedScans);
//   });
//   return arrayOfProcessedDataArray;
// };

// const parseEachDay = (start, end, scans, shiftHolidays) => {
//   const objAllScanByDay = [];
//   for (
//     let dstarting = moment.unix(start);
//     dstarting.diff(moment.unix(end), 'days') <= 0;
//     dstarting.add(1, 'days')) {
//     // console.log(dstarting.format('YYYY-MM-DD'));
//     const todayScans = _.filter(scans, (scan) => {
//       const scanDate = moment.unix(scan.createdAt._seconds);
//       return scanDate.isSame(dstarting, 'day');
//     });
//     const session1 = {}; // checkIn, lunchOut
//     const session2 = {}; // lunchIn, checkOut
//     const session3 = {}; // overTimeIn, overTimeOut
//     const session4 = {};
//     const session5 = {};
//     const todaysChecks = [];
//     _.forEach(todayScans, (data) => {
//       // todaysChecks.push(`${data.scanType}-${moment.unix(data.createdAt._seconds).utc(true).toDate()}`)
//       // const ses1In = []
//       if (data.scanType === 'checkIn') {
//         _.set(session1, 'in', moment.unix(data.createdAt._seconds).utc(true).toDate());
//       }
//       if (data.scanType === 'lunchOut') {
//         _.set(session1, 'out', moment.unix(data.createdAt._seconds).utc(true).toDate());
//       }
//       if (data.scanType === 'lunchIn') {
//         _.set(session2, 'in', moment.unix(data.createdAt._seconds).utc(true).toDate());
//       }
//       if (data.scanType === 'checkOut') {
//         _.set(session2, 'out', moment.unix(data.createdAt._seconds).utc(true).toDate());
//       }
//       if (data.scanType === 'overtimeIn' || data.scanType === 'overtimeOut') {
//         // TODO: confirm again if this is right
//         // By default the first overtime, session 3 will be from 5 - 8 pm
//         // Session 4 : 8pm to 10pm.
//         // Session 5 : After 10pm.
//         const recordAfter8pm = moment.unix(data.createdAt._seconds).utc(true).startOf('day').add(20, 'hours');
//         const recordAfter10pm = moment.unix(data.createdAt._seconds).utc(true).startOf('day').add(22, 'hours');
//         console.log(`check 8pm 10pm: ${recordAfter8pm.format('hh:mmA')}, ${recordAfter10pm.format('hh:mmA')} `);
//         const timeAsDate = moment.unix(data.createdAt._seconds).utc(true).toDate();
//         if (
//           moment.unix(data.createdAt._seconds).utc(true).isAfter(recordAfter8pm)
//             && moment.unix(data.createdAt._seconds).utc(true).isBefore(recordAfter10pm)
//         ) {
//           data.scanType === 'overtimeIn'
//             ? _.set(session4, 'in', timeAsDate)
//             : _.set(session4, 'out', timeAsDate);
//         } else if (moment.unix(data.createdAt._seconds).utc(true).isSameOrAfter(recordAfter10pm)) {
//           data.scanType === 'overtimeIn'
//             ? _.set(session5, 'in', timeAsDate)
//             : _.set(session5, 'out', timeAsDate);
//         } else {
//           data.scanType === 'overtimeIn'
//             ? _.set(session3, 'in', timeAsDate)
//             : _.set(session3, 'out', timeAsDate);
//         }
//       }
//     });
//     todaysChecks.push(session1);
//     todaysChecks.push(session2);
//     todaysChecks.push(session3);
//     todaysChecks.push(session4);
//     todaysChecks.push(session5);

//     objAllScanByDay.push({
//       date: moment(dstarting).format('DD-MM-YYYY'),
//       type: determineIfWorkDay(dstarting, shiftHolidays),
//       sessions: todaysChecks,
//     });
//     // _.set(objAllScanByDay, moment(dstarting).format('DD-MM-YYYY'), todaysChecks)
//   }
//   return objAllScanByDay;
// };

// const determineIfWorkDay = (date, shiftHolidays) => {
//   // shiftHolidays: {shifts: Array(7), holidays: Array(3)}
//   const shifts = _.get(shiftHolidays, 'shifts');
//   const holidays = _.get(shiftHolidays, 'holidays');

//   const dayOfWeek = _.find(shifts, (d) => {
//     if (_.capitalize(d.day) === moment(date).format('dddd')) {
//       return true;
//     }
//   });
//   if (!_.get(dayOfWeek, 'isActive')) {
//     // console.log(`RestDay: ${moment(date).format('LL')}`)
//     return 'RD';
//   }

//   const isHoliday = _.find(holidays, (h) => {
//     // console.log(moment(h.date).format('LL'))
//     // console.log(`--This Date: ${moment(date).format('LL')}`)
//     if (moment(h.date).isSame(moment(date), 'date')) {
//       console.log(`${moment(date).format('LL')} is Holiday`);
//       return true;
//     }
//   });
//   console.log(isHoliday);
//   if (!_.isNil(isHoliday)) {
//     return 'RD';
//   }
//   return 'WD';
// };

// const getRawScans = async (option, startDate, endDate, premise, type, userIdList, departmentId) => {
//   const collectionName = 'facePassScans';
//   // console.log(premise)
//   // const startDate = moment.unix(start).startOf('day').toDate()
//   // const endDate = moment.unix(end).endOf('day').toDate()
//   const allRefs = [];
//   const scans = [];
//   console.log('CHECKING SIZE OF USERS LIST:', _.size(userIdList));

//   if (option === 'free') {
//     _.forEach(userIdList, (id) => {
//       const promise = db.collection(collectionName)
//         .where('uid', '==', id)
//         .where('premiseId', '==', premise)
//         .where('createdAt', '>=', startDate)
//         .where('createdAt', '<=', endDate)
//         .get();
//       allRefs.push(promise);
//     });
//   } else {
//     _.forEach(userIdList, (id) => {
//       const promise = db.collection(collectionName)
//         .where('uid', '==', id)
//         .where('checkType', 'in', ['in', 'out'])
//         .where('premiseId', '==', premise)
//         .where('createdAt', '>=', startDate)
//         .where('createdAt', '<=', endDate)
//         .get();
//       allRefs.push(promise);
//     });
//   }

//   const collectionDataSnapshot = await Promise.all(allRefs);
//   collectionDataSnapshot.forEach((querySnapshot) => {
//     querySnapshot.docs.forEach((queryDocSnap) => {
//       const scanDoc = queryDocSnap.data();
//       scans.push(scanDoc);
//     });
//   });
//   return scans;
// };

// // 🚧🚧🚧 Will be used to replace direct fetch from facepassScans but cache2 needs to be fully populated
// // and the cronjob running before switching.
// const getScansFromCache = async (rows, startDate, endDate, premise, excelOption) => {
//   const start = performance.now();
//   const aStart = moment.unix(startDate);
//   const bEnd = moment.unix(endDate).add(1, 'day');
//   const promisesList = [];
//   const rawScans = [];
//   let flattenScans = [];
//   console.log(premise);
//   console.log(rows);
//   if (_.size(rows)) {
//     while (!aStart.isSame(_.cloneDeep(bEnd), 'day')) {
//       const newDay = _.cloneDeep(aStart).format('DD-MM-YYYY');
//       _.map(rows, (uid, index) => {
//         // console.log(`${uid} - ${newDay}`);
//         const promise = db.collection('cache2').doc(premise).collection(uid).doc(newDay)
//           .get();
//         promisesList.push(promise);
//       });
//       aStart.add(1, 'day');
//     }
//     console.log(`Promise length ${_.size(promisesList)}`);
//     await Promise.all(promisesList).then((results) => {
//       results.forEach((doc) => {
//         if (doc.exists) {
//           rawScans.push(doc.data());
//         }
//       });
//     });
//     _.map(rawScans, (info, idx) => {
//       flattenScans = _.concat(flattenScans, _.get(info, 'scans'));
//     });

//     if (excelOption === 'type1') {
//       flattenScans = _.filter(_.cloneDeep(flattenScans), (scan) => {
//         return scan.checkType === 'in' || scan.checkType === 'out';
//       });
//     }
//     console.log('size of scans: ', _.size(flattenScans));
//     const duration = performance.now() - start;
//     console.log('Duration of Cache get and fill', duration / 1000);
//   }
//   return flattenScans;
// };

// // const getbyDate_PremiseID = async (start, end, premise, listOfIds) => {
// //   console.log(`Checking params: ${start}, ${end}, ${premise}`)
// //   let flattenScans = [];
// //   const rawResults = [];
// //   const promisesList = []
// //   let aStart = moment(start);
// //   const bEnd = moment(end).add(1, 'day');
// //   while (!aStart.isSame(_.cloneDeep(bEnd), 'day')) {
// //     const newDay = _.cloneDeep(aStart).format('YYYY-MM-DD');
// //     console.log(newDay);
// //     _.map(listOfIds, (uid, index) => {
// //       const promise = db.collection('facePassScans')
// //         .where('premiseId', '==', premise)
// //         .where('uid', '==', uid)
// //         .where('date', '==', newDay)
// //         .get();

// //       promisesList.push(promise);
// //     });
// //     aStart.add(1, 'day');
// //   }
// //   console.log(`Promise length ${_.size(promisesList)}`)
// //   await Promise.all(promisesList).then((results) => {
// //     results.forEach((snaps) => {
// //       snaps.forEach((doc) => {
// //         if (doc.exists) {
// //           rawResults.push(doc.data())
// //         }
// //       })
// //     })
// //   })
// //   console.log('size of getbyDate_PremiseID: ', _.size(rawResults));
// //   return rawResults
// // }

// // 🚧 THIS WAS A TEST FUNCTION FOR CACHING, not used for production
// // exports.testCronjobParse = functions.runWith(runtimeOpts).region('asia-southeast2').https.onCall(async (data, context) => {
// //   const {
// //     dateStart,
// //     dateEnd,
// //     prem,
// //     rows,
// //   } = data;

// //   // 1 0 * * * everyday at 00:01
// //   const yesterdayStart = moment().subtract(1, 'day').startOf('day')
// //   const yesterdayEnd = moment().subtract(1, 'day').endOf('day')
// //   console.log(yesterdayStart)
// //   console.log(yesterdayEnd)

// //   return 'done'

// //   const startDate = moment.unix(dateStart).startOf('day').toDate();
// //   const endDate = moment.unix(dateEnd).endOf('day').toDate();
// //   const scanData = [];
// //   const premisesId = []
// //   const promiseList = []
// //   let count = 0;

// //   console.log('start: ', moment(startDate).format('DD-MM-YYYY'));
// //   console.log('end: ', moment(endDate).format('DD-MM-YYYY'));

// //   const promisePremises = db.collection('premises').get()
// //   const promiseScans = db.collection('facePassScans').where('createdAt', '>=', startDate).where('createdAt', '<=', endDate).get()

// //   await Promise.all([promisePremises, promiseScans]).then((res) => {
// //     const premiseSnaps = res[0]
// //     const scanSnaps = res[1]
// //     premiseSnaps.forEach(doc => {
// //       premisesId.push(doc.data().id)
// //     })
// //     scanSnaps.forEach((doc) => {
// //       count += 1;
// //       if (doc.data().uid) {
// //         scanData.push({
// //           createdAt: doc.data().createdAt,
// //           uid: doc.data().uid,
// //           name: doc.data().name,
// //           temperature: doc.data().temperature,
// //           id: doc.data().id,
// //           checkType: doc.data().checkType,
// //           premiseId: doc.data().premiseId,
// //         })
// //       }
// //     })
// //   })

// //   const parsedData = await parseAllScansByEmployee(scanData, startDate, premisesId)
// //   // console.log('sizeOFEMPS: ', _.size(parsedData));
// //   console.log('sizeOfALLscans', count)
// //   console.log('size of recon scans', _.size(scanData))
// //   // return parsedData;
// //   return 'done'
// // })

// // 🚧 THIS WAS A TEST FUNCTION FOR CACHING, not used for production
// // exports.testFetchFromCache1 = functions.runWith(runtimeOpts).region('asia-southeast2').https.onCall(async (data, context) => {
// //   // console.log('A test to scans from cache');
// //   const {
// //     rows,
// //     startDate,
// //     endDate,
// //     premise,
// //     cacheType
// //   } = data;

// //   const start = performance.now();
// //   let aStart = moment.unix(startDate);
// //   const bEnd = moment.unix(endDate).add(1, 'day');
// //   const users = getUids(rows);
// //   const promisesList = [];
// //   const rawScans = [];
// //   let flattenScans = [];
// //   console.log(premise);
// //   console.log(users);

// //   if (_.size(rows) > 0) {
// //     if (cacheType === 'cache2') {
// //       console.log('----FETCHING FROM CACHE 2 ----')
// //       while (!aStart.isSame(_.cloneDeep(bEnd), 'day')) {
// //         const newDay = _.cloneDeep(aStart).format('DD-MM-YYYY')
// //         _.map(users, (uid, index) => {
// //           // console.log(`${uid} - ${newDay}`);
// //           const promise = db.collection('cache2').doc(premise).collection(uid).doc(newDay).get();
// //           promisesList.push(promise);
// //         });
// //         aStart.add(1, 'day');
// //       }
// //       console.log(`Promise length ${_.size(promisesList)}`)
// //       await Promise.all(promisesList).then((results) => {
// //         results.forEach((doc) => {
// //           if (doc.exists) {
// //             const scanDoc = doc.data();
// //             rawScans.push(scanDoc)
// //           }
// //         })
// //       })

// //       _.map(rawScans, (info, idx) => {
// //         flattenScans = _.concat(flattenScans, _.get(info, 'scans'));
// //       })
// //       console.log('size of scans: ', _.size(flattenScans));
// //       const duration = performance.now() - start;
// //       console.log('Duration of Cache get and fill', duration / 1000);

// //     } else {
// //       while (!aStart.isSame(_.cloneDeep(bEnd), 'day')) {
// //         const newDay = _.cloneDeep(aStart).format('DD-MM-YYYY')
// //         console.log(newDay);
// //         aStart.add(1, 'day');

// //         _.forEach(users, (uid) => {
// //           const promise = db.collection('cache').doc(premise).collection(newDay)
// //             .where('uid', '==', uid).get();
// //           promisesList.push(promise);
// //         })
// //       }

// //       const collectionDataSnapshot = await Promise.all(promisesList);
// //       collectionDataSnapshot.forEach(userScanDayQuery => {
// //         userScanDayQuery.docs.forEach(scan => {
// //           const scanDoc = scan.data()
// //           rawScans.push(scanDoc)
// //         })
// //       })

// //       console.log('size of scans: ', _.size(rawScans));
// //       const duration = performance.now() - start;
// //       console.log('Duration of Cache get and fill', duration / 1000);
// //     }

// //   }
// //   return flattenScans;
// // })
