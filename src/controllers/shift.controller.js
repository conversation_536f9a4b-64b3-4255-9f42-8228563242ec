// const functions = require('firebase-functions');
// const admin = require('firebase-admin');

// const db = admin.firestore();
// const _ = require('lodash');
// const moment = require('moment-timezone');
// const { checkAdminPermission } = require('./common/checkAdminPermission');

// moment.tz.setDefault('Asia/Kuala_Lumpur');

// exports.getPremiseShift = async (data, context) => {
//   try {
//     let premAllowed = [];
//     const premiseId = data.prem;
//     let holidayList = [];
//     let weeklyShift = [];
//     await checkAdminPermission(context.auth.uid).then(async (res) => {
//       premAllowed = res.allowedPremises;
//       if (_.includes(premAllowed, premiseId)) {
//         await db.collection('premises').doc(premiseId).get().then((doc) => {
//           // console.log(doc.data())
//           holidayList = doc.data().publicHolidays;
//           weeklyShift = doc.data().weeklyShifts;
//         });
//       }
//     });
//     return {
//       holidayList,
//       weeklyShift,
//     };
//   } catch (error) {
//     throw new functions.https.HttpsError('invalid-argument', error);
//   }
// };

// exports.setShiftAndPHdata = async (data, context) => {
//   await checkAdminPermission(context.auth.uid);
//   const { premise, weeklyShifts, publicHolidays } = data;

//   const promises = [];

//   if (!_.isEmpty(publicHolidays)) {
//     console.log('PUBLIC HOLIDAYS AVAILABLE');
//     promises.push(db.collection('premises').doc(premise).update({
//       publicHolidays,
//     }));
//   } else {
//     console.log('no change to PH');
//   }

//   if (!_.isEmpty(weeklyShifts)) {
//     console.log('SHIFTS DATA AVAILABLE');
//     promises.push(db.collection('premises').doc(premise).update({
//       weeklyShifts,
//     }));
//   } else {
//     console.log('no change to shifts');
//   }

//   return Promise.all(promises);
// };
