/* eslint-disable no-param-reassign */
const ExcelJS = require('exceljs');
const _ = require('lodash');
const moment = require('moment-timezone');

exports.exportAllByDayHilton = (attendanceData, startUnix, endUnix, premiseFullName) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Employee_name', {
    pageSetup: { paperSize: 9, orientation: 'landscape' },
  });

  /* Column headers */
  worksheet.getRow(5).values = [
    '',
    'Name',
    'Department',
    'Date IN',
    'Time IN',
    'Temperature Reading (\xB0C)',
    'Date OUT',
    'Time OUT',
    'Temperature Reading (\xB0C)',
  ];

  /* Define your column keys because this is what you use to insert your data according to your columns, they're column A, B, C, D respectively being idClient, Name, Tel, and Adresse.
	So, it's pretty straight forward */
  worksheet.columns = [
    { key: 'index', width: 5 },
    { key: 'name', width: 20, alignment: { vertical: 'middle' } },
    { key: 'department', width: 20, alignment: { vertical: 'middle' } },
    { key: 'dateIn', width: 15, alignment: { vertical: 'middle', horizontal: 'right' } },
    { key: 'timeIn', width: 20, alignment: { vertical: 'middle', horizontal: 'center' } },
    { key: 'tempIn', width: 16, alignment: { vertical: 'middle', horizontal: 'center', wrapText: true } },
    { key: 'dateOut', width: 15, alignment: { vertical: 'middle', horizontal: 'right' } },
    { key: 'timeOut', width: 20, alignment: { vertical: 'middle', horizontal: 'center' } },
    { key: 'tempOut', width: 16, alignment: { vertical: 'middle', horizontal: 'center', wrapText: true } },
  ];
  // const idCol = worksheet.getColumn('id');
  // const nameCol = worksheet.getColumn('B');
  const ageCol = worksheet.getColumn(3); // index starts from 1
  ageCol.eachCell({ includeEmpty: true }, (cell, rowNumber) => {
    // console.log(cell.value);
  });

  // worksheet.addRow({ id: 1, name: 'John Doe', age: 35 });
  // or
  // worksheet.addRow([2, 'Mary Sue', 22]);

  // worksheet.addRows(rows);
  _.forEach(attendanceData, (row, index) => {
    _.set(row, 'index', index + 1);
    worksheet.addRow(row);
    // console.log(worksheet.getRow())
  });

  worksheet.eachRow((row, number) => {
    row.eachCell((cell, colNumber) => {
      cell.alignment = { vertical: 'middle' };
      if (_.includes([5, 6, 8, 9], colNumber)) {
        cell.alignment = { wrapText: 'true', horizontal: 'center', vertical: 'middle' };
      }
      if (_.includes([4, 7], colNumber)) {
        cell.alignment = { horizontal: 'right', vertical: 'middle' };
      }
      if (number > 4) {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      }
    });
  });

  worksheet.getCell('B2').value = 'COMPANY';
  worksheet.getCell('B3').value = 'FROM';
  worksheet.getCell('B4').value = 'TO';

  _.map(['B2', 'B3', 'B4'], (key) => {
    worksheet.getCell(key).style = { font: { bold: true } };
    worksheet.getCell(key).alignment = { vertical: 'bottom', horizontal: 'right' };
  });

  const startDate = moment.unix(startUnix).startOf('day').format('DD/MM/YYYY');
  const endDate = moment.unix(endUnix).endOf('day').format('DD/MM/YYYY');
  worksheet.getCell('C2').value = premiseFullName;
  worksheet.getCell('C3').value = startDate;
  worksheet.getCell('C3').alignment = { vertical: 'bottom', horizontal: 'right' };
  worksheet.getCell('C4').value = endDate;
  worksheet.getCell('C4').alignment = { vertical: 'bottom', horizontal: 'right' };
  worksheet.mergeCells('A1:I1');
  worksheet.getCell('A1').value = 'WORKTIME SHEET - HOURLY';
  worksheet.getCell('A1').alignment = { vertical: 'bottom', horizontal: 'center' };

  // iterate over each cell
  // row.eachCell(function(cell, colNumber) {
  // });

  // worksheet.getCell('A1').alignment = { vertical: 'top', horizontal: 'left' };

  // BORDER
  // worksheet.getCell('A1').border = {
  // 	top: {style:'double', color: {argb:'FF00FF00'}},
  // 	left: {style:'double', color: {argb:'FF00FF00'}},
  // 	bottom: {style:'double', color: {argb:'FF00FF00'}},
  // 	right: {style:'double', color: {argb:'FF00FF00'}}
  // };

  // FILL
  // worksheet.getCell('A1').fill = {
  // 	type: 'pattern',
  // 	pattern: 'darkTrellis',
  // 	// thin
  // 	// dotted
  // 	// dashDot
  // 	// hair
  // 	// dashDotDot
  // 	// slantDashDot
  // 	// mediumDashed
  // 	// mediumDashDotDot
  // 	// mediumDashDot
  // 	// medium
  // 	// double
  // 	// thick
  // 	fgColor: { argb: 'FFFFFF00' },
  // 	bgColor: { argb: 'FF0000FF' }
  // };
  return workbook;
};
