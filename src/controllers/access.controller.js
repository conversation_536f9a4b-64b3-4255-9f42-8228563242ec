const { asyncJoiValidate } = require('#helpers/index');
const AccessService = require('#services/access.service');
const AccessDTO = require('#dtos/access.dto');
const UserDTO = require('#dtos/user.dtos');
const PremiseDTO = require('#dtos/premise.dtos');

exports.assignUserToDepartments = async (req, res, next) => {
  try {
    const data = {
      ...req.params,
      ...req.body,
    };
    const params = await asyncJoiValidate(data, AccessDTO.assignUserToDepartments);
    const response = await AccessService.assignUserToDepartments(params, req.user);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.createNewAccesses = async (req, res, next) => {
  try {
    const data = {
      ...req.params,
      ...req.body,
    };
    // console.log('🙏 CHECKING DATA', data);
    const params = await asyncJoiValidate(req.params, AccessDTO.createNewAccess);
    console.log('📖 params:', params);
    const response = await AccessService.createUserAccess(params, req.headers.authorization);
    console.log('⭕️ response', response);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.deleteUserAccessUsingAdminKey = async (req, res, next) => {
  try {
    const { authorization } = req.headers;
    console.log('req.params', req.params);
    const params = await asyncJoiValidate(req.params, UserDTO.deleteUser);
    const responses = await AccessService.deleteUserAccessWithAdminKey(params, authorization);
    return res.success(responses);
  } catch (err) {
    return next(err);
  }
};

exports.resetIsDelete = async (req, res, next) => {
  try {
    console.log('resettting things test log');
    const response = await AccessService.resetUserAllUserAccess(req.headers.authorization);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.restoreUserAccess = async (req, res, next) => {
  try {
    const { userId } = req.params;
    const params = asyncJoiValidate(req.params, AccessDTO.restoreAccess); // problem with joi not validating userid
    const response = await AccessService.restoreUserAccess(userId);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.bulkExternalAccess = (facepassServer) => async (req, res, next) => {
  try {
    const params = await asyncJoiValidate({
      ...req.body,
      premiseId: req.body.premiseID,
      apiKey: req.headers.authorization,
    }, AccessDTO.bulkExternalAccess);
    const response = await AccessService.bulkExternalAccess(params, facepassServer);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.createNewAccessesByBody = (facepassServer) => async (req, res, next) => {
  try {
    const data = {
      ...req.params,
      ...req.body,
    };
    // console.log('🙏 CHECKING DATA', data);
    const params = await asyncJoiValidate({
      ...req.body,
      premiseId: req.body?.premiseID,
    }, AccessDTO.createNewAccessByBody);
    console.log('📖 params:', params);
    const response = await AccessService.createUserAccessByBody(params, req.headers.authorization, facepassServer);
    console.log('⭕️ response', response);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.searchUsersAccessByPremise = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate({
      ...req.query,
      premiseId: req.query.premiseID,
    }, AccessDTO.searchUsersAccessByPremise);
    const response = await AccessService.searchUsersAccessByPremise(params, req.user);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.searchPremisesBelongToUser = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate({
      ...req.query,
    }, AccessDTO.searchPremisesBelongToUser);
    const response = await AccessService.searchPremisesBelongToUser(params, req.headers.authorization);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};
