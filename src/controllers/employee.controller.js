const functions = require('firebase-functions');
const admin = require('firebase-admin');

const db = admin.firestore();
const _ = require('lodash');
const moment = require('moment-timezone');

const EmployeeService = require('#services/employee.service');
const EmployeeDTO = require('#dtos/employee.dtos');
const { asyncJoiValidate } = require('#helpers/index');

moment.tz.setDefault('Asia/Kuala_Lumpur');

const runtimeOpts = {
  timeoutSeconds: 540,
  memory: '2GB',
};

// replace context with isAdminLoggedIn middleware
// replace data with req.body

exports.fetchAdminAllowed = async (data, context) => {
  try {
    let premiseList = [];
    let chosenPrem = '';
    await db.collection('admins').doc(context.auth.uid).get()
      .then(async (doc) => {
        // console.log(doc.data().allowedPremises)
        premiseList = doc.data().allowedPremises;
        if (_.includes(premiseList, 'spo')) {
          chosenPrem = await fetchPremiseDoc('spo');
          // console.log(chosenPrem)
        } else {
          chosenPrem = await fetchPremiseDoc(premiseList[0]);
          // console.log(chosenPrem)
        }
      });
    return { premList: premiseList, premSelc: chosenPrem };
  } catch (e) {
    throw new functions.https.HttpsError('invalid-argument', e.message);
  }
};

exports.fetchRawData = async (data, context) => {
  let rawData = [];
  let empList = [];
  let parsedTables = [];
  let timeLineData = [];
  let visibleStaff = 0; // to determine graph height
  let modalData = [];
  const returnData = {};
  let premData = {};

  try {
    const {
      selectedPrem, strtDt, endDt, type,
    } = data;
    // console.log(`Is break?:${includeBreak}`)
    db.collection('admins').doc(context.auth.uid).get().then((doc) => {
      if (!doc.exists) { throw 'you do not have permission'; }
      if (!_.includes(doc.data().allowedPremises, selectedPrem)) { throw 'you do not have permission'; }
    });

    const premFetch = db.collection('premises').doc(selectedPrem)
      .get().then((doc) => {
        return doc.data();
      });

    const empQuery = db.collection('employees')
      .where('employeeOf', '==', selectedPrem)
      .where('isActive', '==', true)
      .get()
      .then((collection) => {
        const tempList = [];
        collection.forEach((doc) => {
          tempList.push(doc.data());
        });
        const tempList2 = _.orderBy(tempList, 'fullName', 'asc');
        return tempList2;
      });
    const waitFetch = fetchAll(strtDt, endDt, selectedPrem);
    // at the same time fetch query base on date and premise(receives selected date prop)
    // parseForPdfData(strtDt,selectedPrem)
    await Promise.all([empQuery, waitFetch, premFetch]).then((values) => {
      empList = values[0];
      rawData = values[1];
      premData = values[2];
      const empNames = [];
      _.forEach(empList, (emp) => {
        empNames.push(emp.uid);
      });
      parsedTables = newParseTableAll(
        rawData,
        strtDt,
        endDt,
        type,
        empList,
        empNames,
        premData.workHour,
        premData.startWork,
        premData.endWork,
        premData.includeBreakCalc,
        premData.includeInOuts,
        premData.minuteBreak,
        premData.hourBreak,
      );
      modalData = parseToModal(rawData, empList);
      _.set(returnData, 'parsedTables', parsedTables);
      _.set(returnData, 'modalData', modalData);
      _.set(returnData, 'empList', empList);
      if (type == 'daily') {
        // let dataForGraph = parseTimelineGraph(type, rawData, empList)
        const dataForGraph = parseTimelineGraph(
          type,
          rawData,
          empList,
          empNames,
          premData.startWork,
          premData.endWork,
          premData.includeInOuts,
        );
        timeLineData = dataForGraph[0];
        visibleStaff = dataForGraph[1];
        _.set(returnData, 'timeLineData', timeLineData);
        _.set(returnData, 'visibleStaff', visibleStaff);
      }
      // if(type === 'monthly') {
      // 	parseForPdfData(strtDt,endDt, selectedPrem,rawData,empList )
      // }
    });
    return returnData;
  } catch (e) {
    throw new functions.https.HttpsError('invalid-argument', e.message);
  }
};

exports.handleDelete = async (data, context) => {
  if (data.rowData.manual) {
    return db.collection('manualInOut').doc(data.rowData.docId)
      .get().then((doc) => {
        doc.ref.delete();
      });
  }
  return db.collection('facePassScans').doc(data.rowData.docId)
    .get().then((doc) => {
      doc.ref.update({
        disabled: true,
      });
    });
};

exports.handleNewCreate = async (data, context) => {
  try {
    const {
      staffList, selectedUid, prem, unixDate, checkType,
    } = data;
    const empData = _.find(staffList, { uid: selectedUid });
    const objManual = {
      checkType,
      createdAt: new moment.unix(unixDate).toDate(),
      employee: true,
      name: empData.fullName,
      premiseId: prem,
      temperature: null,
      uid: selectedUid,
      isAdminAdd: true,
    };
    return db.collection('manualInOut').add(objManual);// .then(() => console.log('---successfully added manual data---'))
  } catch (e) {
    throw new functions.https.HttpsError('invalid-argument', e.message);
  }
};

exports.savePremiseSetting = async (data, context) => {
  try {
    const {
      workHour, startWork, endWork, minuteBreak, premId, includeBreak, includeInOuts, hourBreak, startBreak,
    } = data;
    await db.collection('admins').doc(context.auth.uid).get().then(async (doc) => {
      if (!doc.exists) {
        throw 'you do not have permission';
      }
      await db.collection('premises').doc(premId).update({
        workHour,
        startWork,
        endWork,
        startBreak,
        minuteBreak,
        hourBreak,
        includeBreakCalc: includeBreak,
        includeInOuts,
      }).catch((e) => {
        throw e.message;
      });
    });
  } catch (e) {
    throw new functions.https.HttpsError('invalid-argument', e.message);
  }
};

const fetchPremiseDoc = async (premiseName) => {
  let premiseData = {};
  await db.collection('premises').doc(premiseName).get()
    .then((doc) => {
      premiseData = doc.data();
    });
  return premiseData;
};

const fetchAll = async (startDate, endDate, selectedPrem) => {
  const start = moment.unix(startDate).startOf('day').toDate();
  const end = moment.unix(endDate).endOf('day').toDate();
  const tempRawScan = []; // facePassScans
  const tempManualScan = []; // manualInOut
  const queryScans = db.collection('facePassScans')
    .where('createdAt', '>=', start)
    .where('createdAt', '<=', end)
    .where('premiseId', '==', selectedPrem);
  const queryS = queryScans.get().then((collection) => {
    collection.forEach((doc) => {
      const dataDoc = doc.data();
      if (dataDoc.uid != false
        // dataDoc.employee==true &&
        && !_.has(dataDoc, 'disabled')) {
        _.set(dataDoc, 'docId', doc.id);
        _.set(dataDoc, 'isManual', false);
        tempRawScan.push(dataDoc);
      }
    });
    return tempRawScan;
  }, (err) => {
    console.log(`Encountered error: ${err}`);
  });
  const queryAdminAdd = db.collection('manualInOut')
    .where('premiseId', '==', selectedPrem)
    .where('createdAt', '>=', start)
    .where('createdAt', '<=', end);
  const adminS = queryAdminAdd.get().then((collection) => {
    collection.forEach((doc) => {
      const dataDoc = doc.data();
      if (dataDoc.employee == true) {
        _.set(dataDoc, 'docId', doc.id);
        _.set(dataDoc, 'isManual', true);
        tempManualScan.push(dataDoc);
      }
    });
    return tempManualScan;
  }, (err) => {
    console.log(`Encountered error: ${err}`);
  });

  let conCatRaw = [];
  await Promise.all([queryS, adminS]).then((values) => {
    if (!_.concat(values[0], values[1]).length) {
      // alert('No data on this date')
    }
    conCatRaw = _.concat(values[0], values[1]);
  });
  const orderedRawData = _.orderBy(conCatRaw, (doc) => {
    return new moment.unix(doc.createdAt._seconds);
  }, ['asc']);
  // _.forEach(orderedRawData, (dataObj, idx) => {
  //   console.log(`${moment.unix(dataObj.createdAt._seconds).format('DD MMM, hh:mmA')}, ${idx}`)
  // })
  return orderedRawData;
};

const newParseTableAll = (
  fullRawData,
  startDate,
  endDate,
  type,
  empList,
  empNames,
  otHour,
  startHr,
  endHr,
  includeBreak,
  includeInOuts,
  minuteBreak,
  hourBreak,
) => {
  //	variables to pass into lunch dur
  const startHour = startHr;
  const finishHour = endHr;

  if (_.isNil(otHour)) {
    otHour = finishHour - startHour;
  }

  if (includeInOuts === null) {
    includeInOuts = false;
  }
  // let lunchStart = 12
  // let lunchEnd = 13

  // let fullRawData = _.cloneDeep(fullRawData)
  let newRawScans = _.cloneDeep(fullRawData);
  if (includeInOuts) {
    newRawScans = _.filter(fullRawData, (doc) => {
      return doc.checkType === 'in' || doc.checkType === 'out';
    });
  }

  let staffList = _.uniqBy(newRawScans, 'uid');
  staffList = _.orderBy(_.filter(staffList, (doc) => {
    return empNames.includes(doc.uid);
  }), 'name', 'asc');
  // console.log(`staffLen: ${staffList.length}`)

  const firstDay = moment.unix(startDate).startOf('day');
  const endDay = moment.unix(endDate).endOf('day');
  const tempTable = [];
  const weekTableData = [];
  const monthTableData = [];
  const csvMonthFull = [];
  const pdfMonthData = []; // Array containing arrays for each
  // if (endDay.isAfter(moment())) {
  //     endDay = moment()
  // }
  _.forEach(staffList, (docEmp, index) => {
    const currentDay = _.cloneDeep(firstDay);
    const empOnlyDocs = _.filter(newRawScans, (doc) => {
      return doc.uid === docEmp.uid;
    });

    const empName = _.get(_.find(empList, (x) => {
      if (x.uid === docEmp.uid) {
        return true;
      }
    }), 'fullName');

    const objData = { staffName: empName };
    const csvMonthRow = []; // Each employee has their own row
    csvMonthRow.push[empName];
    let finalHour = 0;

    let weekTotalHour = 0;
    const weekHourArray = [empName];
    let newWeekStart = _.cloneDeep(currentDay);
    let newWeekEnd = moment(currentDay).add(6, 'days');
    const weekRangeRow = [];
    const weekNumRow = [];
    let weekCounter = 1;
    while (!currentDay.isAfter(endDay, 'day')) {
      if (currentDay.isAfter(newWeekEnd, 'day')) {
        if (index == 0) { // this is for the PDF Table //only do one for the header
          const rangeTxt = `${moment(newWeekStart).format('Do')} - ${moment(newWeekEnd).format('Do')} `;
          weekRangeRow.push(rangeTxt);
          weekNumRow.push(`Week ${weekCounter}`);
          weekCounter += 1;
        }
        newWeekStart = _.cloneDeep(currentDay);
        newWeekEnd = moment(currentDay).add(6, 'days');
        weekHourArray.push(parseHour(weekTotalHour));
        weekTotalHour = 0;
      }

      const dayArray = []; // checkins and outs for a day
      _.forEach(empOnlyDocs, (doc) => {
        if (moment.unix(doc.createdAt.seconds).isSame(currentDay, 'day')) {
          dayArray.push(doc);
        }
      });

      let totalHourDay = 0;
      let overtime = '-';
      if (dayArray.length) {
        let firstInDoc = null;
        if (includeInOuts) {
          firstInDoc = _.find(dayArray, (doc) => {
            return doc.checkType == 'in';
          });
        } else {
          firstInDoc = dayArray[0];
        }

        let firstIn = null;
        if (firstInDoc) {
          firstIn = moment.unix(firstInDoc.createdAt).format('h:mm A');
          // console.log(moment.unix(firstInDoc.createdAt).format('h:mm A'))
        }

        let lastOut = null;
        if (!includeInOuts) {
          lastOut = moment.unix(dayArray[dayArray.length - 1].createdAt).format('h:mm A');
        }
        const trackAlreadyIndex = [];
        let inEarlyLate = 0;
        let outEarlyLate = 0;

        // purpose of this loop is to count total hours perday
        if (includeInOuts) {
          _.forEach(dayArray, (checkData, indxDay) => {
            if (!_.includes(trackAlreadyIndex, indxDay)) {
              if (checkData.checkType === 'in') {
                const inTime = moment.unix(checkData.createdAt.seconds);
                if (firstInDoc) {
                  inEarlyLate = calcInEarlyLate(firstInDoc, startHour);
                }
                let nextCount = 1; // counter to keep track of index of redundant data
                let nextCheck = dayArray[indxDay + nextCount];
                if (nextCheck) {
                  let outTime = null;
                  while (nextCheck && nextCheck.checkType == 'in') {
                    trackAlreadyIndex.push(indxDay + nextCount);
                    nextCount++;
                    nextCheck = dayArray[indxDay + nextCount];
                  }
                  if (nextCheck) { // if still in range
                    trackAlreadyIndex.push(indxDay + nextCount);
                    if (nextCheck.checkType == 'out') {
                      outTime = moment.unix(nextCheck.createdAt.seconds);
                      lastOut = outTime.format('h:mm A');
                      while (nextCheck && nextCheck.checkType == 'out') {
                        // 	//find the last out before the next in
                        if (dayArray[indxDay + nextCount + 1] // if next+1 exists
                          && dayArray[indxDay + nextCount + 1].checkType == 'out') {
                          nextCount++;
                          nextCheck = dayArray[indxDay + nextCount];
                          outTime = moment.unix(nextCheck.createdAt.seconds);
                          lastOut = outTime.format('h:mm A');
                          trackAlreadyIndex.push(indxDay + nextCount);
                        } else { // break next loop
                          nextCount++;
                          nextCheck = dayArray[indxDay + nextCount];
                        }
                      }
                      if (nextCount + indxDay == dayArray.length) {
                        outEarlyLate = calcOutEarlyLate(outTime, finishHour);
                      }
                    }
                  }
                  totalHourDay += calcDuration(inTime, outTime, empName, startHour);
                }
              }
            }
          });
        } else {
          const inTime = moment(dayArray[0].createdAt);
          const outTime = moment(dayArray[dayArray.length - 1].createdAt);
          totalHourDay = calcDuration(inTime, outTime, empName, startHour);
          inEarlyLate = calcInEarlyLate(firstInDoc, startHour);
          const outTimeMoment = moment.unix(dayArray[dayArray.length - 1].createdAt);
          outEarlyLate = calcOutEarlyLate(outTimeMoment, finishHour);
        }
        if (includeBreak !== null && firstIn !== null && lastOut !== null) {
          if (includeBreak && !_.isNil(minuteBreak) && !_.isNil(hourBreak)) {
            const startT = moment(firstIn, 'h:mm A');
            const finishT = moment(lastOut, 'h:mm A');
            totalHourDay_beforeDeduct = finishT.diff(startT, 'hours', true);
            const minuteToHour = minuteBreak / 60;
            const totalHourBreak = minuteToHour + hourBreak;
            totalHourDay = totalHourDay_beforeDeduct - totalHourBreak;
          } else {
            const startT = moment(firstIn, 'h:mm A');
            const finishT = moment(lastOut, 'h:mm A');
            totalHourDay = finishT.diff(startT, 'hours', true);
          }
        }

        overtime = calcOvertime(otHour, totalHourDay);

        const newRowData = {
          staffName: empName,
          date: currentDay.format('DD/MM/YYYY'),
          workIn: firstIn || '-',
          lunchOut: '',
          lunchIn: '',
          workOut: lastOut || '-',
          inEarly: parseHour(_.get(inEarlyLate, 'early')),
          inLate: parseHour(_.get(inEarlyLate, 'late')),
          outEarly: parseHour(_.get(outEarlyLate, 'early')),
          outLate: parseHour(_.get(outEarlyLate, 'late')),
          total: parseHour(totalHourDay),
          overT: parseHour(overtime),
        };
        tempTable.push(newRowData);

        finalHour += totalHourDay;
        if (type === 'weekly') {
          const dayName = currentDay.format('ddd'); // sun, mon, tue, wed...
          const dayDataWeek = {
            fCheckin: firstIn,
            lCheckout: lastOut,
            total: parseHour(totalHourDay),
          };
          _.set(objData, dayName, dayDataWeek);
        }

        if (type === 'monthly') {
          const dayNum = `d${currentDay.format('DD')}`; // 1 ,2, 3, ...31
          weekTotalHour += totalHourDay;
          const dayMonthData = {
            fCheckin: firstIn ? moment(firstIn, 'h:mm A').format('HH:mm') : null,
            lCheckout: lastOut ? moment(lastOut, 'h:mm A').format('HH:mm') : null,
            // fCheckin : firstIn,
            // lCheckout : lastOut,
            date: dayNum,
            oTime: parseHour(overtime),
            lateIn: parseHour(_.get(inEarlyLate, 'late')),
            total: parseHour(totalHourDay),
          };
          _.set(objData, dayNum, dayMonthData);
          csvMonthRow.push(totalHourDay);
        }
      } else {
        // csvMonthRow.push('')
      }
      _.set(objData, 'total', parseHour(finalHour));
      currentDay.add(1, 'day');
    }// end day loop

    weekHourArray.push(parseHour(weekTotalHour), parseHour(finalHour));
    pdfMonthData.push(weekHourArray);
    if (index === 0) { // only do one for the header
      newWeekEnd = moment(_.cloneDeep(currentDay)).subtract(1, 'day');
      const rangeTxt = `${moment(_.cloneDeep(newWeekStart)).format('Do')} - ${moment(_.cloneDeep(newWeekEnd)).format('Do')}`;
      weekRangeRow.push(rangeTxt);
      pdfMonthData.unshift(weekRangeRow);
      weekNumRow.push(`Week ${weekCounter}`);
      pdfMonthData.unshift(weekNumRow);
    }
    csvMonthRow.push(finalHour);
    type === 'weekly' ? weekTableData.push(objData) : null;
    type === 'monthly' ? monthTableData.push(objData) : null;
    type === 'monthly' ? csvMonthFull.push(csvMonthRow) : null;
  });// end of staff loop

  const rtrnData = {};

  type === 'monthly' ? _.set(rtrnData, 'parsedType', monthTableData) : null;
  type === 'weekly' ? _.set(rtrnData, 'parsedType', weekTableData) : null;
  _.set(rtrnData, 'tableData', tempTable);
  _.set(rtrnData, 'monthCsv', csvMonthFull);
  _.set(rtrnData, 'pdfData', pdfMonthData);

  return rtrnData;
};

const calcInEarlyLate = (doc, startTime) => {
  const hoursLateEarly = { late: 0, early: 0 };
  checkin = moment.unix(doc.createdAt);
  const startToday = moment(checkin).startOf('day').add(startTime, 'hour');
  if (checkin.isAfter(startToday, 'minute')) {
    _.set(hoursLateEarly, 'late', checkin.diff(startToday, 'hours', true));
  }

  if (startToday.isAfter(checkin, 'minute')) {
    _.set(hoursLateEarly, 'early', startToday.diff(checkin, 'hours', true));
  }

  return hoursLateEarly;
};

const calcOutEarlyLate = (checkOut, finishTime) => {
  const durationHours = { late: 0, early: 0 };
  const finTime = moment(checkOut).startOf('day').add(finishTime, 'hours');
  if (checkOut.isAfter(finTime, 'minute')) {
    _.set(durationHours, 'late', checkOut.diff(finTime, 'hours', true));
  }

  if (checkOut.isBefore(finTime, 'minute')) {
    _.set(durationHours, 'early', finTime.diff(checkOut, 'hours', true));
  }

  return durationHours;
};

const parseHour = (hourNumber) => {
  if (hourNumber <= 0 || !_.isNumber(hourNumber)) {
    return '-';
  }
  const hourPart = Math.trunc(hourNumber);
  let minutePart = Math.trunc(moment.duration((hourNumber - hourPart), 'hour').as('minutes'));
  if (minutePart.toString().length === 1) {
    minutePart = `0${minutePart}`;
  }
  const label = `${hourPart}:${minutePart}`;
  return label;
};

const calcDuration = (checkin, checkout, name, startTime) => {
  if (checkin && checkout) {
    // we do not count the hours where the employee works before start time
    let hourDuration = 0;
    const startToday = moment(checkin).startOf('day').add(startTime, 'hour');
    if (startToday.isAfter(checkin)) {
      hourDuration = moment(checkout).diff(startToday, 'hours', true);
    } else {
      hourDuration = moment(checkout).diff(checkin, 'hours', true);
    }
    return hourDuration;
  } return 0;
};

const calcOvertime = (limitTime, totalTime) => {
  if (limitTime == null) {
    limitTime = 8;
  }
  if (limitTime == '') {
    return '-';
  }
  if (totalTime > limitTime) {
    return totalTime - limitTime;
  } return null;
};

const parseToModal = (rawData, empList) => {
  // let orderedData = _.orderBy(rawData, doc => {
  //   return new moment.unix(doc.createdAt.seconds)
  // }, ['asc'])

  const tempTable = [];
  _.forEach(rawData, (doc) => {
    if (doc.name !== 'Unknown') {
      const objRow = {
        date: moment.unix(doc.createdAt.seconds).format('DD/MM/YYYY'),
        createdAt: moment.unix(doc.createdAt.seconds).format('DD/MM/YYYY, h:mm A'),
        staffName: _.get(_.find(empList, (x) => {
          if (x.uid == doc.uid) {
            return true;
          }
        }), 'fullName', doc.name),
        checkType: doc.checkType,
        temperature: doc.temperature ? Math.round((doc.temperature + Number.EPSILON) * 100) / 100 : 0,
        fullDate: moment.unix(doc.createdAt.seconds).utc().toDate(),
        premise: doc.premiseId,
        userId: doc.uid,
        docId: doc.docId,
        serialNum: doc.serialNumber,
        manual: doc.isManual,
        late: _.get(doc, 'late', false),
        userRemarks: _.get(doc, 'remarks', ''), // new feature to remark late check-in
      };
      tempTable.push(objRow);
    }
  });
  return tempTable;
};

const parseTimelineGraph = (type, fullRawData, empList, empNames, startHour, endHour, includeInOuts) => {
  const startTime = startHour;
  const endTime = endHour;
  let newRawScans = _.cloneDeep(fullRawData);

  if (includeInOuts) {
    // console.log('filter IN/OUTS')
    newRawScans = _.filter(fullRawData, (doc) => {
      return doc.checkType == 'in' || doc.checkType == 'out';
    });
  }

  if (type === 'daily') {
    // make a list of employees from the array
    let staffList = _.uniqBy(newRawScans, 'uid');
    staffList = _.orderBy(_.filter(staffList, (doc) => {
      return empNames.includes(doc.uid);
    }), 'name', 'asc');

    const visibleStaff = staffList.length;
    // create chart structure
    let graphData = [
      [ // structure of data
        { type: 'string', id: 'User' },
        { type: 'string', id: 'Name' },
        { type: 'string', role: 'style' },
        { type: 'date', id: 'Start' },
        { type: 'date', id: 'End' },
      ],
    ];
    // foreach staffName, loop through fullRawData and get only doc related to that emp put in an array
    _.forEach(staffList, (staffDoc, index) => {
      const allData = [];
      _.forEach(newRawScans, (doc) => {
        if (doc.uid === staffDoc.uid) {
          allData.push(doc);
        }
      });

      const orderedData = _.cloneDeep(allData);
      // _.orderBy(allData, doc => {
      //   return new moment.unix(doc.createdAt.seconds)
      // }, ['asc'])

      // make sure none are empty.
      if (!_.isEmpty(orderedData)) {
        // console.log(`${orderedData[0].name}: ${orderedData.length}`)
        const trackAlreadyIndex = [];
        let firstBar = true;
        let isLast = false;
        const empName = _.get(_.find(empList, (x) => {
          if (x.uid == orderedData[0].uid) {
            return true;
          }
        }), 'fullName');

        if (includeInOuts) {
          _.forEach(orderedData, (doc, checkIndex) => {
            if (!_.includes(trackAlreadyIndex, checkIndex)) {
              let barData = [];
              if (doc.checkType === 'in') {
                const inTime = moment.unix(doc.createdAt.seconds);
                const inTimeTxt = inTime.format('h:mm A');
                let nextCounter = 1;
                let nextCheck = orderedData[checkIndex + nextCounter];
                if (nextCheck) { // if next index exists
                  while (nextCheck && nextCheck.checkType === 'in') { // && nextCreateTime.isSame(inTime,'hour')
                    trackAlreadyIndex.push(checkIndex + nextCounter);
                    nextCounter++;
                    nextCheck = orderedData[checkIndex + nextCounter];
                  }
                }
                if (nextCheck) { // CHECK if still in index range
                  trackAlreadyIndex.push(checkIndex + nextCounter);
                  if (nextCheck.checkType == 'out') { // just in case
                    let outTime = moment.unix(nextCheck.createdAt.seconds);
                    let lastOut = outTime.format('h:mm A');
                    while (nextCheck && nextCheck.checkType == 'out') {
                      // 	//find the last out before the next in
                      if (orderedData[checkIndex + nextCounter + 1] // if next+1 exists
                        && orderedData[checkIndex + nextCounter + 1].checkType == 'out') {
                        nextCounter++;
                        nextCheck = orderedData[checkIndex + nextCounter];
                        outTime = moment.unix(nextCheck.createdAt.seconds);
                        lastOut = outTime.format('h:mm A');
                        trackAlreadyIndex.push(checkIndex + nextCounter);
                      } else { // break next loop
                        nextCounter++;
                        nextCheck = orderedData[checkIndex + nextCounter];
                      }
                    }
                    if (nextCounter + checkIndex == orderedData.length) {
                      isLast = true;
                    }
                    const barLabel = `IN:${inTimeTxt} - OUT:${lastOut}`;
                    barData = [empName, barLabel, '#2196F3', inTime.unix(), outTime.unix()];// in our is same since no out in one hour
                    barData = addEarlyLate(barData, startTime, endTime, firstBar, isLast);
                    firstBar = false;
                  } else {
                    const barLabel = `IN:${inTimeTxt} - ?`;
                    barData = [[empName, barLabel, 'orange', inTime.unix(), inTime.unix()]];
                  }
                } else {
                  const barLabel = `IN:${inTimeTxt} - ?`;
                  barData = [[empName, barLabel, 'orange', inTime.unix(), inTime.unix()]];
                }
              }

              if (doc.checkType === 'out') { // remove && index==0
                const outTime = moment.unix(doc.createdAt.seconds);
                let latestOutTime = _.cloneDeep(outTime);
                let nextCount = 1;
                let nextCheck = orderedData[checkIndex + nextCount];
                // check if the nextChecks within one hour is still out
                // if found, use latest out
                if (nextCheck) { // first nextCheck
                  let nextCreateTime = moment.unix(nextCheck.createdAt.seconds);
                  while (nextCheck && nextCheck.checkType == 'out' && nextCreateTime.isSame(outTime, 'hour')) {
                    latestOutTime = _.cloneDeep(nextCreateTime);
                    nextCount++;
                    nextCheck = orderedData[checkIndex + nextCount];
                    if (nextCheck) nextCreateTime = moment.unix(nextCheck.createdAt.seconds);
                  }
                }
                const latestOutText = latestOutTime.format('h:mm A');
                const barLabel = `? - OUT:${latestOutText}`;
                barData = [[empName, barLabel, 'orange', latestOutTime.unix(), latestOutTime.unix()]];
              }
              // graphData.push(barData)
              graphData = _.concat(graphData, barData);
            }
          });
        } else {
          const firstScan = orderedData[0].createdAt.seconds;
          const lastScan = orderedData[orderedData.length - 1].createdAt.seconds;
          const onebarLabel = `IN:${moment.unix(firstScan).format('h:mm A')} - OUT:${moment.unix(lastScan).format('h:mm A')}`;
          let barData = [empName, onebarLabel, '#2196F3', firstScan, lastScan];
          if (moment.unix(firstScan).isAfter(moment.unix(lastScan))) {
            console.log('ERROR : TIME DOES NOT TALLY');
          }
          barData = addEarlyLate(barData, startTime, endTime, true, true);
          graphData = _.concat(graphData, barData);
        }
      }
    });
    return [graphData, visibleStaff];
  }
};

const addEarlyLate = (barData, startTime, finishTime, isFirst, isLast) => {
  const cloneBar = _.cloneDeep(barData);
  const timeIn = moment.unix(barData[3]).startOf('minute');
  const timeOut = moment.unix(barData[4]).startOf('minute');

  const strtTimeToday = moment(timeIn).startOf('day').add(startTime, 'hour');
  const finTimeToday = moment(timeOut).startOf('day').add(finishTime, 'hour');

  const newBar = [];
  let cloneBarIn = moment.unix(barData[3]);
  let cloneBarOut = moment.unix(barData[4]);

  if (isFirst) {
    if (timeIn.isAfter(strtTimeToday) && !timeIn.isSame(timeOut) && timeIn.isBefore(finTimeToday)) {
      const lateCalc = `${timeIn.diff(strtTimeToday, 'minutes')}m`;
      const lateBar = [cloneBar[0], lateCalc, 'crimson', strtTimeToday.unix(), timeIn.unix()];
      newBar.push(lateBar);
    }

    if (timeIn.isAfter(strtTimeToday) && !timeIn.isSame(timeOut) && timeIn.isAfter(finTimeToday)) {
      const lateCalc = `${timeIn.diff(strtTimeToday, 'minutes')}m`;
      const lateBar = [cloneBar[0], lateCalc, 'crimson', strtTimeToday.unix(), finTimeToday.unix()];
      newBar.push(lateBar);
    }

    if (timeIn.isBefore(strtTimeToday) && !timeIn.isSame(timeOut) && timeOut.isAfter(strtTimeToday)) {
      const earlyCalc = `${timeIn.format('h:mm A')} (${strtTimeToday.diff(timeIn, 'minutes')}m) `;
      const earlyBar = [cloneBar[0], earlyCalc, 'green', timeIn.unix(), strtTimeToday.unix()];
      newBar.push(earlyBar);
      cloneBar.splice(3, 1, strtTimeToday.unix());
      cloneBarIn = _.cloneDeep(strtTimeToday);
    }
  }

  if (isLast) { // the last bar that has in and out
    if (timeOut.isBefore(finTimeToday)) {
      const lateCalc = `${finTimeToday.diff(timeOut, 'minutes')}m`;
      const lateBar = [cloneBar[0], lateCalc, 'crimson', timeOut.add(1, 'minute').unix(), finTimeToday.unix()];
      newBar.push(lateBar);
    }

    if (timeOut.isAfter(finTimeToday) && timeIn.isBefore(finTimeToday)) {
      const overCalc = ` ${timeOut.format('h:mm A')} (${timeOut.diff(finTimeToday, 'minutes')}m)`;
      const overBar = [barData[0], overCalc, 'green', finTimeToday.unix(), timeOut.unix()];
      newBar.push(overBar);
      cloneBar.splice(4, 1, finTimeToday.unix());
      cloneBarOut = _.cloneDeep(finTimeToday);
    }

    if (timeOut.isAfter(finTimeToday) && timeIn.isAfter(finTimeToday)) {
      const overCalc = ` ${timeOut.format('h:mm A')} (${timeOut.diff(timeIn, 'minutes')}m)`;
      const overBar = [barData[0], overCalc, 'green', timeIn.unix(), timeOut.unix()];
      newBar.push(overBar);
      return newBar;
      // cloneBar.splice(4,1,finTimeToday.unix())
      // cloneBarOut = _.cloneDeep(finTimeToday)
    }
  }

  const newTextTime = `IN:${cloneBarIn.format('h:mm A')} - OUT:${cloneBarOut.format('h:mm A')}`;
  cloneBar.splice(1, 1, newTextTime);
  newBar.push(cloneBar);
  return newBar;
};

//----------------------------------------
// New controllers.
exports.addEmployeeToPremise = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate(req.body, EmployeeDTO.addEmployeeToPremise);
    const response = await EmployeeService.addEmployeeToPremise(params);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.createEmployee = (facePassServer) => async (req, res, next) => {
  try {
    const body = {
      ...req.user,
      ...req.body,
      imageObj: req.file,
    };
    console.log('create user body:', body);
    const params = await asyncJoiValidate(body, EmployeeDTO.createEmployee);
    const response = await EmployeeService.createEmployee(params, req.user)(facePassServer);

    return res.success(response);
  } catch (err) {
    return next(err);
  }
};
