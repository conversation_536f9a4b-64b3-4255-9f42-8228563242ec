const { asyncJoiValidate, admin, serverTimestamp } = require('#helpers/index');
const ChzeService = require('#services/chze.service');
const CallDTO = require('#dtos/call.dtos');

const { initParams } = require('request');
const { functions } = require('lodash');

// Chze add
exports.getAllDevices = async (req, res, next) => {
  try {
    const response = await ChzeService.getAllDevices(req.body);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.testGetAllRecords = async (req, res, next) => {
  try {
    const response = await ChzeService.testGetAllRecords(req.body);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};
exports.getAllPremises = async (req, res, next) => {
  try {
    const response = await ChzeService.getAllPremises(req.body);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.getDevicesSettingWithoutPremises = async (req, res, next) => {
  const { serialnumber } = req.headers;
  try {
    const response = await ChzeService.getDevicesSettingWithoutPremises({
      serialNumber: serialnumber,
    });
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.createCall = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate(req.body, CallDTO.createCall);
    const response = await ChzeService.createCall({ params });
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.test = async (req, res, next) => {
  try {
    const response = await ChzeService.test();
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.createNewDepartment = async (req, res, next) => {
  try {
    const token = await admin.auth().verifyIdToken(req.headers.authorization);
    console.log('token', token);

    const { uid } = token;
    console.log('uid', uid);

    // await checkAdminPermission(uid);
    console.log('DATA RECEIVED:', req);
    const newDepartment = {
      premise: req.body.premise,
      name: req.body.name,
      createdAt: serverTimestamp,
      count: 0,
    };

    const response = await ChzeService.createNewDepartment(newDepartment);
    return res.success(response);
  } catch (e) {
    console.log('createError', e);
    throw new functions.https.HttpsError('create_dept_error', e.message);
  }
};
