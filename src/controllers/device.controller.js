const { asyncJoiValidate } = require('#helpers/index');
const DeviceService = require('#services/device.service');
const DeviceDTO = require('#dtos/device.dto');
const { initParams } = require('request');
const _ = require('lodash');
const AdminApikeyModel = require('#models/AdminApikey.model');

exports.getDeviceSettings = async (req, res, next) => {
  try {
    const { apikey, serialnumber } = req.headers;
    const response = await DeviceService.getDeviceSettings({
      apiKey: apikey,
      serialNumber: serialnumber,
    });
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.setDeviceSettings = (facepassServer) => async (req, res, next) => {
  try {
    const response = await DeviceService.setDeviceSettingsV2(req.body, 'system', facepassServer);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.getAllDevices = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate({ ...req.params, ...req.query }, DeviceDTO.getAllDevices);
    const response = await DeviceService.getAllDevices(params);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.testGetAllRecords = async (req, res, next) => {
  try {
    const response = await DeviceService.testGetAllRecords(req.body);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.updateDevice = async (req, res, next) => {
  try {
    const data = {
      ...req.body,
      ...req.params,
    };
    const params = await asyncJoiValidate(data, DeviceDTO.updateDevice);
    const response = await DeviceService.updateDevice(params, req.user);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.deleteDevice = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate(req.params, DeviceDTO.deleteDevice);
    const response = await DeviceService.deleteDevice(params, req.user);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.rebootDevice = (facepassServer) => async (req, res, next) => {
  try {
    const params = await asyncJoiValidate(req.params, DeviceDTO.remoteControlDevice);
    const response = await DeviceService.rebootDevice(params, req.user, facepassServer);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.rebootDeviceWithApiKey = (facepassServer) => async (req, res, next) => {
  try {
    const params = await asyncJoiValidate(req.params, DeviceDTO.remoteControlDevice);
    const response = await DeviceService.rebootDeviceWithApiKey(params, facepassServer);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.restartDevice = (facepassServer) => async (req, res, next) => {
  try {
    const params = await asyncJoiValidate(req.params, DeviceDTO.remoteControlDevice);
    const response = await DeviceService.restartDevice(params, req.user, facepassServer);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.openDoorManual = (facepassServer) => async (req, res, next) => {
  try {
    req.params = {
      ...req.params,
      serialNumber: req.body.serialNumber,
      durationOpen: req.body.durationOpen,
    };

    if (!_.isEmpty(req.body?.premiseID)) {
      req.params = {
        ...req.params,
        premiseId: req.body.premiseID,
      };
    }

    const params = await asyncJoiValidate(req.params, DeviceDTO.remoteControlDevice);
    const response = await DeviceService.openDoorManual(params, req.user, facepassServer);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.forceSyncDevice = (facepassServer) => async (req, res, next) => {
  try {
    const params = await asyncJoiValidate(req.params, DeviceDTO.remoteControlDevice);
    const response = await DeviceService.forceSyncDevice(params, req.user, facepassServer);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.forceSyncDeviceWithApiKey = (facepassServer) => async (req, res, next) => {
  try {
    const params = await asyncJoiValidate(req.params, DeviceDTO.remoteControlDevice);
    const response = await DeviceService.forceSyncDeviceWithApiKey(params, facepassServer);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.getAllDeviceWithApiKey = async (req, res, next) => {
  try {
    const { premiseToGetFrom } = req.query;
    const key = req.headers.authorization;
    console.log('premiseToGetFrom', premiseToGetFrom);
    if (!_.isNil(premiseToGetFrom)) {
      const foundKey = await AdminApikeyModel.findOne({
        apiKey: key,
      }).lean();
      const foundPremise = _.find(foundKey.premises, (premise) => { if (premise.premiseId.toString() === premiseToGetFrom) return true; });
      if (foundPremise) {
        req.params.premiseId = premiseToGetFrom;
      } else {
        throw new Error('unauthorized key');
      }
    }
    const params = await asyncJoiValidate(req.params, DeviceDTO.getAllDevices);
    const response = await DeviceService.getAllDevices(params);
    // console.log(response);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.getAllDevicesWithPremiseKey = async (req, res, next) => {
  try {
    const premiseKey = req.headers.authorization;
    const params = await asyncJoiValidate(req.params, DeviceDTO.getAllDevices);
    const response = await DeviceService.getAllDevices(params);
    // console.log(response);
    return res.success(response);
  } catch (error) {
    return next(error);
  }
};

exports.getDeviceUserCount = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate(req.query, DeviceDTO.getDeviceUserCount);
    const response = await DeviceService.getDeviceUserCount(params);
    return res.success(response);
  } catch (error) {
    return next(error);
  }
};
