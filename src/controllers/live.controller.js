const functions = require('firebase-functions');
const admin = require('firebase-admin');

const serverTimestamp = admin.firestore.FieldValue.serverTimestamp();
const db = admin.firestore();
const { FieldValue } = admin.firestore;
const _ = require('lodash');
const moment = require('moment-timezone');

moment.tz.setDefault('Asia/Kuala_Lumpur');

const runtimeOpts = {
  timeoutSeconds: 540,
  memory: '2GB',
};

exports.fetchAllowedPremLiveGraph = async (data, context) => {
  try {
    let returnData = [];
    // console.log(`CHECKING AUTH ID : ${context.auth.uid}`)
    await db.collection('admins').doc(context.auth.uid).get()
      .then(async (doc) => {
        if (doc.exists) {
          // console.log(doc.data())
          const premList = doc.data().allowedPremises;
          if (premList) {
            await db.collection('premises').where('id', 'in', premList).get()
              .then(async (collection) => {
                const process = [];
                // console.log(collection.size)
                collection.forEach((doc) => {
                  // console.log(doc.data().name)
                  const premise = doc.data();
                  const a = db.doc(`premises/${premise.premiseId}`).get().then((doc) => {
                    premise.premise = doc.data();
                    return premise;
                  });
                  process.push(a);
                });
                return Promise.all(process).then((data) => {
                  // console.log(data[0])
                  console.log('Completed~~~~');
                  returnData = data;
                  // console.log(data[0])
                  // const premStr = localStorage.getItem('premiseAna')
                  // if (premStr) {
                  //     console.log(premStr)
                  //     setpremAnalysis('spo')
                  // } else {
                  //     setpremAnalysis(data[0].id)
                  // }
                  // setPremis(data)
                });
              });
          }
        }
      });
    return returnData;
  } catch (e) {
    throw new functions.https.HttpsError('invalid-argument', e.message);
  }
};

exports.fetchDataForAnalysis2 = async (data, context) => {
  functions.logger.log("Hello from fetchDataForAnalysis2. Here's a test message");
  try {
    const rawDoc = [];
    let parsedData = {
      hourCount: 0,
      idenDocs: 0,
      temp: 0,
      graph: 0,
    };
    const { endDate, startDate, premAnalysis } = data;

    await db.collection('admins').doc(context.auth.uid).get().then(async (doc) => {
      if (doc.exists) {
        await db.collection('facePassScans')
          .where('createdAt', '<=', moment.unix(endDate).toDate())
          .where('createdAt', '>=', moment.unix(startDate).toDate())
          .where('premiseId', '==', premAnalysis)
          .get()
          .then((collection) => {
            collection.forEach((doc) => {
              const cloneDoc = _.cloneDeep(doc.data());
              _.unset(cloneDoc, 'photoUrl');
              _.unset(cloneDoc, 'prediction');
              rawDoc.push(cloneDoc);
            });
          });
        parsedData = parseToAnalysis(rawDoc);
      }
    });

    return {
      rawDoc,
      parsedData,
    };
  } catch (e) {
    throw new functions.https.HttpsError('invalid-argument', e.message);
  }
};

const parseToAnalysis = (arrData) => {
  const identifiedRec = [];
  const hourCount = {};
  let lowestTemp = 0;
  let highestTemp = 0;
  const scansAboveSafeTemp = [];
  let cumTemp = 0;
  _.forEach(arrData, (doc, index) => {
    cumTemp += _.get(doc, 'temperature', 0);
    if (index == 0) {
      lowestTemp = _.get(doc, 'temperature', 0);
      highestTemp = _.get(doc, 'temperature', 0);
    }
    _.get(doc, 'temperature', 0) < lowestTemp ? lowestTemp = _.get(doc, 'temperature', 0) : null;
    _.get(doc, 'temperature', 0) > highestTemp ? highestTemp = _.get(doc, 'temperature', 0) : null;
    _.get(doc, 'temperature', 0) > 37.5 ? scansAboveSafeTemp.push(doc) : null;
    if (_.get(doc, 'uid', false) != false) {
      identifiedRec.push(doc);
    }

    const getHour = _.get(hourCount, moment.unix(doc.createdAt.seconds).format('hA'));
    if (getHour) {
      const newHour = getHour + 1;
      _.set(hourCount, moment.unix(doc.createdAt.seconds).format('hA'), newHour);
    } else {
      _.set(hourCount, moment.unix(doc.createdAt.seconds).format('hA'), 1);
    }
  });
  const avgTemp = cumTemp / arrData.length;
  const newTemp = {
    high: highestTemp?.toFixed(2), // needs ? because some do not have temperature, will return error if it does not exist
    low: lowestTemp?.toFixed(2),
    avg: avgTemp?.toFixed(2),
  };
  // console.log(hourCount)
  // setIdentifiedDocs(identifiedRec.length)
  const graphData = parsePeakHour(hourCount);

  return {
    hourCount,
    idenDocs: identifiedRec.length,
    temp: newTemp,
    graph: graphData,
  };
};

const parsePeakHour = (data) => {
  const graphData = [];
  for (let index = 0; index < 24; index++) {
    const timeKey = moment(index, 'H').format('hA');
    const timeVal = _.get(data, timeKey, 0);
    const dataObj = { name: timeKey, count: timeVal };
    graphData.push(dataObj);
  }
  return graphData;
};
