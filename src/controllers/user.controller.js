/* eslint-disable prefer-destructuring */
const functions = require('firebase-functions');
const _ = require('lodash');
const moment = require('moment-timezone');
const {
  admin,
  db,
  serverTimestamp,
  FieldValue,
  asyncJoiValidate,
  uploadImage,
} = require('#helpers/index');
const UserDTO = require('#dtos/user.dtos');
const UserService = require('#services/user.service');
const { readFileSync } = require('fs');

const AdminApikeyModel = require('#models/AdminApikey.model');
const { uploadToGCS } = require('#helpers/uploadData');
const PremiseModel = require('#models/Premise.model');
const responses = require('#helpers/responses');
const { getAllPremiseDepartments, changeAdminPassword, checkAdminPermission } = require('../oldAPI');
// const { query } = require('express');
moment.tz.setDefault('Asia/Kuala_Lumpur');

const runtimeOpts = {
  timeoutSeconds: 540,
  memory: '2GB',
};

exports.createUser = (facePassServer) => async (req, res, next) => {
  try {
    const body = {
      ...req.user,
      ...req.body,
      imageObj: req.file,
    };
    console.log('create user body:', body);
    const params = await asyncJoiValidate(body, UserDTO.createUser);
    const response = await UserService.createUserByAdmin(params, req.user)(facePassServer);

    return res.success(response);
  } catch (err) {
    console.log('err:', err);
    return next(err);
  }
};

exports.createUserWithoutPremise = (facePassServer) => async (req, res, next) => {
  try {
    const { authorization } = req.headers;
    const getApiKeyDoc = await AdminApikeyModel.findOne({ apiKey: authorization }).lean();

    if (_.isEmpty(getApiKeyDoc)) {
      throw responses.failure('You have no access to this premise.');
    }

    const body = {
      ...req.user,
      ...req.body,
      imageObj: req.file,
      premiseId: req.query.premiseId,
    };
    const params = await asyncJoiValidate(body, UserDTO.createUserWithoutPremise);
    const response = await UserService.createUserWithoutPremise(params, authorization, facePassServer);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.createUserWithKey = (facePassServer) => async (req, res, next) => {
  try {
    const { authorization } = req.headers;
    const getApiKeyDoc = await AdminApikeyModel.findOne({ apiKey: authorization });

    const body = {
      ...req.user,
      ...req.body,
      premiseId: getApiKeyDoc.premises[0].premiseId.toString(),
      imageObj: req.file,
    };
    const params = await asyncJoiValidate(body, UserDTO.createUser);
    const response = await UserService.createUserByApiKey(params, authorization)(facePassServer);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.createUserInCustomPremiseWithKey = (facePassServer) => async (req, res, next) => {
  try {
    const { authorization } = req.headers;
    const getApiKeyDoc = await AdminApikeyModel.findOne({ apiKey: authorization });

    const body = {
      ...req.user,
      ...req.body,
      imageObj: req.file,
    };
    const params = await asyncJoiValidate(body, UserDTO.createUserInCustomPremiseWithKey);
    const response = await UserService.createUserByApiKey(params, authorization)(facePassServer);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.createUserWithKeyPremiseId = (facePassServer) => async (req, res, next) => { // this path specified premiseId to add
  try {
    const { authorization } = req.headers;
    const getApiKeyDoc = await AdminApikeyModel.findOne({ apiKey: authorization });
    if (_.isNil(getApiKeyDoc)) {
      throw new Error('error:: invalid key');
    }
    // const findPremise = (await PremiseModel.findOne({ premiseId: req.body.premiseToAdd }).lean())._id;
    // if (_.isNil(getApiKeyDoc)) {
    //   throw new Error('error:: invalid key');
    // }

    // console.log('findpremise:', findPremise.toString());

    // check if premiseToAdd can be found inside the adminapikey
    // const checkIfIncluded = _.find(getApiKeyDoc.premises, (premObj) => {
    //   return premObj.premiseId?.toString() === findPremise.toString();
    // });
    // if (_.isNil(checkIfIncluded)) {
    //   throw new Error('permission denied');
    // }

    const body = {
      ...req.user,
      ...req.body,
      premiseToAdd: req.body.premiseToAdd,
      imageObj: req.file,
      foundKey: getApiKeyDoc,
    };
    console.log('body_test', body);
    const params = await asyncJoiValidate(body, UserDTO.createUserInPremise);
    const response = await UserService.createUserToPremiseByApiKey(params, authorization)(facePassServer);
    return res.success(response);
  } catch (error) {
    return next(error);
  }
};

exports.getUserScans = async (req, res, next) => {
  try {
    let snapShotreturn = null;
    let hasMoreData = false;
    let nextId = null;
    const now = moment().toDate();

    const { userId, allowedPrems, limit } = req.query;
    // console.log('FUll data: ', data);
    // console.log('User Id: ', userId);
    await db.collection('facePassScans')
      .where('uid', '==', userId)
      .where('premiseId', 'in', allowedPrems)
      .where('createdAt', '<=', now)
      .orderBy('createdAt', 'desc')
      .limit(limit)
      .get()
      .then((snapshot) => {
        if (snapshot.size > limit - 1) {
          hasMoreData = true;
          nextId = snapshot.docs.pop().data().id;
        }
        snapShotreturn = snapshot.docs.map((doc) => doc.data());
      });

    return {
      scans: snapShotreturn,
      hasMoreData,
      nextDocId: nextId,
    };
  } catch (err) {
    return next(err);
  }
};

exports.exportOneUserScans = async (req, res, next) => {
  try {
    let snapShotreturn = null;
    const { userId, allowedPrems } = req.query;
    // console.log('FUll data: ', data);
    await db.collection('facePassScans')
      .where('uid', '==', userId)
      .where('premiseId', 'in', allowedPrems)
      .orderBy('createdAt', 'desc')
      .get()
      .then((snapshot) => {
        snapShotreturn = snapshot.docs.map((doc) => doc.data());
      });

    // CALL EXCELJS TO PARSE FUNCTION BEFORE RETURNING
    return {
      scans: snapShotreturn,
    };
  } catch (err) {
    return next(err);
  }
};

exports.fetchUserList = async (req, res, next) => {
  try {
    const query = {
      uid: req.query.uid,
    };
    const params = await asyncJoiValidate(query, UserDTO.fetchList);
    const response = await UserService.fetchUserList(params);

    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.fetchExternalUserList = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate({
      premiseId: req.query.premiseId,
    }, UserDTO.fetchExternalUserList);
    const response = await UserService.fetchUserList(params);

    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.fetchList = async (req, res, next) => {
  try {
    let fetchedData = [];
    let premiseList = [];
    let premiseKeyName = {};
    let tableData = [];
    // let allPremiseDepartments = []

    const { uid, fetchType } = req.query;

    await db.collection('admins').doc(uid).get()
      .then(async (doc) => {
        if (!_.isEmpty(doc.data().allowedPremises)) {
          premiseList = doc.data().allowedPremises;

          if (fetchType === 'fullList') {
            fetchedData = await Promise.all([
              fetchUsersList(doc.id),
              fetchPremisesData(premiseList),
              // getAllPremiseDepartments(premiseList)
            ]);
          } // TODO: fetchType by IC is not available yet
          // else if (data.fetchType == 'byIc') {
          //   fetchedData = await Promise.all([
          //     fetchByIc(doc.id, data.ic),
          //     fetchPremisesData(premiseList),
          //   ])
          // }
          tableData = fetchedData[0]; // fetchUsersList
          premiseKeyName = fetchedData[1]; // fetchPremisesData
          // allPremiseDepartments = fetchedData[2] //getAllPremiseDepartments
        }
      });

    return [tableData, premiseList, premiseKeyName];
  } catch (e) {
    throw new functions.https.HttpsError('invalid-argument', e.message, e);
  }
};

exports.addRemoveEmp = async (data, context) => {
  // console.log(data.empList)//list of premises where user is employee of
  // console.log(data.selectedPrem)//selected premise to compare
  // console.log(data.id)//user id
  const tempPremEmp = _.cloneDeep(data.empList);// new premise employee list after removing or adding as emp
  const indexSelected = tempPremEmp.indexOf(data.selectedPrem);
  const userRef = db.collection('users').doc(data.id);
  if (_.includes(data.empList, data.selectedPrem)) {
    // console.log(`IS employee, removing`)
    tempPremEmp.splice(indexSelected, 1);
    await userRef.update({
      premisesEmployee: tempPremEmp,
    }).then(async () => {
      await employeeRemove(data.id, data.selectedPrem);
    });
  } else {
    // console.log(`not employee, adding`)
    tempPremEmp.push(data.selectedPrem);
    await userRef.update({
      premisesEmployee: tempPremEmp,
    }).then(async () => {
      await addAsEmployee(data.id, data.selectedPrem);
    });
  }
  return tempPremEmp;
};

exports.updateDisplayName = async (data, context) => {
  try {
    // let userData = {}
    const userRef = db.collection('users').doc(data.id);
    // console.log(`--------------${data.displayName}`);
    return userRef.update({
      displayName: data.displayName,
    });
    // .then(doc=> {
    //   console.log(doc.data())
    //   return userData = doc.data();
    //   return doc.data()
    // })
    // return userData
  } catch (e) {
    throw new functions.https.HttpsError('update_DisplayName_error', e.message);
  }
};

exports.createNewDepartment = async (data, context) => {
  try {
    await checkAdminPermission(context.auth.uid);
    console.log('DATA RECEIVED:', data);
    const newDepartment = {
      premise: data.premise,
      name: data.name,
      createdAt: FieldValue.serverTimestamp(),
      count: 0,
    };
    await db.collection('departments').doc().set(newDepartment);
    return {
      response: 'success',
      message: `Success: Created '${data.name}' under '${data.premise}' premise`,
    };
  } catch (e) {
    throw new functions.https.HttpsError('create_dept_error', e.message);
  }
};

exports.getDepartmentList = async (data, context) => {
  try {
    let allPremiseDepartment;
    const premiseList = data.allowed;
    if (context.auth.uid) {
      await getAllPremiseDepartments(premiseList)
        .then((res) => {
          allPremiseDepartment = res;
        });
      return allPremiseDepartment;
    }
    return 'Insufficient permission';
  } catch (error) {
    throw new functions.https.HttpsError('get_dept_err', error.message);
  }
};

exports.deleteDepartment = async (data, context) => {
  try {
    await checkAdminPermission(context.auth.uid);
    const res = await db.collection('departments').doc(data.docID).delete();
    return res;
  } catch (error) {
    throw new functions.https.HttpsError('deleteDepartment_err', error.message);
  }
};

exports.allocateUserToDepartment = async (data, context) => {
  try {
    await checkAdminPermission(context.auth.uid);
    const departmentIdList = [];
    _.forOwn(data.departmentObj, (value, key) => {
      departmentIdList.push(key);
    });
    console.log(departmentIdList);
    await assignDepartment(data.userId, data.departmentObj, departmentIdList, data.premise);

    return {
      status: 'success',
      data: data.departmentObj,
    };
  } catch (error) {
    throw new functions.https.HttpsError(error, error);
  }
};
// exports.unallocateUserFromDepartment = functions.region('asia-southeast2').https.onCall(async (data, context)=> {
//   try {
//     await checkAdminPermission(context.auth.uid)
//     console.log(data)
//   } catch (e) {
//     throw new functions.https.HttpsError('invalid-argument', e.message);
//   }
// })
const assignDepartment = async (userId, departmentMap, departmentIdList, premise) => {
  console.log('---updating dept', departmentMap);
  const promiseList = [];
  const updateUser = db.collection('users').doc(userId).update({
    departments: departmentMap,
  });
  promiseList.push(updateUser);

  db.collection('employees')
    .where('uid', '==', userId)
    .where('employeeOf', '==', premise)
    .get()
    .then((query) => {
      const doc1 = query.docs[0];
      // console.log(doc1.data());
      const updateEmpDept = doc1.ref.update({ departmentIdList })
        .then(() => {
          console.log('DONE UPDATING EMP ARRAY LIST');
        });
      promiseList.push(updateEmpDept);
      // let tmp = doc1.data();
    });

  await Promise.all(promiseList);
};

exports.getPremisesData = async (data, context) => {
  try {
    await checkAdminPermission(context.auth.uid);
    return await fetchPremisesData(data.id);
  } catch (error) {
    throw new functions.https.HttpsError(error, error);
  }
};

exports.syncWithDB = async (data, context) => {
  try {
    const promises = [];
    _.forEach(data, (o) => {
      const ref = db.collection('users')
        .where('fullName', '==', _.get(o, 'fullName', false))
        .where('identifier', '==', _.get(o, 'identifier', false))
        .get();
      promises.push(ref);
    });
    const latestEventDataSnapshot = await Promise.all(promises);
    // console.log(latestEventDataSnapshot.length)
    latestEventDataSnapshot.map((querySnapshot, idx) => {
      // console.log(querySnapshot.docs.length)
      _.set(data[idx], 'synced', querySnapshot.docs.length);
      if (querySnapshot.docs.length > 0) {
        _.set(data[idx], 'department', _.get(querySnapshot.docs[0].data(), 'department', ''));
        _.set(data[idx], 'icno', _.get(querySnapshot.docs[0].data(), 'icno', ''));
        _.set(data[idx], 'gender', _.get(querySnapshot.docs[0].data(), 'gender', ''));
        _.set(data[idx], 'phoneNumber', _.get(querySnapshot.docs[0].data(), 'phoneNumber', ''));
        _.set(data[idx], 'email', _.get(querySnapshot.docs[0].data(), 'email', ''));
      }
    });
    return data;
  } catch (error) {
    throw new functions.https.HttpsError(error, error);
  }
};

exports.uploadCSVusersToDB = async (data, context) => {
  try {
    // console.log(data)
    let hasNew = false;
    const premiseId = _.get(data, 'premiseId', null);
    const premiseName = _.get(data, 'premiseName', null);
    const batch = db.batch();
    _.forEach(_.get(data, 'users'), (obj) => {
      const cloneOb = _.cloneDeep(obj);
      // _.set(cloneOb, 'status', 'faceless')
      _.set(cloneOb, 'active', false);
      _.unset(cloneOb, '');
      _.unset(cloneOb, 'tableData');
      _.set(cloneOb, `premiseEntry[${premiseId}]`, { access: true, type: 'toggle' });
      _.set(cloneOb, `premises[${premiseId}]`, {
        createdAt: serverTimestamp,
        id: premiseId,
        name: premiseName,
      });
      _.set(cloneOb, 'createdAt', serverTimestamp);
      // _.set(cloneOb, `queryPremises`, [`${premiseId}`]) ⛔ This will be done by onUserWrite trigger
      // console.log(cloneOb);
      const docRef = db.collection('users').doc();
      if (_.get(data, 'onlyNew')) {
        if (_.get(cloneOb, 'synced') === 0) {
          hasNew = true;
          batch.set(docRef, cloneOb);
        }
      } else {
        batch.set(docRef, cloneOb);
      }
    });

    if (hasNew) {
      batch.commit().then(() => {
        console.log('DONE');
      }).then(() => {
        return 'DONE';
      }).catch((e) => alert(e.message));
    } else {
      return false;
    }
    // return hasNew;
  } catch (error) {
    throw new functions.https.HttpsError(error, error);
  }
};

// 🚧🚧🚧 ONLY FOR DEV PURPOSES🔴🔴🔴
exports.removeTestingUsers = async (data, context) => {
  await db.collection('users').where('status', '==', 'testing').get()
    .then((querySnap) => {
      querySnap.forEach((doc) => {
        console.log(doc.id);
        doc.ref.delete();
      });
    });
}; // 🚧🚧🚧 ONLY FOR DEV PURPOSES 🔴🔴🔴

exports.removeUserFromPremise = async (data, context) => {
  try {
    const { uid, premises } = data;
    const uRef = db.collection('users').doc(uid);
    const uData = _.cloneDeep(await uRef.get().then((doc) => doc.data()));
    const newPremises = _.get(uData, 'premises');
    const newEntry = _.get(uData, 'premiseEntry');
    _.map(premises, (p, idx) => {
      _.unset(newPremises, p);
      _.unset(newEntry, p);
    });
    await uRef.update({
      premises: newPremises,
      premiseEntry: newEntry,
    }).then(() => console.log('success'))
      .catch((error) => {
        throw new functions.https.HttpsError('removeUserFromPremise_err', error.message);
      });

    return 'DONE';
  } catch (error) {
    throw new functions.https.HttpsError('removeUserFromPremise_err', error.message);
  }
};

exports.updateAdminPassword = async (data, context) => {
  try {
    const { uid, newPass } = data;
    await changeAdminPassword(uid, newPass);
    return 'done';
  } catch (error) {
    throw new functions.https.HttpsError('updateAdminPassword_err', error.message);
  }
};

const fetchPremisesData = async (premiseList) => {
  const premiseKeyName = {};
  const premisesRef = premiseList.map((id) => {
    return db.collection('premises').doc(id).get();
  });
  await Promise.all(premisesRef).then((docs) => {
    _.map(docs, (doc) => {
      _.set(premiseKeyName, doc.id, doc.data().name);
    });
  });
  return premiseKeyName;
};

const employeeRemove = async (userId, selectedPrem) => {
  const userRef = db.collection('employees')
    .where('uid', '==', userId)
    .where('employeeOf', '==', selectedPrem);
  await userRef.get().then(async (collection) => {
    if (collection.size) {
      // console.log('doc exists')
      // console.log(collection.size)
      const firstDoc = collection.docs[0];
      await db.collection('employees').doc(firstDoc.id).update({
        isActive: false,
        isApproved: false,
        designation: '',
        accessLocal: [],
      });
    } // else {console.log('document does not exist')}
  });
};

const addAsEmployee = async (userId, selectedPrem) => {
  const userRef = db.collection('employees')
    .where('uid', '==', userId)
    .where('employeeOf', '==', selectedPrem);
  userRef.get().then(async (collection) => {
    if (collection.size) {
      // console.log(`num of docs found: ${collection.size}`)
      // console.log(`first docId: ${collection[0].uid}`)
      const firstDoc = collection.docs[0];
      if (_.has(firstDoc.data(), 'department')) {
        console.log('THIS DOC HAS DEPARTMENT----JUST UPDATING');
        db.collection('employees').doc(firstDoc.id).update({
          isActive: true,
          isApproved: true,
        });
      } else {
        console.log('THIS DOC NO DEPARTMENT----fethcing from user collection');
        await db.collection('users').doc(userId).get()
          .then(async (doc) => {
            if (doc.exists) {
              const clone = doc.data();
              await db.collection('employees').doc(firstDoc.id).update({
                isActive: true,
                isApproved: true,
                departments: _.get(clone, 'departments', {}),
              });
            } else {
              console.log('No such user found');
            }
          });
      }
      // .then(() => {
      // console.log('done updating document')
      // })
    } else {
      // console.log('collection empty. Adding')
      await db.collection('users').doc(userId).get()
        .then(async (doc) => {
          if (doc.exists) {
            // console.log('user found')
            const clone = doc.data();
            await db.collection('employees').add({ // not tested yet
              uid: clone.id,
              admins: clone.admins,
              email: clone.email,
              fullName: clone.fullName,
              icNo: clone.icNo,
              phoneNumber: clone.phoneNumber,
              photoUrl: clone.photoUrl,
              // department: filterPremiseDepartment(_.get(clone, 'department', {}), selectedPrem),
              department: _.get(clone, 'departments', {}), // better an array
              // photoFailed: clone.photoFailed,
              // other non-dupe data
              createdAt: serverTimestamp,
              employeeOf: selectedPrem,
              isActive: true,
              designation: '',
              adminDefinedName: clone.fullName,
              isApproved: true,
              accessLocal: [], // access doors in premise
            }).then(() => console.log('finished adding new emp'));
          } else {
            console.log('No such user found');
          }
        });
    }
  });
};

const filterPremiseDepartment = (userDepartment, selectedPrem) => {
  const departmentObject = {};
  _.forOwn(userDepartment, (value, key) => {
    const premiseOfDept = _.split(value, '/')[0];
    if (selectedPrem === premiseOfDept) {
      _.set(departmentObject, key, value);
    }
  });
  return departmentObject;
};

exports.fetchUsersListByPremiseId = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate({ ...req.params, ...req.query, ...req.body }, UserDTO.fetchUserListByPremiseId);
    const response = await UserService.fetchUserListByPremiseId(params);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.fetchAllUserListByPremiseId = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate({ ...req.params, ...req.query, ...req.body }, UserDTO.fetchUserListByPremiseId);
    const response = await UserService.fetchAllUserListByPremiseId(params);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};
exports.fetchUserNumber = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate({ ...req.params, ...req.query, ...req.body }, UserDTO.fetchUserNumber);
    const response = await UserService.fetchUserNumber(params);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.fetchVisitorListByPremiseId = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate({ ...req.params, ...req.query, ...req.body }, UserDTO.fetchVisitorListByPremiseId);
    const response = await UserService.fetchVisitorListByPremiseId(params);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.fetchEmployeeListByPremiseId = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate({ ...req.params, ...req.query, ...req.body }, UserDTO.fetchEmployeeListByPremiseId);
    const response = await UserService.fetchEmployeeListByPremiseId(params);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};
exports.exportUserListByPremiseId = async (req, res, next) => {
  try {
    let idArray = null;
    if (!_.isEmpty(_.get(req, 'query.idArray'))) {
      idArray = _.split(_.get(req, 'query.idArray'), ',');
    }
    const data = {
      ...req.params,
      ...req.query,
      ...req.body,
      idArray,
    };
    const cleanedObject = _.pickBy(data, (value) => !_.isEmpty(value));
    const params = await asyncJoiValidate(cleanedObject, UserDTO.exportUserListByPremiseId);
    const response = await UserService.exportUserListByPremiseId(params);
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      'Content-Disposition',
      'attachment; filename=' + 'user_list.xlsx',
    );
    if (_.isEmpty(response)) {
      throw response.failure('Fail to create user record');
    }
    return response?.xlsx?.write(res).then(() => {
      res.status(200).end();
    });
  } catch (err) {
    return next(err);
  }
};

const fetchByIc = async (adminId, userIc) => {
  // TODO: incomplete, TBC in the future, right now
  const userList = [];
  return userList;
};

exports.updateUser = (facepassServer) => async (req, res, next) => {
  try {
    let faceImageUrl = _.get(req, 'body.faceImage');
    if (!_.isEmpty(_.get(req, 'file'))) {
      const imageObj = req.file;
      const imageBuffer = readFileSync(_.get(imageObj, 'path', ''));
      faceImageUrl = await uploadImage(imageBuffer, 'faces');
    }

    const body = {
      // ...req.user,
      ...req.body,
      faceImageUrl,
    };
    console.log("_.get(req, 'file'):", _.get(req, 'file'));
    console.log("_.get(req, 'body.faceImageUrl'):", _.get(req, 'body.faceImage'));
    console.log('faceImageUrl', faceImageUrl);
    console.log('create user body:', body);
    const params = await asyncJoiValidate(body, UserDTO.updateUser);
    const response = await UserService.updateUser(params, req.user);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.deleteUser = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate(req.params, UserDTO.deleteUser);
    const responses = await UserService.deleteUser(params, req.user);
    return res.success(responses);
  } catch (err) {
    return next(err);
  }
};

exports.deleteUserUpdateAccess = async (req, res, next) => {
  // Because phone number is required to be unique so when recreating the same user, it will throw error if same number is used
  try {
    const params = await asyncJoiValidate(req.params, UserDTO.deleteUser);
    const responses = await UserService.deleteUserUpdateAccess(params, req.user);
    return res.success(responses);
  } catch (err) {
    return next(err);
  }
};

exports.fetchUserByAdminApiKey = async (req, res, next) => {
  try {
    const { authorization } = req.headers;
    const params = await asyncJoiValidate({
      ...req.params,
      ...req.query,
    }, UserDTO.fetchUser);
    console.log('params', params);
    const response = await UserService.getUser(params, authorization);
    return res.success(response);
  } catch (error) {
    return next(error);
  }
};

exports.updateUserWithoutPremise = (facepassServer) => async (req, res, next) => {
  try {
    const { authorization } = req.headers;
    const getApiKeyDoc = await AdminApikeyModel.findOne({ apiKey: authorization });
    console.log('req.file', req.file);
    console.log('req.body', req.body);
    let faceImageUrl = '';
    if (!_.isEmpty(_.get(req, 'file'))) {
      const imageObj = req.file;
      const imageBuffer = readFileSync(_.get(imageObj, 'path', ''));
      faceImageUrl = await uploadImage(imageBuffer, 'faces');
    }
    const body = {
      ...req.body,
      ...(faceImageUrl !== '' && { faceImageUrl }),

    };
    console.log('updateUserByKey body:', body);
    const params = await asyncJoiValidate(body, UserDTO.updateUserWithoutPremise);
    const response = await UserService.updateUserByApiKeyWithoutPremise(params, authorization, facepassServer);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.updateUserByKey = (facePassServer) => async (req, res, next) => {
  try {
    const { authorization } = req.headers;
    const getApiKeyDoc = await AdminApikeyModel.findOne({ apiKey: authorization });
    console.log('req.file', req.file);
    console.log('req.body', req.body);
    let faceImageUrl = '';
    if (!_.isEmpty(_.get(req, 'file'))) {
      const imageObj = req.file;
      const imageBuffer = readFileSync(_.get(imageObj, 'path', ''));
      faceImageUrl = await uploadImage(imageBuffer, 'faces');
    }
    const body = {
      ...req.body,
      premiseId: getApiKeyDoc.premises[0].premiseId.toString(),
      ...(faceImageUrl !== '' && { faceImageUrl }),

    };
    console.log('updateUserByKey body:', body);
    const params = await asyncJoiValidate(body, UserDTO.updateUser);
    const response = await UserService.updateUserByApiKey(params, authorization)(facePassServer);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.deleteUserUsingAdminKey = async (req, res, next) => {
  try {
    const { authorization } = req.headers;
    console.log('req.params', req.params);
    const params = await asyncJoiValidate(req.params, UserDTO.deleteUser);
    const response = await UserService.deleteUserWithAdminKey(params, authorization);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.deleteUserByExternal = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate(req.query, UserDTO.deleteUserByExternal);
    const response = await UserService.deleteUserByExternal(params);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.importXLSX = async (req, res, next) => {
  try {
    const url = await uploadToGCS(req);
    console.log(url);
    if (_.isEmpty(url)) {
      throw ('Empty XLSX');
    }
    const response = await UserService.importXLSX(url, req.user);
    return res.success(response);
  } catch (err) {
    // let isLive = true;
    // if (_.get(req, 'hostname') == 'localhost') {
    //   isLive = false;
    // }
    // await backendErrorModel.create({
    //   apiName: 'importXLSX', error: JSON.stringify(err), type: 'dorm-backend', isLive, admin: _.get(req, 'user._id'),
    // });
    return next(err);
  }
};

exports.fetchUserListByParams = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate({
      ...req.query,
      adminPremiseId: req.query.premiseId,
    }, UserDTO.fetchUserListByParams);
    const response = await UserService.fetchUserListByParams(params);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};
