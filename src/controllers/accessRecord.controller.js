const { asyncJoiValidate } = require('#helpers/index');
const accessService = require('#services/access.service');
const AccessDTO = require('#dtos/access.dto');
const moment = require('moment');
const _ = require('lodash');
const { excelAttendanceCheckInCheckOut } = require('#services/attendance.service');

exports.getAllAccessRecords = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate({ ...req.params, ...req.query }, AccessDTO.getAllAccessRecords);
    console.log(params);
    const response = await accessService.getAllAccessRecords(params);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.getTodayVisitorRecords = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate(req.params, AccessDTO.getTodayVisitorRecords);
    const response = await accessService.getTodayVisitorRecords(params);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.getAllAccessRecordsExternal = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate(req.params, AccessDTO.getAllAccessRecords);
    console.log(params);
    const response = await accessService.getAllAccessRecordsExternal(params);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.getAccessRecordsByPremise = async (req, res, next) => {
  try {
    const startDate = _.get(req, 'body.startDate') ? moment(req.body?.startDate).toDate() : moment('2023-01-01').toDate();
    const endDate = _.get(req, 'body.endDate') ? moment(req.body?.endDate).toDate() : moment().toDate();
    const body = {
      ...req.params,
      ...req.body,
      startDate,
      endDate,
    };
    console.log('BODY', body);
    const params = await asyncJoiValidate(body, AccessDTO.getAcessRecordbyPremise);
    const response = await accessService.getScanRecordsByPremise(params, req.user);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.getAttendanceReport = async (req, res, next) => {
  try {
    const paramBody = {
      ...req.params,
      ...req.body,
      ...req.query,
      startDate: moment(req.body?.startDate).toDate(),
      endDate: moment(req.body?.endDate).toDate(),
    };
    const params = await asyncJoiValidate(paramBody, AccessDTO.generateExcelAttendanceReport);
    const attendanceDataParams = await accessService.processAttendanceExternal(params, _.get(params, 'premiseId'));
    const workbookAttendance = await excelAttendanceCheckInCheckOut({
      attendanceData: attendanceDataParams.attendanceData,
      endUnix: attendanceDataParams.endUnix,
      startUnix: attendanceDataParams.startUnix,
      premiseFullName: attendanceDataParams.premiseFullName,
    });
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      'Content-Disposition',
      'attachment; filename=' + 'attendance_record.xlsx',
    );
    if (_.isEmpty(workbookAttendance)) {
      throw workbookAttendance.failure('Fail to create attendance record');
    }
    return workbookAttendance?.xlsx?.write(res).then(() => {
      res.status(200).end();
    });
    // const filebuffer = await workbookAttendance.xlsx.writeBuffer();
    // res.set({
    //   'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    //   'Content-Disposition': 'attachment;',
    // });
    // return res.success({ data: filebuffer });
    // return res.success(attendanceDataParams);
  } catch (error) {
    return next(error);
  }
};

exports.getAccessRecordReport = async (req, res, next) => {
  try {
    const paramBody = {
      ...req.params,
      ...req.body,
      ...req.query,
      startDate: moment(req.body?.startDate).toDate(),
      endDate: moment(req.body?.endDate).toDate(),
    };
    const params = await asyncJoiValidate(paramBody, AccessDTO.exportAccessRecordExternal);
    const responses = await accessService.exportAccessRecordExternal(params, _.get(params, 'premiseId'));

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      'Content-Disposition',
      'attachment; filename=attendance_record.xlsx',
    );
    if (_.isEmpty(responses)) {
      throw responses.failure('Fail to create attendance record');
    }
    return responses?.xlsx?.write(res).then(() => {
      res.status(200).end();
    });
    // const filebuffer = await workbookAttendance.xlsx.writeBuffer();
    // res.set({
    //   'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    //   'Content-Disposition': 'attachment;',
    // });
    // return res.success({ data: filebuffer });
    // return res.success(attendanceDataParams);
  } catch (error) {
    return next(error);
  }
};

exports.getAccessRecordsByUser = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate({
      ...req.body,
      ...req.query,
      premiseId: req.query.premise,
    }, AccessDTO.getAccessRecordsByUser);
    const response = await accessService.getAccessRecordsByUser(params);

    return res.success(response);
  } catch (err) {
    return next(err);
  }
};
