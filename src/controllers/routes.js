const express = require('express');
const premiseController = require('#controllers/premise.controller');
const shiftController = require('#controllers/shift.controller');
const staffController = require('#controllers/staff.controller');
const userController = require('#controllers/user.controller');
const employeeController = require('#controllers/employee.controller');
const multer = require('multer');
const { default: mongoose } = require('mongoose');
const requireAuth = require('#middlewares/requireAuth');
const onlyAllow = require('#middlewares/onlyAllow');
const DeviceController = require('#controllers/device.controller');
const ChzeController = require('#controllers/chze.controller');
const StatisticController = require('#controllers/statistic.controller');
const AccessRecordController = require('#controllers/accessRecord.controller');
const DepartmentController = require('#controllers/department.controller');
const AccessController = require('#controllers/access.controller');
const RequestController = require('#controllers/request.controller');
const requireApiKey = require('#middlewares/requireApiKey');
const SerialNumberModel = require('#models/SerialNumber.model');
const { clearFaceCache, emitToDeviceToOpenDoor } = require('../../script');

const upload = multer({ dest: 'uploads/' });
const bufferUpload = multer({
	storage: multer.memoryStorage(),
	limits: { fileSize: 10 * 1024 * 1024, fieldSize: 25 * 1024 * 1024 }, // Limit the file size to 10MB (in bytes)

});
module.exports = (facePassServer) => {
	const router = express.Router();

	// // auth controller
	// router.post('/auth/register', authController.register);
	// router.post('/auth/forgot-password', authController.forgotPassword);
	// router.post('/auth/reset-password', authController.resetPassword);
	// router.post('/auth/register-profile', authController.registerProfile);

	// // profile controller
	// router.get('/profile', isLoggedIn, profileController.getProfile);
	// router.put('/profile', isLoggedIn, profileController.updateProfile);

	// // employee controller
	router.post('/employee/add-to-premise', requireAuth('platform-admin'), employeeController.addEmployeeToPremise);
	router.post('/employee', requireAuth('platform-admin', 'premise-admin'), upload.single('faceImage'), employeeController.createEmployee(facePassServer));

	// router.get('/employee/fetch-admin-allowed', fetchAdminAllowed);
	// router.get('/employee/fetch-raw-data', fetchRawData);
	// router.get('/employee/handle-delete', handleDelete);
	// router.get('/employee/handle-new-create', handleNewCreate);
	// router.get('/employee/save-premise-settings', savePremiseSetting);

	// // export file controller
	// router.get('/schedule-caching-scans', scheduledCachingScansController.scheduledCachingScans);
	// router.get('/export-attendance-data', scheduledCachingScansController.exportAttendanceData);

	// // export hilton excel controller
	// router.get('/export-hilton-excel', hiltonExcelController.exportAllByDayHilton);

	// // graph controller
	// router.get('/graph-data', graphController.getGraphData);
	// router.get('/premise-list', graphController.getPremiseList);
	// router.get('/related-device', graphController.getRelatedDevices);

	// // live controller
	// router.get('/allowed-premise-live-graph', liveController.fetchAllowedPremLiveGraph);
	// router.get('/data-for-analysis', liveController.fetchDataForAnalysis2);

	// // message controller
	// router.get('/announcement', messageController.getAnnouncements);
	// router.post('/announcement', messageController.postAnnouncement);
	// router.delete('/announcement', messageController.deleteAnnouncement);

	// premise controller
	router.post('/premise', requireAuth('platform-admin'), upload.single('premiseLogoImage'), premiseController.createPremise);
	router.post('/external-account', upload.single('premiseLogoImage'), premiseController.createPremiseReturningAdminApiKey);
	router.post('/add-admin-to-premise', requireAuth('platform-admin'), premiseController.addAdminToPremise);
	router.post('/add-children-to-premise', requireAuth('platform-admin'), upload.single('premiseLogoImage'), premiseController.addChildrenToPremise);
	router.post('/premise/external', requireApiKey(), upload.single('premiseLogoImage'), premiseController.createPremiseExternal);
	router.put('/premise/external', requireApiKey(), upload.single('premiseLogoImage'), premiseController.updatePremiseExternal(facePassServer));
	router.get('/premises/external', requireApiKey(), premiseController.getAllPremisesByAPIKey);
	router.get('/premises/external/:premiseID', requireApiKey(), premiseController.getPremiseExternalByPremiseId);

	router.put('/premise', requireAuth('platform-admin', 'premise-admin'), upload.single('premiseLogoImage'), premiseController.updatePremise);
	router.get('/premises', requireAuth('platform-admin', 'premise-admin'), premiseController.getAllPremises);
	router.get('/premise/api', premiseController.getPremiseByAPI);
	router.get('/premise-raw-scans', requireApiKey(), premiseController.getAllPremisesByAPIKey);
	router.delete('/premise/external/:premiseID', requireApiKey(), premiseController.deletePremiseExternal);

	// department controller
	router.post('/premise/:premiseId/departments', requireAuth('platform-admin', 'premise-admin'), DepartmentController.createDepartmentByPremiseId);
	router.get('/premise/:premiseId/departments', requireAuth('platform-admin', 'premise-admin'), DepartmentController.getDepartmentsByPremiseId);
	router.get('/department', DepartmentController.getDepartmentByAPIKeyAndSerialNumber);
	router.delete('/premise/:premiseId/departments/:departmentId', requireAuth('platform-admin', 'premise-admin'), DepartmentController.deleteDepartmentByPremiseId);

	// device controller
	router.get('/device/settings', DeviceController.getDeviceSettings);
  router.get('/device/userCount', DeviceController.getDeviceUserCount);
	router.post('/device/settings', requireAuth('platform-admin', 'premise-admin'), DeviceController.setDeviceSettings(facePassServer));
	router.put('/premises/:premiseId/devices/:serialNumber', requireAuth('platform-admin', 'premise-admin'), DeviceController.updateDevice);
	router.delete('/premises/:premiseId/devices/:serialNumber', requireAuth('platform-admin', 'premise-admin'), DeviceController.deleteDevice);
	router.post('/premises/:premiseId/devices/:serialNumber/reboot', requireAuth('platform-admin', 'premise-admin'), DeviceController.rebootDevice(facePassServer));
	router.post('/premises/:premiseId/devices/:serialNumber/restart', requireAuth('platform-admin', 'premise-admin'), DeviceController.restartDevice(facePassServer));
	router.post('/premises/:premiseId/devices/:serialNumber/force-sync', requireAuth('platform-admin', 'premise-admin'), DeviceController.forceSyncDevice(facePassServer));
	router.post('/premises/:premiseId/devices/:serialNumber/force-sync-with-api-key', requireApiKey(), DeviceController.forceSyncDeviceWithApiKey(facePassServer));
	router.post('/premises/devices/:serialNumber/reboot', requireApiKey(), DeviceController.rebootDeviceWithApiKey(facePassServer));

	// Chze add
	router.get('/device/all', ChzeController.getAllDevices);
	router.get('/accessRecord/all', ChzeController.testGetAllRecords);
	router.get('/premise/all', ChzeController.getAllPremises);
	router.get('/device/getSetting', ChzeController.getDevicesSettingWithoutPremises);
	router.post('/call/start', ChzeController.createCall);
	router.post('/department', ChzeController.createNewDepartment);

	router.get('/:premiseId/devices', requireAuth('platform-admin', 'premise-admin'), DeviceController.getAllDevices);
	router.get('/accessRecord/all', DeviceController.testGetAllRecords);

	// access record controller

	router.get('/premises-external/access-records', requireApiKey(), AccessRecordController.getAllAccessRecordsExternal);
	router.get('/premises-external/access-records/attendance', requireApiKey(), AccessRecordController.getAttendanceReport);
 	router.post('/access-records/download', AccessRecordController.getAccessRecordReport);// is acually a get request
 	router.post('/attendance/download', AccessRecordController.getAttendanceReport);// is acually a get requestis acually a get request
	router.get('/accessRecords/byUser', requireApiKey(), AccessRecordController.getAccessRecordsByUser);
	router.post('/accessRecords/byUser', requireApiKey(), AccessRecordController.getAccessRecordsByUser);

	router.get('/accessRecord/:premiseIdToQuery', requireAuth('platform-admin', 'premise-admin'), AccessRecordController.getAccessRecordsByPremise);
	router.get('/premises/:premiseId/access-records', requireAuth('platform-admin', 'premise-admin'), AccessRecordController.getAllAccessRecords);
	router.get('/premises/:premiseId/access-records/visitor/today', requireAuth('platform-admin', 'premise-admin'), AccessRecordController.getTodayVisitorRecords);

	// access controller
	router.put('/premises/:premiseId/users/:userId/assign-to-department', requireAuth('platform-admin', 'premise-admin'), AccessController.assignUserToDepartments);
	router.post('/accesses-external/:userId/:premiseIdToQuery', requireApiKey(), AccessController.createNewAccesses);
	router.post('/accesses-external-by-body', requireApiKey(), AccessController.createNewAccessesByBody(facePassServer));
	router.get('/devices-by-admin-premise', requireApiKey(), DeviceController.getAllDeviceWithApiKey);
	router.get('/devices-by-premisekey', requireApiKey(), DeviceController.getAllDeviceWithApiKey);
	router.post('/device/settings-external', requireApiKey(), DeviceController.setDeviceSettings(facePassServer));
	router.put('/accesses-external/:userId', requireApiKey(), AccessController.restoreUserAccess);
	router.put('/bulk-accesses-external', requireApiKey(), AccessController.bulkExternalAccess(facePassServer));
	router.get('/search-users-by-premise', requireApiKey(), AccessController.searchUsersAccessByPremise);
	router.get('/search-users-with-premises', requireApiKey(), AccessController.searchPremisesBelongToUser);
	// // shift controller
	// router.get('/', shiftController.getPremiseShift);
	// router.post('/shift-and-public-holiday', shiftController.setShiftAndPHdata);

	// // staff manage controller
	// router.get('/employees', staffController.fetchEmployees);
	// router.put('/employees', staffController.updateEmp);
	// router.delete('/employees', staffController.removeEmp);
	// router.get('/leaves-by-premise', staffController.fetchLeavesByPremise);
	// router.get('/employee-page-allowed-premise', staffController.empPageAllowedPremise);
	// router.get('/link-email-to-qv', staffController.linkEmailToQVApp);
	// router.put('/leave-status', staffController.updateLeaveStatus);
	// router.put('/fix-leave-status', staffController.updateLeaveStatusFix);

	// request controller
	router.get('/request', requireAuth('premise-admin', 'platform-admin'), RequestController.getAllRequests);
	router.get('/request/id/:premiseId', requireAuth('premise-admin', 'platform-admin'), RequestController.getRequestById);
	router.post('/request', requireAuth('premise-admin', 'platform-admin'), bufferUpload.any(), RequestController.createRequest);
	router.put('/request', requireAuth('premise-admin', 'platform-admin'), bufferUpload.any(), RequestController.updateRequest);
	router.delete('/request', requireAuth('premise-admin', 'platform-admin'), bufferUpload.any(), RequestController.deleteRequest);

	// // user controller
	router.post(
		'/users',
		requireAuth('platform-admin', 'premise-admin'),
		upload.single('faceImage'),
		userController.createUser(facePassServer),
	);
	// router.get('/user-list', requireAuth('premise-admin'), userController.fetchUserList);
	router.put('/users', requireAuth('platform-admin', 'premise-admin'), upload.single('faceImage'), userController.updateUser(facePassServer));
	router.delete(
		'/premises/:premiseId/users/:userId',
		requireAuth('platform-admin', 'premise-admin'),
		userController.deleteUser,
	);
	router.delete('/premises-access/:premiseId/users/:userId', requireAuth('platform-admin', 'premise-admin'), userController.deleteUserUpdateAccess);

	// endpoints for admins that authenticate with API keys
	router.post('/users-external', requireApiKey(), upload.single('faceImage'), userController.createUserWithKey(facePassServer));
	router.post('/users-external-with-premise', requireApiKey(), upload.single('faceImage'), userController.createUserInCustomPremiseWithKey(facePassServer));
	router.post('/users-external-without-premise', requireApiKey(), upload.single('faceImage'), userController.createUserWithoutPremise(facePassServer));
	// this one specified premiseId
	router.post('/users-premise-external', requireApiKey(), upload.single('faceImage'), userController.createUserWithKeyPremiseId(facePassServer));
	router.put('/users-external', requireApiKey(), upload.single('faceImage'), userController.updateUserByKey(facePassServer)); // ✅
	router.put('/users-external-without-premise', requireApiKey(), upload.single('faceImage'), userController.updateUserWithoutPremise(facePassServer)); // ✅
	router.get('/users-external-list-with-records', requireApiKey(), userController.fetchUsersListByPremiseId); // ✅
	router.get('/users-external-list', requireApiKey(), userController.fetchExternalUserList);
	router.get('/users-external-list-by-params', requireApiKey(), userController.fetchUserListByParams);
	router.get('/users-external/:userId', requireApiKey(), userController.fetchUserByAdminApiKey);
	router.delete('/accesses-external/:userId', requireApiKey(), AccessController.deleteUserAccessUsingAdminKey);// ✅
	router.delete('/users-external/:userId', requireApiKey(), userController.deleteUserUsingAdminKey);// ✅
	router.delete('/users-external', requireApiKey(), userController.deleteUserByExternal);// ✅
	router.get('/accessRecord-external/:premiseIdToQuery', requireApiKey(), AccessRecordController.getAccessRecordsByPremise);
	router.get('/premise/api', premiseController.getPremiseByAPI);
	router.get('/:premiseId/user-list', requireAuth('premise-admin', 'platform-admin'), userController.fetchUsersListByPremiseId);
	router.get('/:premiseId/user-list/all', requireAuth('premise-admin', 'platform-admin'), userController.fetchAllUserListByPremiseId);

	router.get('/:premiseId/user-list/number', requireAuth('premise-admin', 'platform-admin'), userController.fetchUserNumber);
	router.get('/:premiseId/visitor-list', requireAuth('premise-admin', 'platform-admin'), userController.fetchVisitorListByPremiseId);
	router.get('/:premiseId/employee-list', requireAuth('premise-admin', 'platform-admin'), userController.fetchEmployeeListByPremiseId);
	router.get('/:premiseId/user-list/export', requireAuth('premise-admin', 'platform-admin'), userController.exportUserListByPremiseId);
	router.post('/user/user-list/importXLSX', requireAuth('premise-admin', 'platform-admin'), bufferUpload.single('xlsx'), userController.importXLSX);

	// router.put('/accesses-external/globalReset', requireApiKey(), AccessController.resetIsDelete);
	// router.put('/premise-external', requireApiKey('platform-admin', 'premise-admin'), upload.single('premiseLogoImage'), premiseController.updatePremiseByApiKey);
	router.post('/door-control', requireApiKey(), DeviceController.openDoorManual(facePassServer));

	// callback url
	router.put('/callback', requireApiKey(), premiseController.updatePremiseCallbackURL);

	// router.get('/premise/api', requireApiKey(), premiseController.getPremiseByAPI);
	// router.get('/add-remove-employee', userController.addRemoveEmp);
	// router.post('/allocate-user-to-department', userController.allocateUserToDepartment);
	// router.post('/department', userController.createNewDepartment);
	// router.delete('/department', userController.deleteDepartment);
	// router.get('/one-user-scans', userController.exportOneUserScans);
	// router.get('/department-list', userController.getDepartmentList);
	// router.get('/next-user-scans', userController.getNextUserScans);
	// router.get('/premises', userController.getPremisesData);
	// router.get('/user-scans', userController.getUserScans);
	// router.delete('/remove-testing-users', userController.removeTestingUsers);
	// router.delete('/remove-user-from-premise', userController.removeUserFromPremise);
	// router.get('/sync-with-db', userController.syncWithDB);
	// router.put('/admin-password', userController.updateAdminPassword);
	// router.put('/display-name', userController.updateDisplayName);
	// router.put('/upload-csv-users-to-db', userController.uploadCSVusersToDB);

	// statistic controller
	router.get('/dashboard', StatisticController.getDashboardData);
	router.get('/employees/attendance-records', requireAuth('platform-admin', 'premise-admin'), StatisticController.getAttendanceRecords);
	router.get('/employees/attendance-records-byApiKey', requireApiKey(), StatisticController.getAttendanceRecords);
	router.post('/test', ChzeController.test);
	router.get('/dashboard/getAttendanceRates', StatisticController.getAttendanceRates);
	router.get('/dashboard/getScanStatistic', StatisticController.getScanStatistic);

	// script
	router.post('/clear-face-cache', async (req, res, next) => {
		try {
			const { devicePremiseId, serialNo } = req.body;
			await clearFaceCache(devicePremiseId, serialNo, facePassServer);
			return res.success('face cache cleared successfully!');
		} catch (err) {
			console.log('error during clearing face cache:', err);
			return next(err);
		}
	});

	router.get('/callback-dev', async (req, res, next) => {
		console.log('request', req.body);
	});

	router.post('/open-door', async (req, res, next) => {
		try {
			const { devicePremiseId, serialNo } = req.body;
			await emitToDeviceToOpenDoor(devicePremiseId, serialNo, facePassServer);
			return res.success('door opened successfully!');
		} catch (err) {
			console.log('error during opening door:', err);
			return next(err);
		}
	});

	// // dev controller
	// router.get('/test-send-to-device', (req, res, next) => {
	//   facePassServer.emit('spo', 'data', {});

	//   return res.send('ok');
	// });

	// router.get('/test-transaction', async (req, res, next) => {
	//   try {
	//     const session = await mongoose.startSession();

	//     await session.withTransaction(async () => {
	//       throw 'Some error';
	//     });

	//     return res.send('OK');
	//   } catch (err) {
	//     return next(err);
	//   }
	// });

	router.get('/serial-number-by-external', async (req, res, next) => {
		const session = await mongoose.startSession();
		session.startTransaction();
		try {
			const { apikey } = req.headers;
			if (apikey === 'Mm58TZibeie38aodWA7dJJx2nPUUPtRuXMeQH8VV') {
				const latestSerialNumber = await SerialNumberModel.findOneAndUpdate({
					_id: '64dd9e844326ebe5597c286b',
				}, {
					$inc: {
						counter: 1,
					},
				}, {
					new: true,
					session,
				});

				await session.commitTransaction();
				await session.endSession();

				return res.success({
					serialNumber: latestSerialNumber.counter,
				});
			}
			await session.abortTransaction();
			throw 'you are not authorized.';
		} catch (err) {
			return next(err);
		}
	});

	// TO DO LIST
	// check whether user does exist in both firebase and mongodb. if no, throw error and redirect them to register profile.
	return router;
};
