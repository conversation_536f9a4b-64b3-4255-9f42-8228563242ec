// const functions = require('firebase-functions');
// const admin = require('firebase-admin');

// const serverTimestamp = admin.firestore.FieldValue.serverTimestamp();
// const db = admin.firestore();
// const { FieldValue } = admin.firestore;
// const _ = require('lodash');
// const cors = require('cors')({ Origin: true });
// const moment = require('moment-timezone');
// const { checkAdminPermission } = require('./common/checkAdminPermission');
// const { getAllPremiseDepartments } = require('./common/getAllPremiseDepartments');

// moment.tz.setDefault('Asia/Kuala_Lumpur');

// exports.empPageAllowedPremise = async (data, context) => {
//   try {
//     let premAllowed = [];
//     let selectedPrem = '';
//     // let employeeList = []
//     // let departmentList = []
//     let groupFetch = [];
//     await checkAdminPermission(context.auth.uid).then(async (res) => {
//       premAllowed = res.allowedPremises;
//       // console.log(`Premise Allowed: ${premAllowed}`)
//       if (!_.isNil(premAllowed)) {
//         if (premAllowed.includes('spo')) {
//           selectedPrem = 'spo';
//           groupFetch = await Promise.all([
//             fetchEmployeesUnderPrem('spo'),
//             getAllPremiseDepartments(premAllowed),
//             getPremiseDetails(premAllowed),
//           ]);
//           // employeeList = await fetchEmployeesUnderPrem('spo')
//           // departmentList = await getAllPremiseDepartments(['spo'])
//         } else {
//           selectedPrem = premAllowed[0];
//           groupFetch = await Promise.all([
//             fetchEmployeesUnderPrem(premAllowed[0]),
//             getAllPremiseDepartments(premAllowed),
//             getPremiseDetails(premAllowed),
//           ]);
//           // employeeList = await fetchEmployeesUnderPrem(premAllowed[0])
//           // departmentList = await getAllPremiseDepartments([premAllowed[0]])
//         }
//       }
//     });
//     return {
//       premAllowed,
//       selectedPrem,
//       employees: groupFetch[0],
//       departments: groupFetch[1],
//       premisesInfo: groupFetch[2],
//     };
//   } catch (e) {
//     throw new functions.https.HttpsError('invalid-argument', e.message);
//   }
// };

// exports.fetchEmployees = async (data, context) => {
//   try {
//     const { selectedPrem } = data;
//     // console.log(selectedPrem)
//     // const userIdList = [];
//     // let tempEmp = []
//     let newEmpList = [];
//     await checkAdminPermission(context.auth.uid).then(async (res) => {
//       if (res.allowedPremises.includes(selectedPrem)) {
//         newEmpList = await fetchEmployeesUnderPrem(selectedPrem);
//       } else {
//         throw 'you do not have permission';
//       }
//     });
//     return newEmpList;
//   } catch (e) {
//     throw new functions.https.HttpsError('invalid-argument', e.message);
//   }
// };

// exports.removeEmp = async (data, context) => {
//   try {
//     // let tempEmp = []
//     await checkAdminPermission(context.auth.uid);
//     await db.collection('employees')
//       .where('icNo', '==', data.rowData.icNo)
//       .where('employeeOf', '==', data.premise)
//       .get()
//       .then((querysnap) => {
//         querysnap.forEach((doc) => {
//           doc.ref.update({
//             isActive: false,
//             isApproved: false,
//             designation: '',
//             accessLocal: [],
//           });
//         });
//       });
//     // return tempEmp
//   } catch (e) {
//     throw new functions.https.HttpsError('invalid-argument', e.message);
//   }
// };

// exports.updateEmp = async (data, context) => {
//   try {
//     await checkAdminPermission(context.auth.uid);
//     return db.collection('employees')
//       .where('uid', '==', data.userId)
//       .where('employeeOf', '==', data.premise).get()
//       .then((querysnap) => {
//         querysnap.forEach((doc) => {
//           doc.ref.update({
//             designation: data.designation,
//             fullName: data.fullName,
//             icNo: data.icNumber,
//             phoneNumber: data.phNum,
//             email: data.email,
//             // displayName:data.displayName, //this is updated in users table
//           }).catch((e) => {
//             throw new functions.https.HttpsError('invalid-argument', e.message);
//           });
//         });
//       });
//   } catch (e) {
//     throw new functions.https.HttpsError('invalid-argument', e.message);
//   }
// };

// exports.linkEmailToQVApp = async (data, context) => {
//   try {
//     await checkAdminPermission(context.auth.uid);
//     console.log(data);
//     // return data;
//     return db.collection('users').doc(data.id)
//       .update({
//         qvAccount: data.qvEmail,
//       });
//   } catch (error) {
//     throw new functions.https.HttpsError('invalid-argument', error.message);
//   }
// };

// // TODO: To delete function
// // exports.getAllLeavesForPremise = functions.region('asia-southeast2').https.onCall(async (data, context) => {
// // 	try {
// // 		return null;
// // 		await checkAdminPermission(context.auth.uid)
// // 		const dataList = []
// // 		!_.isNil(data.premiseId) ?
// // 			await db.collection('leaves').where('premiseId', '==', data.premiseId).get()
// // 				.then(querySnap => {
// // 					querySnap.forEach(doc => {
// // 						dataList.push(doc.data())
// // 					})
// // 				}) : null
// // 		return dataList;
// // 	} catch (error) {
// // 		throw new functions.https.HttpsError('function to get leaves', error.message);
// // 	}
// // })

// exports.fetchLeavesByPremise = async (data, context) => {
//   try {
//     await checkAdminPermission(context.auth.uid);
//     const dataList = [];
//     await db.collection('leaves').where('premiseId', '==', _.get(data, 'premiseId'))
//       .get()
//       .then((querySnap) => {
//         querySnap.forEach((doc) => {
//           const cloneData = _.cloneDeep(doc.data());
//           dataList.push(cloneData);
//         });
//       });
//     return dataList;
//   } catch (error) {
//     throw new functions.https.HttpsError('function to get leaves', error.message);
//   }
// };

// exports.updateLeaveStatus = async (data, context) => {
//   try {
//     return 'SUCCESS';
//     // console.log(`Leave update: ${data.docID}: ${data.status}: ${data.userID}`)
//     // const userLeaveRef = db.collection('users').doc(data.userID).collection('leaves').doc(data.docID);
//     // const rootRef = db.collection('leaves').doc(data.docID);
//     // const batch = db.batch()
//     // batch.update(userLeaveRef, { 'approvalState': data.status })
//     // batch.update(rootRef, { 'approvalState': data.status })
//     // await batch.commit();
//   } catch (error) {
//     throw new functions.https.HttpsError('function to get leaves-', error.message);
//   }
// };

// exports.updateLeaveStatusFix = async (data, context) => {
//   try {
//     return 'ERROR';
//   } catch (error) {
//     throw new functions.https.HttpsError('function to get leaves', error.message);
//   }
// };

// // TODO:
// // Cuurently the display name is only in users collection, so each time they go into employees page, it has to fetch from user page their display name
// // the display name on users table and employee page must be the same
// // suggesting create one function that will update both users and employees collection when displayname is edited on either page: both share the same function

// const getDisplayNameFromUid = async (uids) => {
//   try {
//     let displayNameList;
//     const itemRefs = uids.map((id) => {
//       return db.collection('users').doc(id).get();
//     });
//     await Promise.all(itemRefs).then((docs) => {
//       displayNameList = docs.map((doc) => {
//         return {
//           displayName: _.get(doc.data(), 'displayName', ''),
//           departments: _.get(doc.data(), 'departments', {}),
//           qvAccount: _.get(doc.data(), 'qvAccount', false),
//         };
//       });
//     }).catch((error) => console.log(error));
//     //  console.log('displayNameList', displayNameList);
//     return displayNameList;
//   } catch (error) {
//     throw new functions.https.HttpsError('getDisplayNameFromUid', error);
//   }
// };

// const fetchEmployeesUnderPrem = async (selectedPrem) => {
//   const tempEmp = [];
//   const userIdList = [];
//   const newEmpList = [];
//   // let trackNoDepartment = []
//   await db.collection('employees')
//     .where('employeeOf', '==', selectedPrem)
//     .where('isActive', '==', true).get()
//     .then(async (col) => {
//       col.forEach((doc) => {
//         const emp = doc.data();
//         if (emp.isApproved) {
//           userIdList.push(emp.uid);
//           tempEmp.push({
//             adminDefinedName: emp.adminDefinedName,
//             icNo: emp.icNo,
//             userId: emp.uid,
//             designation: emp.designation,
//             email: emp.email,
//             // fullName: emp.fullName,
//             fullName: _.get(emp, 'fullName', ''),
//             departmentIdList: _.get(emp, 'departmentIdList', 'none'), // different from department
//             // displayName: _.get(emp,'emp.displayName', ''),
//             phoneNumber: emp.phoneNumber,
//             // photoFailed: emp.photoFailed,
//             photoUrl: emp.photoUrl,
//             isAccepted: emp.isApproved,
//             accessLocal: emp.accessLocal, // access doors in premise
//             // qvAccount: _.get(emp, 'qvAccount', false),
//           });
//           // if (!_.has(emp, 'departments')) {
//           // 	trackNoDepartment.push(tempEmp.length - 1)
//           // }
//         }
//       });
//       // console.log('userListBefore:', userIdList);
//       // console.log(trackNoDepartment)

//       await getDisplayNameFromUid(userIdList)
//         .then((res) => {
//           _.map(tempEmp, (empData, index) => {
//             _.set(empData, 'displayName', _.get(res[index], 'displayName'));
//             _.set(empData, 'qvAccount', _.get(res[index], 'qvAccount'));
//             _.set(empData, 'departments', _.get(res[index], 'departments'));
//             newEmpList.push(empData);
//           });
//         });
//     });
//   return newEmpList;
// };

// const getPremiseDetails = async (allowedPrems) => {
//   let premisesData;
//   const premiseRefs = allowedPrems.map((id) => {
//     return db.collection('premises').doc(id).get();
//   });
//   await Promise.all(premiseRefs).then((docs) => {
//     premisesData = docs.map((doc) => {
//       return doc.data();
//     });
//   }).catch((error) => console.log(error));
//   return premisesData;
// };
