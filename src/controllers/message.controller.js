const functions = require('firebase-functions');
const admin = require('firebase-admin');

const serverTimestamp = admin.firestore.FieldValue.serverTimestamp();
const db = admin.firestore();
const { FieldValue } = admin.firestore;
const _ = require('lodash');
const moment = require('moment-timezone');

moment.tz.setDefault('Asia/Kuala_Lumpur');

exports.postAnnouncement = async (data, context) => {
  try {
    const clone = _.cloneDeep(data);
    const time = moment.unix(data.createdAt).toDate();
    console.log(_.get(clone, 'premise', null));
    const docRef = db.collection('premises').doc(_.get(clone, 'premise', null)).collection('announcement').doc();
    console.log(docRef.id);

    _.set(clone, 'createdAt', time);
    _.set(clone, 'msgId', docRef.id);
    await docRef.set(clone)
      .then(() => console.log('done'))
      .catch((e) => console.log(e));
    return docRef.id;
  } catch (e) {
    throw new functions.https.HttpsError('postAnnouncement-invalid-argument', e.message);
  }
};

exports.deleteAnnouncement = async (data, context) => {
  try {
    const { premiseId, messageId } = data;
    let status = '';
    const docRef = db.collection('premises').doc(premiseId).collection('announcement').doc(messageId);
    await docRef.delete().then(() => {
      status = 'success';
    }).catch((e) => console.log(e));
    return status;
  } catch (e) {
    throw new functions.https.HttpsError('deleteAnnouncement-invalid-argument', e.message);
  }
};

exports.getAnnouncements = async (data, context) => {
  try {
    const allAnnouncements = [];
    const { premiseId } = data;
    await db.collection('premises').doc(premiseId).collection('announcement')
      .limit(30)
      .get()
      .then((querysnapshot) => {
        querysnapshot.forEach((doc) => {
          console.log(doc.data().createdAt);
          allAnnouncements.push(doc.data());
        });
      });
    const sortedAnnouncement = _.orderBy(
      allAnnouncements,
      [(obj) => moment.unix(_.get(obj, 'createdAt._seconds')).toDate()],

      ['desc'],
    );

    return sortedAnnouncement;
  } catch (e) {
    throw new functions.https.HttpsError('getAnnouncements-invalid-argument', e.message);
  }
};
