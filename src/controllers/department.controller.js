const { asyncJoiValidate } = require('#helpers/index');
const DepartmentService = require('#services/department.service');
const DepartmentDTO = require('#dtos/department.dto');

exports.createDepartmentByPremiseId = async (req, res, next) => {
  try {
    const data = {
      ...req.body,
      ...req.params,
    };
    const params = await asyncJoiValidate(data, DepartmentDTO.createDepartment);
    const response = await DepartmentService.createDepartmentByPremiseId(params, req.user);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.getDepartmentsByPremiseId = async (req, res, next) => {
  try {
    const data = {
      ...req.body,
      ...req.params,
    };
    const params = await asyncJoiValidate(data, DepartmentDTO.getDepartments);
    const response = await DepartmentService.getDepartmentsByPremiseId(params, req.user);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.getDepartmentByAPIKeyAndSerialNumber = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate(req.query, DepartmentDTO.getDepartmentByDeviceSerialNumber);
    const response = await DepartmentService.getDepartmentByAPIKeyAndSerialNumber(params);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.deleteDepartmentByPremiseId = async (req, res, next) => {
  try {
    const params = await asyncJoiValidate(req.params, DepartmentDTO.deleteDepartmentByPremiseId);
    const response = await DepartmentService.deleteDepartmentByPremiseId(params, req.user);
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};
