const { asyncJoiValidate, admin, serverTimestamp } = require('#helpers/index');
const { initParams } = require('request');
const { functions } = require('lodash');
const RequestDTO = require('#dtos/request.dtos');
const RequestService = require('#services/request.service');
const _ = require('lodash');
// Chze add
exports.getAllRequests = async (req, res, next) => {
  try {
    const data = {
      ...req.body,
      ...req.params,
      ...req.query,
    };
    const params = await asyncJoiValidate(data, RequestDTO.getAllRequests);

    const response = await RequestService.getAllRequests(params, _.get(req, 'user'));
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.getRequestById = async (req, res, next) => {
  try {
    const data = {
      ...req.body,
      ...req.params,
      ...req.query,
    };
    const params = await asyncJoiValidate(data, RequestDTO.getRequestById);

    const response = await RequestService.getRequestById(params, _.get(req, 'user'));
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.createRequest = async (req, res, next) => {
  try {
    const data = {
      ...req.body,
      ...req.params,
      ...req.query,
    };
    const params = await asyncJoiValidate(data, RequestDTO.createRequest);
    const response = await RequestService.createRequest(params, _.get(req, 'user'));
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.updateRequest = async (req, res, next) => {
  try {
    const data = {
      ...req.body,
      ...req.params,
      ...req.query,
    };
    const params = await asyncJoiValidate(data, RequestDTO.updateRequest);
    const response = await RequestService.updateRequest(params, _.get(req, 'user'));
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.updateRequestStatus = async (req, res, next) => {
  try {
    const data = {
      ...req.body,
      ...req.params,
      ...req.query,
    };
    const params = await asyncJoiValidate(data, RequestDTO.updateRequestStatus);
    const response = await RequestService.updateRequestStatus(params, _.get(req, 'user'));
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};

exports.deleteRequest = async (req, res, next) => {
  try {
    const data = {
      ...req.body,
      ...req.params,
      ...req.query,
    };
    const params = await asyncJoiValidate(data, RequestDTO.deleteRequest);
    const response = await RequestService.deleteRequest(params, _.get(req, 'user'));
    return res.success(response);
  } catch (err) {
    return next(err);
  }
};
