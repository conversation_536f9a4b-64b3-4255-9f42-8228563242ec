const functions = require('firebase-functions');
const admin = require('firebase-admin');
const serverTimestamp = admin.firestore.FieldValue.serverTimestamp();
const db = admin.firestore();
const FieldValue = admin.firestore.FieldValue;
const _ = require('lodash');
const rp = require('request-promise');
const moment = require('moment-timezone');
moment.tz.setDefault('Asia/Kuala_Lumpur');

const runtimeOpts = {
  timeoutSeconds:540,
  memory:'2GB'
}

// 🚩 THIS FILE IS AN OLD VERSION AND CURRENTLY UNUSED : New one at graph2.js
  exports.getGraphData = async (data, context)=>{
    let currentDate = moment(data.startD, 'MMMM DD, YYYY').toDate()
    let endD = moment(data.endD, 'MMMM DD, YYYY').toDate()
    let dateRange = []
    let deviceList = data.deviceList
    let cacheData = {}

    let querData = []
    let parsedRawData = {}
    let graphDatas = {}

    while( ! moment(currentDate).isSame(moment(endD).add(1,'day'), 'day') ) {
      dateRange.push(moment(currentDate).format('YYYY-MM-DD'))
      currentDate = moment(currentDate).add(1,'day')
    }

    await Promise.all(_.map(deviceList, async deviceId=>{
      await Promise.all(_.map(dateRange, async date=>{
        _.set(cacheData, `${deviceId}.${date}`, false)
        if(data.parseType!='day'){
          return db.collection('cache').doc(deviceId).collection('date').doc(date).get()
            .then(doc=>{
              if(doc.exists) {
                _.set(cacheData, `${deviceId}.${date}`, doc.data())
              } else {console.log(`${deviceId}-${date} not in cache`)}
            })
        } else { console.log('Skipping Cache') }
      }))
    }))
      .then(async()=>{ //get data not found in cache
        await Promise.all(_.map(cacheData, async (dates, deviceId)=>{
          return Promise.all(_.map(dates, async (data, date)=>{
            if(data==false){
              return db.collection('scanRecords')
                .where('DeviceId', '==', deviceId)
                .where('CreateTime', '>=', moment(date, 'YYYY-MM-DD').startOf('day').toDate())
                .where('CreateTime', '<=', moment(date, 'YYYY-MM-DD').endOf('day').toDate())
                .get().then(collection=>{
                  collection.forEach(doc=>{
                    querData.push(doc.data())
                  })
                })
            }
          }))
        }))
          .then(async()=>{
            console.log(`RAW DATA LENGTH: ${querData.length}`)
            if(querData.length){
              await parseUnCachedData(querData, cacheData).then(async res=>{
                // set to cache
                await pushToCache(res).then(response=>{
                  console.log(response)
                })
                _.map(res, info=>{
                  _.set(cacheData, `${info._deviceID}.${info._date}`, info)
                })

              })
            }
            graphDatas = await parseFullCachedData(cacheData, moment(data.startD, 'MMMM DD, YYYY').toDate(), endD, data.parseT)
          })
      })
      // .catch(e=>console.log(e))

    return graphDatas

  }

  const parseUnCachedData = async (raw, cacheInfo) =>{
    console.log('PARSING CACHE DATA')
    let identifiedRecord = []
    let unIdentifiedRecord = []
    let fullCacheQuery = []
    let countTest = 0

    _.forEach(raw, record => {
      if (record.UserID !='') {
        identifiedRecord.push(record)
      } else {
        unIdentifiedRecord.push(record)
      }
    })

    console.log(`Idenfied Rec:${identifiedRecord.length}`)
    await Promise.all(_.map(cacheInfo, async (dates, deviceId)=>{
      await Promise.all(_.map(dates, async (data, date)=>{
        if(!data) { //if there is no data in cache, parse data and add to batch
          let _13BelowCount = 0
          let _13to21Count = 0
          let _22to39Count = 0
          let _40to64Count = 0
          let _65AboveCount = 0
          let unidentifiedAge = 0
          let maleCnt=0, femaleCnt=0
          // let total=0
          let totalUnidentified = 0
          let totalIdenfied = 0

          let userInfoList = []
          let uniqueVisitors = []

          totalIdenfied = _.sumBy(identifiedRecord, record => {
            let recordDate = moment.unix(record.CreateTime._seconds).format()
            if(!_.includes(uniqueVisitors, record.CardNo) && moment(recordDate).isSame(date,'day') && record.DeviceId==deviceId ){
              uniqueVisitors.push(record.CardNo)
            }
            return moment(recordDate).isSame(date, 'day') && record.DeviceId==deviceId
          });

          totalUnidentified =_.sumBy(unIdentifiedRecord, record =>{
            let recordDate = moment.unix(record.CreateTime._seconds).format()
            return moment(recordDate).isSame(date, 'day') && record.DeviceId==deviceId
          })

          await Promise.all(_.map(identifiedRecord, async record => {
            let createDate = moment.unix(record.CreateTime._seconds).toDate()
            if (moment(createDate).isSame(date,'day') && record.DeviceId == deviceId)  {
              return db.collection('users').doc(record.UserID).get().then(doc => {
                userInfoList.push(doc.data().icNo)
              })
            }
          }))

          countTest +=userInfoList.length
          _.forEach(userInfoList, icNo=>{
            let age=calculateAge(icNo)
            if (age < 13) { _13BelowCount++ }
            else if (age >=13 && age < 22) { _13to21Count++ }
            else if (age >=22 && age < 40) { _22to39Count++ }
            else if (age >=40 && age < 65) { _40to64Count++ }
            else if (age >=65) { _65AboveCount++ }
            else {unidentifiedAge++}

            if(icNo){
              let gender= icNo.slice(-1) % 2
              if (gender ==0) {femaleCnt++}
              if (gender ==1) {maleCnt++}
            } //no unidenfied gender

          })
          //must be the same as database cache
          let barData = {
            _deviceID: deviceId,
            _date: date,
            _13Below: _13BelowCount,
            _13to21: _13to21Count,
            _22to39: _22to39Count,
            _40to64: _40to64Count,
            _65Above: _65AboveCount,
            unidentifiedAge: unidentifiedAge,
            male: maleCnt,
            female: femaleCnt,
            _totalUnidentified: totalUnidentified,
            _totalIdentified: totalIdenfied,
            _total: totalIdenfied + totalUnidentified,
            _uniqueList: uniqueVisitors
          }

          fullCacheQuery.push(barData)
        }

      }))
    }))

    console.log(`Check: ${countTest}`)
    return fullCacheQuery
  }

  const calculateAge = (userIC) => {
    if (userIC) {
      const userBday = userIC.slice(0,6)
      const thisDay = moment().diff(moment(userBday, "YYMMDD"), 'years')
      return thisDay
    }
  }

  const pushToCache = async(querData) => {
    console.log('SET TO CACHE')
    let today = new Date()
    const arraySetCache = querData

    await Promise.all(_.map(arraySetCache, async data=>{

      let dateSet = moment(data._date, 'YYYY-MM-DD').toDate()

      if(moment(dateSet).isSame(today,'day')){
        console.log('Skipped today')
      } else {
            // db.collection('cache').doc(deviceId).collection('date').doc(date)
        return db.collection('cache').doc(data._deviceID).collection('date').doc(data._date)
          .set({
            _deviceID: data._deviceID,
            _date: dateSet,
            _13Below: data._13Below,
            _13to21: data._13to21,
            _22to39: data._22to39,
            _40to64: data._40to64,
            _65Above: data._65Above,
            unidentifiedAge: data.unidentifiedAge,
            male: data.male,
            female: data.female,
            _totalUnidentified: data._totalUnidentified,
            _totalIdentified: data._totalIdentified,
            _total: data._total,
            _uniqueList: data._uniqueList,
            cacheTimestamp: admin.firestore.FieldValue.serverTimestamp()
          })
      }
    })).catch(e=>{
      return e
    })
    return 'finished set to cache'

  }

  exports.getRelatedDevices = async (data,context)=>{

    const uid = context.auth.uid;
    try {
      let tempArr=[]

      const admin = await db.doc(`admins/${uid}`).get().then(doc => doc.data());

      if(!_.includes(_.get(admin, 'allowedPremises'), data.premiseId)) {
        throw new Error("Invalid premise ID")
      }

      await db.collection('devices')
        .where('premiseId', '==', data.premiseId)
        .get()
        .then(collection=>{
          collection.forEach(doc=>{
            tempArr.push(doc.data())
          })
        })

      if(tempArr.length > 1){
        return {deviceList: tempArr, firstDevice: 'all'}
      } else {
        return {deviceList: tempArr, firstDevice: tempArr[0].id}
      }
    } catch (e) {
      throw new functions.https.HttpsError('invalid-argument', e.message);
    }
  }

  exports.getPremiseList = async (data, context)=>{

    const uid = context.auth.uid;
    try {
      let premises = [];

      const admin = await db.doc(`admins/${uid}`).get().then(doc => doc.data());

      if(!admin) {
        throw new Error("Invalid user.")
      }

      await Promise.all(_.map(admin.allowedPremises, async premiseId => {
        return db.doc(`premises/${premiseId}`).get().then(doc=>{
          const data = doc.data();
          data.id = doc.id;
          data.createdAt = data.createdAt.toDate().toDateString()
          premises.push(data)
        })
      }))

      return premises;
    } catch (e) {
      throw new functions.https.HttpsError('invalid-argument', e.message);
    }
  }


  const parseFullCachedData = async (fullCacheData, sDate, enDate, parseType) => {
    // const fullCacheData = data.fullCache
    // const sDate = moment(data.sDate).toDate()
    // const enDate = moment(data.enDate).toDate()
    // const parseType = data.parseType

    let entryGraph = [] //for entry chart
    let demograph = [] //for gender and age chart
    let countPeakGraph = [] //for peak day/hour chart
    let countUniq = [] //to count unique
    let performanceDevice = [] //device performance chart
    let ratioChart = [] //entry ratio chart
    let totalRange = 0
    let recurring = 0 //recurring visitors for different days

    entryGraph = ParseRawData(fullCacheData, sDate, enDate, parseType)
    demograph = parseDemographChart(fullCacheData,sDate, enDate, parseType)
    countPeakGraph = parseSumPeakHour(fullCacheData,sDate, enDate, parseType)
    countUniq = CountUnique(fullCacheData)
    ratioChart = parseToPie(fullCacheData)
    totalRange = countAll(fullCacheData)
    recurring = CountRecurring(fullCacheData)
    performanceDevice = await parseToDevicePerformance(fullCacheData)

    return {
      _entry: entryGraph,
      _demograph: demograph,
      _countPeakGraph:countPeakGraph,
      _countUniq: countUniq,
      _performanceDevice: performanceDevice,
      _ratioChart: ratioChart,
      _total: totalRange,
      _recurring: recurring
    }
  }
// })

  const countAll = (data) => {
    console.log('COUNT ALL FIRED')
    let count = 0
    _.map(data, (dates, deviceId)=>{
      _.map(dates, (data, date)=>{
        count += data._total
      })
    })
    return count
  }

  const ParseRawData = (data, startDate, endDate, parseType) => {
    console.log('ParseRawData FIRED')
    let currentDate = startDate
    let stopDate = moment(endDate).add(1, parseType)
    let parsedData = []
    while(! moment(currentDate).isSame(stopDate, parseType)){ //combine all devices data to a single day
      // let total=0
      let totalUnidentified = 0
      let totalIdenfied = 0
      // let userInfoList = []
      let uniqueVisitors = []

      _.map(data, (dates, deviceId)=>{
        _.map(dates, (data, date)=>{
          if (moment(date).isSame(currentDate, parseType)) {
            // let dateString = moment.unix(data._date.seconds).format('LL')
            uniqueVisitors = _.union(uniqueVisitors, data._uniqueList)
            totalUnidentified += data._totalUnidentified
            totalIdenfied +=data._totalIdentified
          }
        })
      })

      let dateFormat = 'LL'
      if(parseType=='hour') {dateFormat='h A, dddd D MMMM YYYY'}
      let dataBar = {
        date: moment(currentDate).format(dateFormat),
        dateLabel: FormatXaxis(parseType, currentDate),
        count: totalUnidentified + totalIdenfied,
        identified: totalIdenfied,
        unidentified: totalUnidentified,
        uniqueList: uniqueVisitors.length
      }

      parsedData.push(dataBar)
      currentDate = moment(currentDate).add(1, parseType).startOf(parseType)
    }

    return parsedData

  }

  const FormatXaxis = (parseType, date) => {
    switch (parseType) {
      case 'day':
        return moment(date).format('D MMM')

      case 'hour':
        return moment(date).format('HH:mm') //h A

      case 'week':
        return moment(date).format(`DMMM-${moment(date).endOf('week').format('D')}MMM`)

      case 'month':
        return moment(date).format('MMM-YYYY')

      default: return 'invalid'
    }

  }

  const CountUnique = (data) =>{
    console.log('CountUnique FIRED')
    let uniqueList = []
    _.map(data, (dates, deviceId)=>{
      _.map(dates, (data, date)=>{
        uniqueList = _.union(uniqueList, data._uniqueList)
      })
    })
    return uniqueList.length
  }

  const CountRecurring = (data) => {
    let recurrlist = []
    _.map(data, (dates, deviceId)=>{
      _.map(dates, (data, date)=>{
        recurrlist = _.concat(recurrlist, data._uniqueList)
      })
    })
    var groupped = _.groupBy(recurrlist, function (n) {return n});
    var result = _.uniq(_.flatten(_.filter(groupped, function (n) {return n.length > 1})));
    console.log(`Recurring:${result.length}`)
    return result.length
  }


  const parseToPie = (data) => {
    console.log('parseToPie FIRED')
    let countIden = 0
    let countUnIden = 0
    _.map(data, (dates, deviceId)=>{
      _.map(dates, (data, date)=>{
        countIden += data._totalIdentified
        countUnIden += data._totalUnidentified
      })
    })
    return [
      {name:'Identified', value: countIden},
      {name:'Unidentified', value: countUnIden}
    ]
  }

  const parseSumPeakHour =(data, startD, endDate, parseT) => {
    console.log('parseSumPeakHour fired')
    let parsedTotal = []
    let barData = {}
    let dFormat ='LL'
    let stopDate = moment(endDate).add(1, parseT)
    if (parseT=='hour') {
      dFormat='h A, dddd D MMMM YYYY'
    }
    let currentDate = startD
    while( ! moment(currentDate).isSame(stopDate, parseT)) {
      let countToday=0
      _.map(data, (dates, deviceId)=>{
        _.map(dates, (data, date)=>{
          if(moment(date, 'YYYY-MM-DD').isSame(currentDate, parseT)) {
            countToday+=data._total
          }
        })
      })
      barData = {
        date: moment(currentDate).format(dFormat),
        dateLabel: FormatXaxis(parseT, currentDate),
        total: countToday
      }
      parsedTotal.push(barData)
      currentDate = moment(currentDate).add(1, parseT).startOf(parseT) //increment loop
    }
    return parsedTotal
  }

  const parseToDevicePerformance = async (data) => {
    console.log('parseToDevicePerformance fired')
    let devicePieData = []
    await Promise.all(_.map(data, async (dates, deviceId)=>{
      let countDeviceEntry = 0

      _.map(dates, (data, date)=>{
        countDeviceEntry += data._total
      })
      return db.collection('devices').doc(deviceId).get().then(doc=>{
        devicePieData.push({name: doc.data().name, count:countDeviceEntry})
      })
    }))

    return devicePieData

  }

  const parseDemographChart = (data, startDate, endDate, parseType) => {
    console.log('parseDemographChart fired')
    let parsedBarInfo = []
    let barData = {}
    let currentDate = startDate
    let stopDate = moment(endDate).add(1, parseType)
    let labelFormat = 'LL'
    if(parseType=='hour') {
      labelFormat='h A, dddd D MMMM YYYY'
    }

    while(!moment(currentDate).isSame(stopDate, parseType)){
      let _13BelowCount = 0
      let _13to21Count = 0
      let _22to39Count = 0
      let _40to64Count = 0
      let _65AboveCount = 0
      let unidentifiedAge = 0
      let maleCnt=0, femaleCnt=0
      _.map(data, (dates, deviceId)=>{
        _.map(dates, (data, date)=>{
          if (moment(date).isSame(currentDate, parseType)) {
            // let dateString = moment.unix(data._date.seconds).format('LL')
            _13BelowCount += data._13Below
            _13to21Count += data._13to21
            _22to39Count += data._22to39
            _40to64Count += data._40to64
            _65AboveCount += data._65Above
            unidentifiedAge += data.unidentifiedAge
            maleCnt += data.male
            femaleCnt += data.female
          }
        })
      })

      barData = {
        timeLabel: FormatXaxis(parseType, currentDate),
        _13Below: _13BelowCount,
        _13to21: _13to21Count,
        _22to39: _22to39Count,
        _40to64: _40to64Count,
        _65Above: _65AboveCount,
        unidentified: unidentifiedAge,
        male: maleCnt,
        female: femaleCnt,
        time: moment(currentDate).format(labelFormat),
        // unidentifiedCount: unidentifiedRecord.length,
        // total: hourlyTotal
      }
      parsedBarInfo.push(barData)
      currentDate = moment(currentDate).add(1, parseType).startOf(parseType) //increment loop

    }

    return parsedBarInfo

  }
