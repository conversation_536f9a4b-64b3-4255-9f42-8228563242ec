const _ = require('lodash');
const rp = require('request-promise');
const responses = require('./responses');

const socketEmit = (socket, path, data) => {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error('timed out'));
    }, 10000);
    socket.emit(path, data, (result) => {
      clearTimeout(timeout);
      resolve(result);
    });
  });
};

exports.addFaceToFacePass = (user, socket) => {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error('timed out'));
    }, 10000);
    socket.emit('addFace', user, (result) => {
      clearTimeout(timeout);
      if (result === 0) {
        resolve();
      } else if (result === 1) {
        reject(new Error('No face detected.'));
      } else if (result === 2) {
        reject(new Error('Photo quality is too low.'));
      } else if (result === 3) {
        reject(new Error('Failed to bind face to device.'));
      }
    });
  });
};

exports.detectMask = async (base64) => {
  const res = await rp({
    url: 'https://mask-detection-302507.an.r.appspot.com/detect_mask',
    method: 'post',
    json: {
      image_base64: base64,
      token: 'UJjSnKvEdD9s3EnS',
    },
  });

  const probability = _.get(res, 'faces.0.mask_probability', 0) * 100;

  if (probability > 80) {
    console.log('probability', probability);
    throw new Error('Please remove your mask and upload a new photo.');
  } else if (_.size(_.get(res, 'faces', [])) === 0) {
    throw new Error('No face has been detected in photo.');
  }

  return {
    probability,
    faces: _.size(_.get(res, 'faces', [])),
  };
};

exports.detectFaceQuality = async (base64) => {
  const buffer = Buffer.from(base64, 'base64');

  const body = await rp({
    url: 'https://qv-face.cognitiveservices.azure.com/face/v1.0/detect',
    qs: {
      returnFaceId: false,
      recognitionModel: 'recognition_04',
      detectionModel: 'detection_03',
      returnFaceAttributes: 'headpose,mask,qualityforrecognition',
    },
    method: 'post',
    // json: true,
    body: buffer,
    headers: {
      'Content-Type': 'application/octet-stream',
      'Ocp-Apim-Subscription-Key': '330fe6fd32c847258a4dbbab4acaa03c',
    },
  }).catch((e) => {
    const error = _.get(e, 'error.error.message');
    const code = _.get(e, 'error.error.code');
    if (error) {
      throw { message: error, code };
    }
    throw e;
  });

  const faces = JSON.parse(body);

  if (faces.length === 0) {
    throw responses.failure('No face was detected in photo.', 400, { code: 'no_face' });
  } else if (faces.length > 1) {
    throw responses.failure('More than one face was detected in photo.', 400, { code: 'many_face' });
  }

  const face = faces[0];
  const attr = face.faceAttributes;
  const rect = face.faceRectangle;

  if (attr.qualityForRecognition !== 'high') {
    throw responses.failure('Face image quality low or are not facing camera properly.', 400, { code: 'low_quality' });
  } else if (attr.mask.noseAndMouthCovered) {
    throw responses.failure('Face mask are not allowed.', 400, { code: 'masked' });
  } else if (rect.width < 250 || rect.height < 350) {
    throw responses.failure('Face are too small.', 400, { code: 'face_small' });
  } else if (attr.headPose.roll > 15 || attr.headPose.roll < -15) {
    throw responses.failure('Face are tilted.', 400, { code: 'face_tilt' });
  }

  return face;
};

exports.addFaceToDevices = async (user, facePassSockets) => {
  await Promise.all(_.map(_.get(user, 'premiseEntry'), (entry, premiseId) => {
    return Promise.all(_.map(_.get(facePassSockets, premiseId), (socket, serialNumber) => {
      return this.addFaceToFacePass(user, socket);
    }));
  }));
};

exports.syncFaces = async (user, facePassSockets) => {
  await Promise.all(_.map(_.get(user, 'premiseEntry'), (entry, premiseId) => {
    return Promise.all(_.map(_.get(facePassSockets, premiseId), (socket, serialNumber) => {
      return socketEmit(socket, 'sync');
    }));
  }));
};

exports.openDoor = async (socket) => {
  return socketEmit(socket, 'door', 'open');
};
