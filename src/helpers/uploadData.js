const { Storage } = require('@google-cloud/storage');
const { readFileSync } = require('fs');
const { readFile } = require('fs');
const _ = require('lodash');
const multer = require('multer');
const path = require('path');

const bucketName = 'facial-recognition-3bf89.appspot.com';

const storage = new Storage({
  projectId: 'dahua-e2392',
  keyFilename: 'facial-recognition-3bf89-firebase-adminsdk-g3a42-791efa1632.json',
});

exports.uploadToGCS = async (req) => {
  try {
    console.log('req.file', req.file);
    const ext = path.extname(req.file.originalname);
    const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1E9)}`;
    // console.log('inside upload to gcs:', req.file);
    const bucket = await storage.bucket(bucketName);
    const file = await bucket.file(uniqueSuffix + ext);

    // const imageBuffer = await readFileSync(path.join(__dirname, '../../', req.file.path));
    // console.log('imageBuffer', imageBuffer);

    await file.save(req.file.buffer);

    const options = {
      action: 'read',
      expires: '03-09-9999',
    };

    const [url] = await file.getSignedUrl(options);
    console.log('url', url);
    return url;
  } catch (e) {
    console.log('uploadToGCS', e);
    return '';
  }
};
