const _ = require('lodash');
const fs = require('fs');
const rp = require('request-promise');

const auth = {
  user: 'admin',
  pass: 'ARX@dahua@SDEC',
  sendImmediately: false,
};

const parseData = (data) => {
  const split = data.split('\r\n');
  const output = {};
  _.forEach(split, (text) => {
    if (_.isEmpty(text)) {
      return;
    }
    const s = text.split('=');
    let value = s[1];
    if (value === 'true') {
      value = true;
    } else if (value === 'false') {
      value = false;
    }
    _.set(output, s[0], value);
  });
  return output;
};

class dahuaSocket {
  constructor(ip, socket) {
    this.ip = ip;
    this.baseUrl = `http://${this.ip}:8888/cgi-bin`;
    this.socket = socket;
  }

  socketRequest(options, type = 'rp') {
    return new Promise((resolve, reject) => {
      options.auth = auth;
      if (!this.socket) {
        return reject({ message: 'Socket not available.' });
      }
      const timeout = setTimeout(() => {
        reject({ message: 'Socket timed out.' });
      }, _.get(options, 'timeout', 30000));
      this.socket.emit(type, options, (data) => {
        clearTimeout(timeout);
        if (_.has(data, 'error')) {
          return reject({ message: data.message });
        }
        return resolve(data);
      });
    });
  }

  checkOnline() {
    return this.socketRequest({
      url: `${this.baseUrl}/magicBox.cgi`,
      method: 'GET',
      qs: {
        action: 'getSystemInfo',
      },
      timeout: 3000,
    }).then(parseData);
  }

  getSystemInformation() {
    return this.socketRequest({
      url: `${this.baseUrl}/magicBox.cgi`,
      method: 'GET',
      qs: {
        action: 'getSystemInfo',
      },
    }).then((data) => {
      const parsed = parseData(data);
      if (!_.has(parsed, 'serialNumber')) {
        throw new Error('Invalid device.');
      }
      return parsed;
    });
  }

  getNetworkInfo() {
    return this.socketRequest({
      url: `${this.baseUrl}/configManager.cgi`,
      method: 'GET',
      qs: {
        action: 'getConfig',
        name: 'Network',
      },
    }).then((data) => {
      return _.get(parseData(data), 'table.Network');
    });
  }

  setNetworkInfo(data) {
    console.log(data);
    return this.socketRequest({
      url: `${this.baseUrl}/configManager.cgi`,
      method: 'GET',
      qs: {
        action: 'setConfig',
        ...data,
      },
    });
  }

  getScanRecords(fromDate, toDate, count = -1) {
    return this.socketRequest({
      url: `${this.baseUrl}/recordFinder.cgi`,
      method: 'GET',
      qs: {
        action: 'find',
        name: 'AccessControlCardRec',
        count,
        StartTime: fromDate,
        EndTime: toDate,
      },
      timeout: 360000,
    }).then(parseData);
  }

  reboot() {
    return this.socketRequest({
      url: `${this.baseUrl}/magicBox.cgi`,
      method: 'GET',
      qs: {
        action: 'reboot',
      },
    });
  }

  countUsers() {
    return this.socketRequest({
      url: `${this.baseUrl}/recordFinder.cgi`,
      method: 'GET',
      qs: {
        action: 'getQuerySize',
        name: 'AccessControlCard',
      },
    }).then(parseData);
  }

  getUsers(count = -1) {
    return this.socketRequest({
      url: `${this.baseUrl}/recordFinder.cgi`,
      method: 'GET',
      qs: {
        action: 'find',
        name: 'AccessControlCard',
        count,
      },
    }).then(parseData);
  }

  findUser(uid) {
    return this.socketRequest({
      url: `${this.baseUrl}/recordFinder.cgi`,
      method: 'GET',
      qs: {
        action: 'find',
        name: 'AccessControlCard',
        'condition.UserID': uid,
      },
    }).then((str) => {
      const data = parseData(str);
      if (data.found === '0') {
        throw new Error('User not found in device.');
      }
      return data.records[0];
    });
  }

  addUser(uid, fullName) {
    return this.socketRequest({
      url: `${this.baseUrl}/recordUpdater.cgi`,
      method: 'GET',
      qs: {
        action: 'insert',
        name: 'AccessControlCard',
        CardNo: uid,
        CardStatus: '0',
        UserID: uid,
        CardName: fullName,
      },
    }).then(parseData);
  }

  removeUser(recno) {
    return this.socketRequest({
      url: `${this.baseUrl}/recordUpdater.cgi`,
      method: 'GET',
      qs: {
        action: 'remove',
        name: 'AccessControlCard',
        recno: _.toString(recno),
      },
    });
  }

  removeFace(uid) {
    return this.socketRequest({
      url: `${this.baseUrl}/FaceInfoManager.cgi`,
      method: 'GET',
      qs: {
        action: 'remove',
        UserID: uid,
      },
    });
  }

  async getFaces() {
    const startFind = await this.socketRequest({
      url: `${this.baseUrl}/FaceInfoManager.cgi`,
      method: 'GET',
      qs: {
        action: 'startFind',
      },
    }).then(JSON.parse);

    const doFind = await this.socketRequest({
      url: `${this.baseUrl}/FaceInfoManager.cgi`,
      method: 'GET',
      qs: {
        action: 'doFind',
        Token: startFind.Token,
        Count: 99999,
        Offset: 0,
      },
    }).then(JSON.parse);

    await this.socketRequest({
      url: `${this.baseUrl}/FaceInfoManager.cgi`,
      method: 'GET',
      qs: {
        action: 'stopFind',
        Token: startFind.Token,
      },
    });

    return doFind;
  }

  async countFaces() {
    const startFind = await this.socketRequest({
      url: `${this.baseUrl}/FaceInfoManager.cgi`,
      method: 'GET',
      qs: {
        action: 'startFind',
      },
    }).then(JSON.parse);

    const stopFind = await this.socketRequest({
      url: `${this.baseUrl}/FaceInfoManager.cgi`,
      method: 'GET',
      qs: {
        action: 'stopFind',
        Token: startFind.Token,
      },
    });

    return startFind;
  }

  updateFace(uid, base64) {
    return this.socketRequest({
      url: `${this.baseUrl}/FaceInfoManager.cgi?action=update`,
      method: 'POST',
      json: {
        UserID: uid,
        Info: {
          PhotoData: [base64],
        },
      },
    });
  }

  listenEvents(json) {
    return this.socketRequest({
      url: `${this.baseUrl}/snapManager.cgi?action=attachFileProc&Flags[0]=Event&Events=[All]&heartbeat=10`,
      forever: true,
      method: 'GET',
      json,
    }, 'stream');
  }

  mpegStream(json) {
    // res.setHeader('Content-Type', 'multipart/x-mixed-replace; boundary=myboundary');
    return this.socketRequest({
      url: `${this.baseUrl}/mjpg/video.cgi`,
      forever: true,
      json,
    }, 'mpeg');
  }

  openDoor() {
    return this.socketRequest({
      url: `${this.baseUrl}/accessControl.cgi`,
      method: 'GET',
      qs: {
        action: 'openDoor',
        channel: 1,
        Type: 'Remote',
      },
    });
  }
}

module.exports = dahuaSocket;
