const _ = require('lodash');
const fs = require('fs');
const rp = require('request-promise');

const auth = {
  user: 'admin',
  pass: 'admin1234',
  sendImmediately: false,
};

const api = rp.defaults({
  auth,
  timeout: 10000,
});

const parseData = (data) => {
  const split = data.split('\r\n');
  const output = {};
  _.forEach(split, (text) => {
    if (_.isEmpty(text)) {
      return;
    }
    const s = text.split('=');
    let value = s[1];
    if (value === 'true') {
      value = true;
    } else if (value === 'false') {
      value = false;
    }
    _.set(output, s[0], value);
  });
  return output;
};

class dahuaApi {
  constructor(ip) {
    this.ip = ip;
    this.baseUrl = `http://${this.ip}/cgi-bin`;
  }

  checkOnline() {
    return api({
      url: `${this.baseUrl}/magicBox.cgi`,
      method: 'GET',
      qs: {
        action: 'getSystemInfo',
      },
      timeout: 3000,
    }).then(() => true);
  }

  getSystemInformation() {
    return api({
      url: `${this.baseUrl}/magicBox.cgi`,
      method: 'GET',
      qs: {
        action: 'getSystemInfo',
      },
    }).then((data) => {
      const parsed = parseData(data);
      if (!_.has(parsed, 'serialNumber')) {
        throw new Error('Invalid device.');
      }
      return parsed;
    });
  }

  getScanRecords(count = -1) {
    return api({
      url: `${this.baseUrl}/recordFinder.cgi`,
      method: 'GET',
      qs: {
        action: 'find',
        name: 'AccessControlCardRec',
        count,
      },
    }).then(parseData);
  }

  reboot() {
    return api({
      url: `${this.baseUrl}/magicBox.cgi`,
      method: 'GET',
      qs: {
        action: 'reboot',
      },
    });
  }

  countUsers() {
    return api({
      url: `${this.baseUrl}/recordFinder.cgi`,
      method: 'GET',
      qs: {
        action: 'getQuerySize',
        name: 'AccessControlCard',
      },
    }).then(parseData);
  }

  getUsers(count = -1) {
    return api({
      url: `${this.baseUrl}/recordFinder.cgi`,
      method: 'GET',
      qs: {
        action: 'find',
        name: 'AccessControlCard',
        count,
      },
    }).then(parseData);
  }

  findUser(uid) {
    return api({
      url: `${this.baseUrl}/recordFinder.cgi`,
      method: 'GET',
      qs: {
        action: 'find',
        name: 'AccessControlCard',
        'condition.UserID': uid,
      },
    }).then((str) => {
      const data = parseData(str);
      if (data.found === '0') {
        throw new Error('User not found in device.');
      }
      return data.records[0];
    });
  }

  addUser(uid, fullName) {
    return api({
      url: `${this.baseUrl}/recordUpdater.cgi`,
      method: 'GET',
      qs: {
        action: 'insert',
        name: 'AccessControlCard',
        CardNo: uid,
        CardStatus: '0',
        UserID: uid,
        CardName: fullName,
      },
    }).then(parseData);
  }

  removeUser(recno) {
    return api({
      url: `${this.baseUrl}/recordUpdater.cgi`,
      method: 'GET',
      qs: {
        action: 'remove',
        name: 'AccessControlCard',
        recno: _.toString(recno),
      },
    });
  }

  updateFace(uid, base64) {
    return api({
      url: `${this.baseUrl}/FaceInfoManager.cgi?action=update`,
      method: 'POST',
      json: {
        UserID: uid,
        Info: {
          PhotoData: [base64],
        },
      },
    });
  }
}

module.exports = dahuaApi;
