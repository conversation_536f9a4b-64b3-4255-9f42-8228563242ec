import { auth } from "../configs/firebase";

const axios = require("axios");

const fetch = async (path, method = "get", body, options = {}) => {
  if (method === "get") {
    options.params = new URLSearchParams(body);
  }

  if (method === "post" || method === "put") {
    options.data = body;
  }

  const token = await auth.currentUser.getIdToken(true);

  return axios({
    url: process.env["NEXT_PUBLIC_API_BASE_URL"] + path,
    method,
    headers: {
      "Content-Type": "application/json",
      Authorization: token,
    },
    ...options,
  }).catch((err) => {
    console.error("axios error:", err);
    throw { data: err.response.data, status: err.status };
  });
};

export { fetch };
