const responses = require('#helpers/responses');
const admin = require('firebase-admin');

const _ = require('lodash');

/**
 * Asynchronously Validates parameters according to the Jo<PERSON> Schema parameter.
 * @param {object} data Data that is to be validated.
 * @param {object} joiObject Joi Schema to validate against
 * @returns {object} The response object.
 */
const asyncJoiValidate = async (data, joiObject) => {
  try {
    const validatedData = await joiObject.validateAsync(data, {
      abortEarly: false,
      allowUnknown: false,
      convert: true,
      stripUnknown: true,
    });
    return validatedData;
  } catch (error) {
    console.log('error from joi:', error);
    const errors = {};
    const messages = [];
    _.map(error.details, (item) => {
      errors[`${item.context.label}`] = item.message;
      messages.push(item.message);
    });

    throw responses.failure(messages.join(', '), errors, 400);
  }
};

const serverTimestamp = admin.firestore.FieldValue.serverTimestamp();
// const bucket = admin.storage().bucket('gs://dahua-e2392.appspot.com');
// const db = admin.firestore();
const { v4: uuidv4 } = require('uuid');
const moment = require('moment-timezone');
const Joi = require('joi');

moment.tz.setDefault('Asia/Kuching');
const sharp = require('sharp');
const { storage } = require('#config/firebase');
const AdminModel = require('#models/Admin.model');

const uploadImage = async (base64, path = '') => {
  const upload = storage.file(`${path}/${uuidv4()}.jpg`, {
    metadata: { contentType: 'image/jpeg' },
    validation: 'md5',
  });

  const imageBuffer = Buffer.from(base64);
  await upload.save(imageBuffer);

  const photoUrl = await upload
    .getSignedUrl({
      action: 'read',
      expires: '03-09-9999',
    })
    .then((signedUrls) => signedUrls[0]);

  if (!photoUrl) {
    throw responses.failure('Photo cannot be uploaded.');
  }

  return photoUrl;
};

const collectionToArray = (collection) => {
  const output = [];
  collection.forEach((doc) => {
    const data = doc.data();
    data.id = doc.id;
    if (data.createdAt) {
      try {
        data.createdAt = data.createdAt.toDate();
      } catch (e) {}
    } else {
      data.createdAt = moment().toDate();
    }
    output.push(data);
  });
  return output;
};

const joiValidateJson = (data, schemaJson) => {
  const schema = Joi.object(schemaJson);
  const validate = schema.validate(data, {
    abortEarly: false,
    allowUnknown: false,
    convert: true,
    stripUnknown: true,
  });
  if (validate.error) {
    const errorArray = _.map(_.get(validate, 'error.details'), (detail) => {
      const { label, key } = detail.context;
      let { message } = detail;
      if (label === key) {
        message = message.replace(key, _.startCase(label));
      }
      return message;
    });
    throw responses.failure(errorArray.join(', '));
  }
  return validate.value;
};

const iterate = (data, cb, excludes) => {
  _.forEach(data, (value, key) => {
    if (_.includes(excludes, key)) {
      _.unset(data, key);
    } else if (_.isObject(value) && !_.has(value, '_seconds')) {
      iterate(value, cb);
    } else {
      cb(data, key, value);
    }
  });
};

const parseFirestoreDoc = (doc, excludes = []) => {
  const data = doc.data();
  // const obj = {}
  iterate(
    data,
    (item, key) => {
      let value = item[key];
      try {
        if (typeof value === 'object') {
          value = value.toDate();
        }
      } catch (e) {}
      _.set(item, key, value);
    },
    excludes,
  );
  /* _.forEach(data, (item, key) => {
    try {
      if(typeof item === 'object') {
        item = item.toDate();
      }
    } catch (e) {}
    if(!_.includes(excludes, key)) {
      _.set(obj, key, item)
    }
  }) */
  return data;
};

const getOneFromCollection = async (collection, excludes = []) => {
  if (collection.size === 0) {
    return null;
  }
  return parseFirestoreDoc(collection.docs[0], excludes);
};

const compressPhotoBase64 = async (photoBase64) => {
  const imageBuffer = Buffer.from(photoBase64, 'base64');
  const resizedBuffer = await sharp(imageBuffer)
    .resize(600, 1000, {
      fit: 'cover',
    })
    .jpeg()
    .toBuffer();
  return resizedBuffer.toString('base64');
};

const getAdminByUid = (uid) => {
  return AdminModel.findOne({ uid }).lean();
};

module.exports = {
  admin,
  serverTimestamp,
  // db,
  // bucket,
  uploadImage,
  collectionToArray,
  joiValidateJson,
  parseFirestoreDoc,
  getOneFromCollection,
  compressPhotoBase64,
  asyncJoiValidate,
  getAdminByUid,
};
