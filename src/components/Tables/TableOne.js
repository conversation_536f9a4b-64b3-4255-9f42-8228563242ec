import { BRAND } from "@/types/brand";
import Image from "next/image";

const brandData = [
  {
    logo: "/images/brand/brand-01.svg",
    name: "Google",
    visitors: 3.5,
    revenues: "5,768",
    sales: 590,
    conversion: 4.8,
  },
  {
    logo: "/images/brand/brand-02.svg",
    name: "Twitter",
    visitors: 2.2,
    revenues: "4,635",
    sales: 467,
    conversion: 4.3,
  },
  {
    logo: "/images/brand/brand-03.svg",
    name: "<PERSON><PERSON><PERSON>",
    visitors: 2.1,
    revenues: "4,290",
    sales: 420,
    conversion: 3.7,
  },
  {
    logo: "/images/brand/brand-04.svg",
    name: "Vimeo",
    visitors: 1.5,
    revenues: "3,580",
    sales: 389,
    conversion: 2.5,
  },
  {
    logo: "/images/brand/brand-05.svg",
    name: "Facebook",
    visitors: 3.5,
    revenues: "6,768",
    sales: 390,
    conversion: 4.2,
  },
];

const TableOne = ({ salesmanLeaderboard }) => {
  return (
    <div className="col-span-12 rounded-sm border border-stroke bg-white px-5 pb-5 pt-7.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:col-span-12">
      <h4 className="mb-6 text-xl font-semibold text-black dark:text-white">
        Sales Leaderboards
      </h4>

      <div className="flex flex-col">
        <div className="grid grid-cols-2 rounded-sm bg-gray-2 dark:bg-meta-4 sm:grid-cols-2">
          <div className="p-2.5 xl:p-5">
            <h5 className="text-sm font-medium uppercase xsm:text-base">
              Salesman
            </h5>
          </div>
          <div className="p-2.5 text-center xl:p-5">
            <h5 className="text-sm font-medium uppercase xsm:text-base">
              Total Sales
            </h5>
          </div>
          {/*<div className="p-2.5 text-center xl:p-5">*/}
          {/*  <h5 className="text-sm font-medium uppercase xsm:text-base">*/}
          {/*    Revenues*/}
          {/*  </h5>*/}
          {/*</div>*/}
          {/*<div className="hidden p-2.5 text-center sm:block xl:p-5">*/}
          {/*  <h5 className="text-sm font-medium uppercase xsm:text-base">*/}
          {/*    Sales*/}
          {/*  </h5>*/}
          {/*</div>*/}
          {/*<div className="hidden p-2.5 text-center sm:block xl:p-5">*/}
          {/*  <h5 className="text-sm font-medium uppercase xsm:text-base">*/}
          {/*    Conversion*/}
          {/*  </h5>*/}
          {/*</div>*/}
        </div>

        {salesmanLeaderboard &&
          salesmanLeaderboard.map((leaderboard, key) => (
            <div
              className={`grid grid-cols-2 sm:grid-cols-2 ${
                key === brandData.length - 1
                  ? ""
                  : "border-b border-stroke dark:border-strokedark"
              }`}
              key={key}
            >
              <div className="flex items-center gap-3 p-2.5 xl:p-5">
                {/*<div className="flex-shrink-0">*/}
                {/*  <Image src={brand.logo} alt="Brand" width={48} height={48} />*/}
                {/*</div>*/}
                <p className="hidden text-black dark:text-white sm:block">
                  {leaderboard?.salesman?.name}
                </p>
              </div>

              <div className="flex items-center justify-center p-2.5 xl:p-5">
                <p className="text-black dark:text-white">
                  RM {parseFloat(leaderboard?.totalSales).toFixed(2)}
                </p>
              </div>

              {/*<div className="flex items-center justify-center p-2.5 xl:p-5">*/}
              {/*  <p className="text-meta-3">${leaderboard?.salesman?.name}</p>*/}
              {/*</div>*/}

              {/*<div className="hidden items-center justify-center p-2.5 sm:flex xl:p-5">*/}
              {/*  <p className="text-black dark:text-white">*/}
              {/*    {leaderboard?.salesman?.name}*/}
              {/*  </p>*/}
              {/*</div>*/}

              {/*<div className="hidden items-center justify-center p-2.5 sm:flex xl:p-5">*/}
              {/*  <p className="text-meta-5">{leaderboard?.salesman?.name}%</p>*/}
              {/*</div>*/}
            </div>
          ))}
      </div>
    </div>
  );
};

export default TableOne;
