import _ from "lodash";
import { clsx } from "clsx";
import DatePicker from "react-tailwindcss-datepicker";
import moment from "moment";
import FormError from "../common/FormError";

const Input = ({ inputField, returnedValue, value, disabled = false, error = null, placeholder }) => {
  const { name, type, options, className } = inputField;

  const handleChange = (e) => {
    returnedValue(e.target.value);
  };

  const renderInput = () => {
    switch (type) {
      case "text":
        return (
          <input
            type="text"
            className={clsx(
              "w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary",
              error && "border-red-500",
              className
            )}
            placeholder={placeholder}
            value={value || ""}
            onChange={handleChange}
            disabled={disabled}
          />
        );
      case "select":
        return (
          <select
            className={clsx(
              "w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary",
              error && "border-red-500",
              className
            )}
            value={value || ""}
            onChange={handleChange}
            disabled={disabled}
          >
            {options.map((option, index) => (
              <option key={index} value={option.value}>
                {option.name}
              </option>
            ))}
          </select>
        );
      case "date":
        return (
          <DatePicker
            disabled={disabled}
            asSingle={true}
            popoverDirection={"down"}
            value={{
              startDate: value,
              endDate: value,
            }}
            onChange={(value) => {
              returnedValue(moment(value.startDate).toISOString());
            }}
            inputClassName={clsx(
              "w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary",
              error && "border-red-500",
              className
            )}
          />
        );
      case "price":
        return (
          <label className="input input-bordered flex items-center gap-2">
            RM
            <input
              disabled={disabled}
              value={parseFloat(value) || ""}
              onChange={(e) => {
                e.preventDefault();
                if (_.isEmpty(e.target.value)) {
                  returnedValue("0");
                } else {
                  returnedValue(e.target.value);
                }
              }}
              type="text"
              placeholder="0.00"
            />
          </label>
        );
      case "number":
        return (
          <input
            type="text"
            className={clsx(
              "w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary",
              error && "border-red-500",
              className
            )}
            value={value || ""}
            onChange={(e) => {
              e.preventDefault();
              const isNumber = /^[0-9]*$/.test(e.target.value);
              console.log("is number", isNumber);
              if (isNumber) {
                returnedValue(e.target.value);
              } else {
                returnedValue(value);
              }
            }}
            disabled={disabled}
            placeholder={placeholder}
          />
        );
      case "email":
        return (
          <input
            type="email"
            className={clsx(
              "w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary",
              error && "border-red-500",
              className
            )}
            value={value || ""}
            onChange={(e) => {
              returnedValue(e.target.value);
            }}
            disabled={disabled}
            placeholder={placeholder}
          />
        );
      case "textarea":
        return (
          <textarea
            className={clsx(
              "w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary",
              error && "border-red-500",
              className
            )}
            value={value || ""}
            onChange={handleChange}
            disabled={disabled}
          />
        );


      case 'checkbox':
        return (
          <>
            <input
              type={type || "text"}
              className={clsx(
                "ml-4 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary",
                error && "border-red-500",
                className
              )}
              checked={value}
              onChange={(e) => {
                returnedValue(e.target.checked);
              }}
              disabled={disabled}
            />
          </>
        );

      default:
        return (
          <input
            type={type || "text"}
            className={clsx(
              "w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary",
              error && "border-red-500",
              className
            )}
            value={value || ""}
            onChange={handleChange}
            disabled={disabled}
            placeholder={placeholder}
          />
        );
    }
  };

  return (
    <div className="mb-4 w-full">
      <label className="mb-2.5 text-black dark:text-white">
        {name}
      </label>
      {renderInput()}
      <FormError error={error} fieldName={name.toLowerCase()} />
    </div>
  );
};

export default Input;
