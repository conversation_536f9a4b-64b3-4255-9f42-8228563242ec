"use client";

import React, { useContext } from "react";
import { usePathname } from "next/navigation";
import SidebarItem from "@/components/Sidebar/SidebarItem";
import ClickOutside from "@/components/ClickOutside";
import useLocalStorage from "@/hooks/useLocalStorage";
import UserContext from "@/Context/UserContext";
import { FaTachometerAlt, FaUsers, FaClipboardList, FaFileContract, FaTag } from "react-icons/fa"; // Importing icons from react-icons
import _ from 'lodash'

interface SidebarProps {
	sidebarOpen: boolean;
	setSidebarOpen: (arg: boolean) => void;
}

const menuGroups = [
	{
		name: "Sales",
		menuItems: [
			{
				icon: <FaTachometerAlt />, // Dashboard icon
				label: "Dashboard",
				route: "#",
				children: [{ label: "Home", route: "/" }],
			},
			{
				icon: <FaUsers />, // Sales icon
				label: "Sales",
				route: "/sales",
			},
			{
				icon: <FaClipboardList />, // Staff KPI icon
				label: "Staff KPI",
				route: "/staff-kpi-list",
			},
			{
				icon: <FaFileContract />, // Terms and Conditions icon
				label: "Terms and Conditions",
				route: "/terms-and-conditions",
			},
			{
				icon: <FaTag />, // Voucher icon
				label: "Vouchers",
				route: "/vouchers",
			},
			{
				icon: <FaClipboardList />, // Reviews icon
				label: "Reviews",
				route: "/reviews",
			},
		],
	},
	{
		name: "Installer",
		menuItems: [
			{
				icon: <FaUsers />,
				label: "Installer",
				route: "/installer",
			},
		],
	},
	{
		name: "Access",
		menuItems: [
			{
				icon: <FaUsers />, // Staff Access icon
				label: "Staff Access",
				route: "/access",
			},
		],
	},
	{
		name: "Settings",
		menuItems: [
			{
				icon: <FaClipboardList />, // Packages icon
				label: "Packages",
				route: "/packages",
			},
		],
	},
];

const salesmanMenuGroups = [
	{
		name: "Sales",
		menuItems: [
			{
				icon: <FaTachometerAlt />, // Dashboard icon
				label: "Dashboard",
				route: "#",
				children: [{ label: "Home", route: "/" }],
			},
			{
				icon: <FaUsers />, // Sales icon
				label: "Sales",
				route: "/sales",
			},
			{
				icon: <FaClipboardList />, // Staff KPI icon
				label: "Staff KPI",
				route: "/staff-kpi-list",
			},
			{
				icon: <FaTag />, // Voucher icon
				label: "Vouchers",
				route: "/vouchers",
			},
		],
	},
];

const installationCheckerGroups = [
	{
		name: "Installation Checker",
		menuItems: [
			{
				icon: <FaTachometerAlt />, // Dashboard icon
				label: "Dashboard",
				route: "/",
			},
			{
				icon: <FaClipboardList />, // All Orders icon
				label: "All Orders",
				route: "/installer-checker/order-list",
			},
			{
				icon: <FaClipboardList />, // Checked Orders icon
				label: "Checked Orders",
				route: "/installer-checker/checked-orders",
			},
		],
	},
];

const installerMenuGroups = [
	{
		name: "General",
		menuItems: [
			{
				icon: <FaTachometerAlt />, // Dashboard icon
				label: "Dashboard",
				route: "/",
			},
			{
				icon: <FaClipboardList />, // Order List icon
				label: "Order List",
				route: "/installer/order-list",
			},
			{
				icon: <FaClipboardList />, // My Claimed Order icon
				label: "My Claimed Order",
				route: "/installer/claimed-order",
			},
		],
	},
];

const Sidebar = ({ sidebarOpen, setSidebarOpen }: SidebarProps) => {
	const pathname = usePathname();
	const [pageName, setPageName] = useLocalStorage("selectedMenu", "dashboard");
	const context = useContext(UserContext);

	const getMenuGroups = () => {
		const roles = context?.user?.roles;

		if (_.includes(roles, "admin") || _.includes(roles, "platform-admin")) {
			return menuGroups;
		}

		if (_.includes(roles, "installer")) {
			return installerMenuGroups;
		}

		if (_.includes(roles, "salesman")) {
			return salesmanMenuGroups;
		}

		if (_.includes(roles, "installation-checker")) {
			return installationCheckerGroups;
		}

		return [];
	};

	return (
		<ClickOutside onClick={() => setSidebarOpen(false)}>
			<aside
				className={`fixed left-0 top-0 z-9999 flex h-screen w-72.5 flex-col overflow-y-hidden bg-black duration-300 ease-linear dark:bg-boxdark xl:translate-x-0 ${sidebarOpen ? "translate-x-0" : "-translate-x-full"
					}`}
			>
				{/* <!-- SIDEBAR HEADER --> */}
				<div className="flex items-center justify-between gap-2 px-6 py-5.5 xl:py-6.5">
					<img
						className={"aspect-auto w-50"}
						alt={"vb-motor-image"}
						src={"/images/vb-motor-logo.png"}
					/>
					<button
						onClick={() => setSidebarOpen(!sidebarOpen)}
						aria-controls="sidebar"
						className="block xl:hidden"
					>
						<svg
							className="fill-current"
							width="20"
							height="18"
							viewBox="0 0 20 18"
							fill="none"
							xmlns="http://www.w3.org/2000/svg"
						>
							<path
								d="M19 8.175H2.98748L9.36248 1.6875C9.69998 1.35 9.69998 0.825 9.36248 0.4875C9.02498 0.15 8.49998 0.15 8.16248 0.4875L0.399976 8.3625C0.0624756 8.7 0.0624756 9.225 0.399976 9.5625L8.16248 17.4375C8.31248 17.5875 8.53748 17.7 8.76248 17.7C8.98748 17.7 9.17498 17.625 9.36248 17.475C9.69998 17.1375 9.69998 16.6125 9.36248 16.275L3.02498 9.8625H19C19.45 9.8625 19.825 9.4875 19.825 9.0375C19.825 8.55 19.45 8.175 19 8.175Z"
								fill=""
							/>
						</svg>
					</button>
				</div>
				{/* <!-- SIDEBAR HEADER --> */}

				<div className="no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear">
					{/* <!-- Sidebar Menu --> */}
					<nav className="mt-5 px-4 py-4 xl:mt-9 xl:px-6">
						{getMenuGroups().map((group, groupIndex) => (
							<div key={groupIndex}>
								<h3 className="mb-4 ml-4 text-sm font-semibold text-bodydark2">
									{group.name}
								</h3>

								<ul className="mb-6 flex flex-col gap-1.5">
									{group.menuItems.map((menuItem, menuIndex) => (
										<SidebarItem
											key={menuIndex}
											item={menuItem}
											pageName={pageName}
											setPageName={setPageName}
										/>
									))}
								</ul>
							</div>
						))}
					</nav>
					{/* <!-- Sidebar Menu --> */}
				</div>
			</aside>
		</ClickOutside>
	);
};

export default Sidebar;
