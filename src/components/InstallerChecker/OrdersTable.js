import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { fetch } from "../../helpers/index";
import { IoEyeOutline } from "react-icons/io5";
import { FaCheckCircle } from "react-icons/fa";
import _ from "lodash";
import moment from "moment";
import { clsx } from "clsx";
import FormError from "../common/FormError";

const OrdersTable = ({ orders, getOrders, ordersLength, compact = false, isCheckedList = false, error = null }) => {
	const [page, setPage] = useState(0);
	const router = useRouter();

	useEffect(() => {
		if (getOrders) {
			getOrders(5, page);
		}
	}, [page]);

	const markAsChecked = async (orderId) => {
		try {
			const res = await fetch(`/orders/${orderId}/check`, "put");
			console.log("Order marked as checked:", res);
			if (getOrders) getOrders(5, page);
		} catch (err) {
			console.log("Error marking order as checked:", err);
			if (err.response?.data?.message) {
				// Handle error display here if needed
			}
		}
	};

	return (
		<div className="overflow-x-scroll">
			<table className={"table mt-4 bg-white"}>
				{/* head */}
				<thead>
					<tr>
						{!compact && (
							<th>
								<label>
									<input type="checkbox" className="checkbox" />
								</label>
							</th>
						)}
						<th>Order ID</th>
						<th>Car Plate No</th>
						<th>Service Type</th>
						{!compact && <th>Customer</th>}
						{!compact && <th>Created At</th>}
						<th>Status</th>
						<th>Actions</th>
					</tr>
				</thead>
				<tbody>
					{_.map(orders, (order) => (
						<tr key={order._id}>
							{!compact && (
								<th>
									<label>
										<input type="checkbox" className="checkbox" />
									</label>
								</th>
							)}
							<td>
								<div className="font-bold">#{order._id.slice(-6)}</div>
							</td>
							<td>{order.carPlateNo}</td>
							<td className={"capitalize"}>{order.serviceType}</td>
							{!compact && (
								<td>
									<div className="flex items-center gap-3">
										<div>
											<div className="font-bold capitalize">
												{order.customer?.name}
											</div>
											<div className="text-sm opacity-50">{order.customer?.phone}</div>
										</div>
									</div>
								</td>
							)}
							{!compact && <td>{moment(order.createdAt).format("lll")}</td>}
							<td className={"capitalize"}>
								{isCheckedList ? (
									<span className="badge badge-success gap-1">
										<FaCheckCircle /> Checked
									</span>
								) : (
									order.status
								)}
							</td>
							<td className={clsx("capitalize", "flex", "justify-between")}>
								<button
									onClick={() => {
										router.push(`/sales/${order._id}`);
									}}
									className="btn btn-ghost p-0"
								>
									<IoEyeOutline style={{ fontSize: 20 }} />
								</button>
								{!isCheckedList && (
									<button
										onClick={() => markAsChecked(order._id)}
										className="text- btn btn-ghost p-0 text-green-500"
									>
										<div>Mark As Checked</div>
									</button>
								)}
							</td>
						</tr>
					))}
				</tbody>
			</table>

			{!compact && ordersLength > 0 && (
				<div className="join mt-2 flex justify-end">
					{_.times(Math.ceil(ordersLength / 5), (index) => (
						<button
							key={index}
							onClick={() => {
								setPage(index);
							}}
							className={`btn join-item btn-sm bg-white ${page === index && "btn-active"}`}
						>
							{index + 1}
						</button>
					))}
				</div>
			)}

			{error && (
				<div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
					<div className="flex items-center text-red-600">
						<span className="mr-2">•</span>
						<span>{Object.values(error)[0]}</span>
					</div>
				</div>
			)}
		</div>
	);
};

export default OrdersTable; 