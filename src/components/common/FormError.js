import React from 'react';
import { clsx } from "clsx";

/**
 * FormError component to display error messages for form fields
 * @param {Object} props - Component props
 * @param {string|Object} props.error - Error message or error object
 * @param {string} props.fieldName - Name of the field with error
 * @returns {JSX.Element|null} - Error message component or null if no error
 */
const FormError = ({ error, fieldName }) => {
	// If there's no error, return null
	if (!error) {
		return null;
	}

	// Handle different error formats
	let errorMessage = error;

	// If error is an object but not a string (to handle nested error objects)
	if (typeof error === 'object' && error !== null && !(error instanceof String)) {
		// Try to extract a message from common error object formats
		errorMessage = error.message ||
			error.error ||
			(error[fieldName] ? error[fieldName] : JSON.stringify(error));
	}

	return (
		<div className={clsx(
			"text-sm text-red-500 mt-1 font-medium"
		)}>
			{fieldName} is required
		</div>
	);
};

export default FormError;