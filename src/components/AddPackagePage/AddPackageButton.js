import { useRouter } from "next/navigation";
import { clsx } from "clsx";

const AddPackageButton = ({ onClick }) => {
  const router = useRouter();

  return (
    <div className={clsx("flex", "justify-end", "mt-4")}>
      <button
        onClick={onClick}
        className={clsx(
          "bg-primary",
          "text-white",
          "rounded-md",
          "px-6",
          "py-2",
          "mt-1",
          "w-40",
        )}
      >
        Add
      </button>
    </div>
  );
};

export default AddPackageButton;
