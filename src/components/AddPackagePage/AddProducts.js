import { useEffect, useState } from "react";
import { clsx } from "clsx";
import Input from "../Input/Input";
import { FiDelete } from "react-icons/fi";
import { PiTrashLight } from "react-icons/pi";

const productTypes = [
  "Front Wind Screen",
  "Rear Wind Screen",
  "First Sun Roof",
  "Second Sun Roof",
  "L1 Side Mirror",
  "L2 Side Mirror",
  "L3 Side Mirror",
  "R1 Side Mirror",
  "R2 Side Mirror",
  "R3 Side Mirror",
  "Body Coating",
  "Headlamp Coating",
  "Engine Coating",
  "Chrome Coating",
  "Rim and Caliper Coating",
  "Wind Screen Coating",
  "Interior Coating",
  "Exterior Coating",
  "Full Glass Coating",
  "Front Headlamp PPF",
  "Four Side Door Steps PPF",
  "3 Times Car Wash",
  "Car Plate",
];

const AddProducts = ({ returnedValue }) => {
  const [products, setProducts] = useState([
    {
      types: [],
    },
  ]);

  useEffect(() => {
    console.log("updated product list:", products);
    returnedValue(products);
  }, [products]);

  return (
    <div className={clsx("m-2")}>
      <div className="label">
        <span className="label-text">Products:</span>
      </div>
      {_.map(products, (product, productIndex) => (
        <Product
          returnedProduct={(product, pIndex) => {
            const cloned = _.cloneDeep(products);
            cloned.splice(pIndex, 1, product);
            setProducts([...cloned]);
          }}
          key={productIndex}
          onDelete={(pIndex) => {
            const cloned = _.cloneDeep(products);
            console.log("p index:", pIndex);
            _.pullAt(cloned, pIndex);
            console.log("cloned deleted product:", cloned);

            setProducts([...cloned]);
          }}
          product={product}
          setProducts={setProducts}
          productIndex={productIndex}
        />
      ))}
      <button
        onClick={() => {
          setProducts((prev) => [
            ...prev,
            {
              types: [],
            },
          ]);
        }}
        className={clsx("btn", "mt-4", "btn-outline", "w-full", "btn-sm")}
      >
        +
      </button>
    </div>
  );
};

const Product = ({ product, returnedProduct, productIndex, onDelete }) => {
  // useEffect(() => {
  //   console.log("monitor product:", product);
  //   returnedProduct(product, productIndex);
  // }, [product]);

  return (
    <div className={clsx("w-full", productIndex === 0 ? "mt-0" : "mt-10")}>
      <div className={"relative"}>
        <button
          onClick={() => {
            onDelete(productIndex);
          }}
        >
          <PiTrashLight
            className={clsx("absolute", "right-0")}
            color={"red"}
            size={26}
          />
        </button>
        <Input
          key={productIndex}
          returnedValue={(value) => {
            returnedProduct(
              {
                ...product,
                name: value,
              },
              productIndex,
            );
          }}
          value={""}
          inputField={{
            name: "Product Name",
            value: product?.name,
          }}
        />
      </div>
      <div
        className={clsx(
          "grid grid-cols-1",
          "gap-4",
          "xsm:grid-cols-2",
          "sm:grid-cols-3",
          "mt-6",
        )}
      >
        <Checkbox
          returnedValue={(isChecked) => {
            const cloned = _.cloneDeep(product);
            if (isChecked) {
              const t = [];
              _.map(productTypes, (type) => {
                t.push(type);
              });
              returnedProduct(
                {
                  ...cloned,
                  types: t,
                },
                productIndex,
              );
            } else {
              returnedProduct({
                ...cloned,
                types: [],
              });
            }
          }}
          text={"Select All"}
        />
        {_.map(productTypes, (productType) => (
          <Checkbox
            value={_.includes(product?.types, productType)}
            returnedValue={(isChecked, value) => {
              const cloned = _.cloneDeep(product);
              const isFieldExisted = _.includes(product?.types, value);
              if (isFieldExisted) {
                _.remove(cloned?.types, (type) => type === value);
              } else {
                cloned?.types.push(value);
              }

              returnedProduct(
                {
                  ...cloned,
                  types: cloned?.types,
                },
                productIndex,
              );
            }}
            text={productType}
          />
        ))}
      </div>
    </div>
  );
};

const Checkbox = ({ returnedValue, value, text }) => {
  return (
    <div className={clsx("flex", "mr-4")}>
      <input
        onChange={(e) => {
          returnedValue(e.target.checked, text);
        }}
        type="checkbox"
        checked={value}
        className="checkbox"
      />
      <div className={"ml-2"}>{text}</div>
    </div>
  );
};

export default AddProducts;
