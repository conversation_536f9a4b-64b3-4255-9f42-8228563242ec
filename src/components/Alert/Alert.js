import React from "react";

const Alert = ({ text, type }) => {
  const alertClasses = {
    success: "alert-success",
    warning: "alert-warning",
    error: "alert-error",
  };

  return (
    <div
      role="alert"
      className={`alert fixed left-80 right-10 top-30 z-9 mt-4 max-w-sm ${alertClasses[type]}`}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-6 w-6 shrink-0 stroke-current"
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
      <span>{text}</span>
    </div>
  );
};

export default Alert;
