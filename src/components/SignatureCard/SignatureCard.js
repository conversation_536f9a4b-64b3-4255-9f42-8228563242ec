"use client";
import React, { useRef } from 'react';
import SignatureCanvas from 'react-signature-canvas';
import { clsx } from 'clsx';

const SignatureCard = ({ salesObj, setSalesObj, disabled = false }) => {
	const sigPadRef = useRef(null);

	const clearSignature = () => {
		sigPadRef.current.clear();
		setSalesObj(prev => ({
			...prev,
			signature: null
		}));
	};

	const saveSignature = () => {
		if (!sigPadRef.current.isEmpty()) {
			const signatureData = sigPadRef.current.toDataURL();
			setSalesObj(prev => ({
				...prev,
				signature: signatureData
			}));
		}
	};

	return (
		<div className={clsx("bg-white", "p-8", "rounded-2xl", "mt-4", "shadow-3", "shadow-gray-300")}>
			<div className={clsx("border-b border-stroke py-4 px-6.5 dark:border-strokedark")}>
				<h3 className="font-medium text-black dark:text-white">
					Customer Signature
				</h3>
			</div>
			<div className="p-6.5">
				<div className="mb-4.5">
					<div className="border border-stroke rounded-sm">
						<SignatureCanvas
							ref={sigPadRef}
							canvasProps={{
								className: 'w-full h-[200px] bg-white'
							}}
							backgroundColor="rgb(255, 255, 255)"
						/>
					</div>
					{!disabled && (
						<div className="flex gap-4 mt-4">
							<button
								onClick={clearSignature}
								className={clsx(
									"btn",
									"bg-danger",
									"text-white",
									"px-4",
									"py-2"
								)}
							>
								Clear
							</button>
							<button
								onClick={saveSignature}
								className={clsx(
									"btn",
									"bg-primary",
									"text-white",
									"px-4",
									"py-2"
								)}
							>
								Save Signature
							</button>
						</div>
					)}
				</div>

				{salesObj.signature && (
					<div className="mt-4">
						<p className="text-sm text-meta-1">Signature saved</p>
						<img
							src={salesObj.signature}
							alt="Saved signature"
							className="mt-2 border border-stroke rounded-sm"
							style={{ maxWidth: '100%', height: 'auto' }}
						/>
					</div>
				)}
			</div>
		</div>
	);
};

export default SignatureCard; 