"use client";
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { fetch } from '../helpers';

const TermsAndConditions = () => {
	const [terms, setTerms] = useState([]);
	const [alertMessage, setAlertMessage] = useState('');
	const [alertType, setAlertType] = useState('');
	const [termId, setTermId] = useState(null);
	const [showAlert, setShowAlert] = useState(false);
	const router = useRouter(); // Initialize router

	useEffect(() => {
		const fetchTerms = async () => {
			try {
				const response = await fetch('/terms', 'get', {});

				const data = await response.data;
				setTerms(data.terms || []); // Assuming the API returns an object with a terms array
				setTermId(data._id);
			} catch (error) {
				console.error('Error fetching terms:', error);
				setAlertMessage('Error fetching terms. Please try again.'); // Set error alert message
				setAlertType('error');
				setShowAlert(true);
			}
		};

		fetchTerms();
	}, []);

	const handleChange = (index, field, value) => {
		const newTerms = [...terms];
		newTerms[index][field] = value;
		setTerms(newTerms);
	};

	const addTerm = () => {
		setTerms([...terms, { title: '', content: '' }]);
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		try {
			// Check if we're creating new terms or updating existing ones
			console.log('terms:', terms);
			console.log('termId:', termId);
			if (terms.length > 0 && termId) {
				// Update existing terms
				fetch(`/terms/${termId}`, 'put', { terms, termId }, {
					headers: {
						'Content-Type': 'application/json',
					},
				});

				setAlertMessage('Terms updated successfully!');
				setAlertType('success');
				setShowAlert(true);
			} else {
				// Create new terms
				const response = await fetch(`/terms`, 'post', { terms, termId }, {
					headers: {
						'Content-Type': 'application/json',
					},
				});


				const data = await response.json();
				console.log('Terms created successfully:', data);
				setAlertMessage('Terms created successfully!');
				setAlertType('success');
				setShowAlert(true);

				// Show alert popup
				alert('Terms created successfully!');
			}

			setTimeout(() => {
				router.push('/'); // Redirect to main page after 3 seconds
			}, 3000);
		} catch (error) {
			console.error('Error updating terms:', error);
			setAlertMessage('Error updating terms. Please try again.');
			setAlertType('error');
			setShowAlert(true);
		}
	};

	const removeTerm = (index) => {
		const newTerms = [...terms];
		newTerms.splice(index, 1);
		setTerms(newTerms);
	};

	return (
		<div className="grid grid-cols-1 gap-4 md:gap-6 2xl:gap-7.5">
			<div className="bg-white dark:bg-boxdark">
				<div className="px-6.5 py-4">
					{alertMessage && showAlert && (
						<div className={`alert ${alertType === 'success' ? 'alert-success' : 'alert-error'}`}>
							{alertMessage}
						</div>
					)}
				</div>
				<form onSubmit={handleSubmit}>
					<div>
						{terms.length > 0 ? (
							terms.map((term, index) => (
								<div key={index} className="mb-4.5 rounded-sm border border-stroke dark:border-strokedark p-6.5 shadow-default rounded-lg">
									<div className="flex justify-between items-center mb-2.5">
										<label className="mb-2.5 block text-black dark:text-white">
											Term {index + 1}
										</label>
										{terms.length > 1 && (
											<button
												type="button"
												onClick={() => removeTerm(index)}
												className="flex items-center justify-center rounded-md bg-danger py-2 px-3 text-white hover:bg-opacity-90"
											>
												Remove
											</button>
										)}
									</div>
									<input
										type="text"
										placeholder="Title"
										value={term.title}
										onChange={(e) => handleChange(index, 'title', e.target.value)}
										className="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 text-black outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:text-white dark:focus:border-primary"
									/>
									<textarea
										rows="6"
										placeholder="Content"
										value={term.content}
										onChange={(e) => handleChange(index, 'content', e.target.value)}
										className="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 text-black outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:text-white dark:focus:border-primary mt-4.5"
									></textarea>
								</div>
							))
						) : (
							<div className="text-center py-4">
								<p>No terms available. Please add a term.</p>
							</div>
						)}

						<div className="flex flex-wrap gap-3 px-6.5 py-4">
							<button
								type="button"
								onClick={addTerm}
								className="flex items-center justify-center rounded-md bg-primary py-2 px-6 text-white hover:bg-opacity-90"
							>
								Add Term
							</button>
							<button
								type="submit"
								className="flex items-center justify-center rounded-md bg-success py-2 px-6 text-white hover:bg-opacity-90"
							>
								Submit
							</button>
						</div>
					</div>
				</form>
			</div>
		</div>
	);
};

export default TermsAndConditions;