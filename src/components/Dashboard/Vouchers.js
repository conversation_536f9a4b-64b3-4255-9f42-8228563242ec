/* eslint-disable @next/next/no-img-element */
"use client";
import React, { useEffect, useState } from "react";
import { fetch } from "@/helpers";
import { auth } from "@/configs/firebase";

const Vouchers = () => {
	const [vouchers, setVouchers] = useState([]);
	const [editingVoucher, setEditingVoucher] = useState(null);
	const [formData, setFormData] = useState({
		voucherName: "",
		voucherImage: "",
		voucherCode: ""
	});
	const [imageFile, setImageFile] = useState(null);
	const [alert, setAlert] = useState({ type: '', message: '' });

	useEffect(() => {
		getVouchers();
	}, []);

	const getVouchers = async () => {
		try {
			const res = await fetch("/vouchers", 'get', {}, {
				headers: {
					'authorization': await auth.currentUser.getIdToken(true)
				},
			});
			const data = await res.data;
			setVouchers(Array.isArray(data) ? data : []);
		} catch (err) {
			console.log("error getting vouchers:", err);
		}
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		const formDataToSend = new FormData();
		formDataToSend.append("voucherName", formData.voucherName);
		formDataToSend.append("voucherCode", formData.voucherCode);
		if (imageFile) {
			formDataToSend.append("voucherImage", imageFile);
		}

		try {
			if (editingVoucher) {
				formDataToSend.append("voucherId", editingVoucher._id);
				const res = await fetch(`/voucher`, "put", formDataToSend, {
					headers: {
						"Content-Type": "multipart/form-data",
						'authorization': await auth.currentUser.getIdToken(true)
					},
				});
				if (!res.data) {
					throw new Error('Failed to update voucher');
				}
				setAlert({ type: 'success', message: 'Voucher updated successfully!' });
			} else {
				const res = await fetch("/voucher", "post", formDataToSend, {
					headers: {
						"Content-Type": "multipart/form-data",
						'authorization': await auth.currentUser.getIdToken(true)
					},
				});
				if (!res.data) {
					throw new Error('Failed to create voucher');
				}
				setAlert({ type: 'success', message: 'Voucher created successfully!' });
			}
			getVouchers();
			resetForm();
		} catch (err) {
			console.log("error saving voucher:", err);
			setAlert({ type: 'error', message: err.message || 'An error occurred while saving the voucher' });
		}
	};

	const handleDelete = async (voucherId) => {
		try {
			await fetch(`/voucher/${voucherId}`, "delete");
			getVouchers();
			setAlert({ type: 'success', message: 'Voucher deleted successfully!' });
		} catch (err) {
			console.log("error deleting voucher:", err);
			setAlert({ type: 'error', message: 'Failed to delete voucher' });
		}
	};

	const resetForm = () => {
		setFormData({
			voucherName: "",
			voucherImage: "",
			voucherCode: ""
		});
		setImageFile(null);
		setEditingVoucher(null);
	};

	const startEdit = (voucher) => {
		setEditingVoucher(voucher);
		setFormData({
			voucherName: voucher.voucherName,
			voucherCode: voucher.voucherCode
		});
		setImageFile(null); // Reset image file on edit
	};

	return (
		<>
			{alert.message && (
				<div className={`alert ${alert.type === 'success' ? 'alert-success' : 'alert-error'} mb-4`}>
					<div className="flex-1">
						<span>{alert.message}</span>
					</div>
					<div className="flex-none">
						<button onClick={() => setAlert({ type: '', message: '' })} className="btn btn-ghost btn-sm">
							✕
						</button>
					</div>
				</div>
			)}
			<div className="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
				<div className="border-b border-stroke py-4 px-6.5 dark:border-strokedark">
					<h3 className="font-medium text-black dark:text-white">
						{editingVoucher ? 'Edit Voucher' : 'Add New Voucher'}
					</h3>
				</div>

				<form className="p-6.5" onSubmit={handleSubmit}>
					<div className="mb-4.5 flex flex-col gap-6 xl:flex-row">
						<div className="w-full xl:w-1/2">
							<label className="mb-2.5 block text-black dark:text-white">
								Voucher Name
							</label>
							<input
								type="text"
								placeholder="Enter voucher name"
								className="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 text-black outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:text-white dark:focus:border-primary"
								value={formData.voucherName}
								onChange={(e) => setFormData({ ...formData, voucherName: e.target.value })}
							/>
						</div>

						<div className="w-full xl:w-1/2">
							<label className="mb-2.5 block text-black dark:text-white">
								Voucher Code
							</label>
							<input
								type="text"
								placeholder="Enter voucher code"
								className="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 text-black outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:text-white dark:focus:border-primary"
								value={formData.voucherCode}
								onChange={(e) => setFormData({ ...formData, voucherCode: e.target.value })}
							/>
						</div>
					</div>

					<div className="mb-4.5">
						<label className="mb-2.5 block text-black dark:text-white">
							Image Upload
						</label>
						<input
							type="file"
							accept="image/*"
							className="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 text-black outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:text-white dark:focus:border-primary"
							onChange={(e) => setImageFile(e.target.files[0])}
						/>
					</div>

					<button className="flex w-full justify-center rounded bg-primary p-3 font-medium text-gray">
						{editingVoucher ? 'Update Voucher' : 'Add Voucher'}
					</button>
				</form>
			</div>

			<div className="mt-4 rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
				<div className="border-b border-stroke py-4 px-6.5 dark:border-strokedark">
					<h3 className="font-medium text-black dark:text-white">
						Voucher List
					</h3>
				</div>

				<div className="p-6.5">
					<div className="flex flex-col">
						<div className="grid grid-cols-4 rounded-sm bg-gray-2 dark:bg-meta-4 sm:grid-cols-6">
							<div className="p-2.5 xl:p-5">
								<h5 className="text-sm font-medium uppercase xsm:text-base">
									Voucher Name
								</h5>
							</div>
							<div className="p-2.5 text-center xl:p-5">
								<h5 className="text-sm font-medium uppercase xsm:text-base">
									Code
								</h5>
							</div>
							<div className="p-2.5 text-center xl:p-5">
								<h5 className="text-sm font-medium uppercase xsm:text-base">
									Image
								</h5>
							</div>
							<div className="hidden p-2.5 text-center sm:block xl:p-5">
								<h5 className="text-sm font-medium uppercase xsm:text-base">
									Actions
								</h5>
							</div>
						</div>

						{vouchers.map((voucher, index) => (
							<div
								className="grid grid-cols-4 border-b border-stroke dark:border-strokedark sm:grid-cols-6"
								key={index}
							>
								<div className="flex items-center gap-3 p-2.5 xl:p-5">
									<p className="text-black dark:text-white">{voucher.voucherName}</p>
								</div>
								<div className="flex items-center justify-center p-2.5 xl:p-5">
									<p className="text-black dark:text-white">{voucher.voucherCode}</p>
								</div>
								<div className="flex items-center justify-center p-2.5 xl:p-5">
									{voucher.voucherImageUrl ? (
										<img
											src={voucher.voucherImageUrl}
											alt={voucher.voucherName}
											className="h-12 w-12 rounded-[10px] object-cover"
											onError={(e) => {
												e.target.onerror = null;
												e.target.src = "/placeholder-image.png"; // Fallback image
											}}
										/>
									) : (
										<div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
											<span className="text-xs text-gray-500">No image</span>
										</div>
									)}
								</div>
								<div className="flex items-center justify-center gap-4 p-2.5 xl:p-5">
									<button
										onClick={() => startEdit(voucher)}
										className="hover:text-primary"
									>
										Edit
									</button>
									<button
										onClick={() => handleDelete(voucher._id)}
										className="hover:text-danger"
									>
										Delete
									</button>
								</div>
							</div>
						))}
					</div>
				</div>
			</div>
		</>
	);
};

export default Vouchers; 