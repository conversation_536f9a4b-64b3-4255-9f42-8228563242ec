import { useState, useEffect } from "react";
import { fetch } from "../../helpers/index";
import { clsx } from "clsx";
import Input from "../Input/Input";

const InstallerCheckerCard = ({ salesObj, setSalesObj, disabled = false, errors }) => {
	const [installers, setInstallers] = useState([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		getInstallers();
	}, []);

	const getInstallers = async () => {
		setLoading(true);
		try {
			const res = await fetch("/installation-checker-list");
			const data = await res.data;
			setInstallers(data);
		} catch (err) {
			console.log("Error fetching installers:", err);
		} finally {
			setLoading(false);
		}
	};

	const handleInstallerChange = (e) => {
		setSalesObj((prev) => ({
			...prev,
			installerChecker: e.target.value
		}));
	};

	return (
		<div
			className={clsx(
				"bg-white",
				"p-8",
				"rounded-2xl",
				"mt-4",
				"shadow-3",
				"shadow-gray-300",
			)}
		>
			<div className={clsx("font-semibold", "capitalize", "text-2xl")}>
				Checker
			</div>
			<div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
				<div className={"w-full sm:m-2"}>
					<Input
						disabled={disabled}
						returnedValue={(value) => {
							setSalesObj((prev) => ({
								...prev,
								installer: value,
							}));
						}}
						inputField={{
							name: "Installer",
							type: "text",
							value: salesObj?.installer,
						}}
						error={errors?.installer}
					/>
				</div>
				<div className={"w-full sm:m-2"}>
					<Input
						disabled={disabled}
						returnedValue={(value) => {
							setSalesObj((prev) => ({
								...prev,
								checker: value,
							}));
						}}
						inputField={{
							name: "Checker",
							type: "text",
							value: salesObj?.checker,
						}}
						error={errors?.checker} // Pass error if exists
					/>
				</div>
			</div>
		</div>
	);
};

export default InstallerCheckerCard; 