import _ from "lodash";
import { clsx } from "clsx";
import { useContext, useEffect, useState } from "react";
import UserContext from "../../Context/UserContext";

const InstallerInput = ({ returnedValue, value, disabled = false, installers }) => {
  const context = useContext(UserContext);
  const [installerList, setInstallerList] = useState([])

  // TO DO LIST
  // admin can select all installer, but installer are only able to select themselves.

  const getOptions = () => {
    return [
      {
        name: "Please select a user",
        value: "",
      },
      {
        name: context?.user?.name,
        value: context?.user?._id,
      },
    ];
  };


  const getInstallerOptions = async () => {
    // Check if user is admin
    const isAdmin = context?.user?.roles?.includes("platform-admin");

    if (isAdmin) {
      console.log('installers:', installers)
      setInstallerList(installers)
    } else {
      const data = getOptions();
      setInstallerList(data)
    }
  };

  useEffect(() => {
    getInstallerOptions();
  }, [installers]);

  return (
    <label className="form-control mt-4 w-full">
      <div className="label">
        <span className="label-text">Installer In Charge:</span>
      </div>
      <select
        onChange={(e) => {
          console.log("installer value:", e.target.value);
          returnedValue(e.target.value);
        }}
        value={value}
        disabled={disabled}
        className="select select-bordered w-full"
      >
        {_.map(installerList, (option) => (
          <option value={option?.value} className={clsx("max-w-1")}>
            {option?.name}
          </option>
        ))}
      </select>
    </label>
  );
};

export default InstallerInput;
