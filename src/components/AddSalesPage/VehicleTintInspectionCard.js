import { clsx } from "clsx";
import Input from "../Input/Input";
import InstallerInput from "./InstallerInput";

const VehicleTintInspectionCard = ({
  salesObj,
  productOptions,
  setSalesObj,
  disabled = false,
  errors,
  installers
}) => {
  const getProductOptions = (field) => {
    const po = _.chain(productOptions)
      .map((po) => {
        const isIncluded = _.includes(po.types, field);
        if (isIncluded) {
          return po;
        } else {
          return null;
        }
      })
      .value();
    return [
      { name: "Please select your product", value: null },
      ..._.compact(po),
    ];
  };

  return (
    <div
      className={clsx(
        "bg-white",
        "p-8",
        "rounded-2xl",
        "mt-4",
        "shadow-3",
        "shadow-gray-300",
      )}
    >
      <div className={clsx("font-semibold", "capitalize", "text-2xl")}>
        Vehicle&apos;s Inspection
      </div>

      <img
        className={"mt-4 justify-self-center"}
        alt={"tint-sheet"}
        src={"/images/tint-sheet.png"}
      />
      {/* first row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  frontWindScreen: {
                    ...prev.vehicleInspection.frontWindScreen,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "Front Windscreen",
              type: "select",
              value: salesObj?.vehicleInspection?.frontWindScreen?.product,
              options: getProductOptions("Front Wind Screen"),
            }}
            disabled={disabled}
          />
          <InstallerInput
            installers={installers}
            value={
              salesObj?.vehicleInspection?.frontWindScreen?.installerInCharge
            }
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  frontWindScreen: {
                    ...prev.vehicleInspection.frontWindScreen,
                    installerInCharge: value,
                  },
                },
              }));
            }}
          />
        </div>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  firstSunRoof: {
                    ...prev.vehicleInspection.firstSunRoof,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "1st Sunroof",
              type: "select",
              value: salesObj?.vehicleInspection?.firstSunRoof?.product,
              options: getProductOptions("First Sun Roof"),
            }}
            disabled={disabled}
          />
          <InstallerInput
            installers={installers}
            value={salesObj?.vehicleInspection?.firstSunRoof?.installerInCharge}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  firstSunRoof: {
                    ...prev.vehicleInspection.firstSunRoof,
                    installerInCharge: value,
                  },
                },
              }));
            }}
          />
        </div>
      </div>
      {/* second row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  rearWindScreen: {
                    ...prev.vehicleInspection.rearWindScreen,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "Rear Windscreen",
              type: "select",
              value: salesObj?.vehicleInspection?.rearWindScreen?.product,
              options: getProductOptions("Rear Wind Screen"),
            }}
            disabled={disabled}
          />
          <InstallerInput
            value={
              salesObj?.vehicleInspection?.rearWindScreen?.installerInCharge
            }
            installers={installers}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  rearWindScreen: {
                    ...prev.vehicleInspection.rearWindScreen,
                    installerInCharge: value,
                  },
                },
              }));
            }}
          />
        </div>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  secondSunRoof: {
                    ...prev.vehicleInspection.secondSunRoof,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "2nd Sunroof",
              type: "select",
              value: salesObj?.vehicleInspection?.secondSunRoof?.product,
              options: getProductOptions("Second Sun Roof"),
            }}
            disabled={disabled}
          />
          <InstallerInput
            installers={installers}
            value={
              salesObj?.vehicleInspection?.secondSunRoof?.installerInCharge
            }
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  secondSunRoof: {
                    ...prev.vehicleInspection.secondSunRoof,
                    installerInCharge: value,
                  },
                },
              }));
            }}
          />
        </div>
      </div>
      {/* third row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  L1SideMirror: {
                    ...prev.vehicleInspection.L1SideMirror,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "L1 Side Mirror",
              type: "select",
              value: salesObj?.vehicleInspection?.L1SideMirror?.product,
              options: getProductOptions("L1 Side Mirror"),
            }}
            disabled={disabled}
          />
          <InstallerInput
            installers={installers}
            value={salesObj?.vehicleInspection?.L1SideMirror?.installerInCharge}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  L1SideMirror: {
                    ...prev.vehicleInspection.L1SideMirror,
                    installerInCharge: value,
                  },
                },
              }));
            }}
          />
        </div>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  R1SideMirror: {
                    ...prev.vehicleInspection.R1SideMirror,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "R1 Side Mirror",
              type: "select",
              value: salesObj?.vehicleInspection?.R1SideMirror?.product,
              options: getProductOptions("R1 Side Mirror"),
            }}
            disabled={disabled}
          />
          <InstallerInput
            installers={installers}
            value={salesObj?.vehicleInspection?.R1SideMirror?.installerInCharge}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  R1SideMirror: {
                    ...prev.vehicleInspection.R1SideMirror,
                    installerInCharge: value,
                  },
                },
              }));
            }}
          />
        </div>
      </div>
      {/* forth row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  L2SideMirror: {
                    ...prev.vehicleInspection.L2SideMirror,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "L2 Side Mirror",
              type: "select",
              value: salesObj?.vehicleInspection?.L2SideMirror?.product,
              options: getProductOptions("L2 Side Mirror"),
            }}
            disabled={disabled}
          />
          <InstallerInput
            installers={installers}
            value={salesObj?.vehicleInspection?.L2SideMirror?.installerInCharge}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  L2SideMirror: {
                    ...prev.vehicleInspection.L2SideMirror,
                    installerInCharge: value,
                  },
                },
              }));
            }}
          />
        </div>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  R2SideMirror: {
                    ...prev.vehicleInspection.R2SideMirror,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "R2 Side Mirror",
              type: "select",
              value: salesObj?.vehicleInspection?.R2SideMirror?.product,
              options: getProductOptions("R2 Side Mirror"),
            }}
            disabled={disabled}
          />
          <InstallerInput
            installers={installers}
            value={salesObj?.vehicleInspection?.R2SideMirror?.installerInCharge}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  R2SideMirror: {
                    ...prev.vehicleInspection.R2SideMirror,
                    installerInCharge: value,
                  },
                },
              }));
            }}
          />
        </div>
      </div>
      {/* fifth row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  L3SideMirror: {
                    ...prev.vehicleInspection.L3SideMirror,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "L3 Side Mirror",
              type: "select",
              value: salesObj?.vehicleInspection?.L3SideMirror?.product,
              options: getProductOptions("L3 Side Mirror"),
            }}
            disabled={disabled}
          />
          <InstallerInput
            installers={installers}
            value={salesObj?.vehicleInspection?.L3SideMirror?.installerInCharge}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  L3SideMirror: {
                    ...prev.vehicleInspection.L3SideMirror,
                    installerInCharge: value,
                  },
                },
              }));
            }}
          />
        </div>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  R3SideMirror: {
                    ...prev.R3SideMirror,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "R3 Side Mirror",
              type: "select",
              value: salesObj?.vehicleInspection?.R3SideMirror?.product,
              options: getProductOptions("R3 Side Mirror"),
            }}
            disabled={disabled}
          />
          <InstallerInput
            installers={installers}
            value={salesObj?.vehicleInspection?.R3SideMirror?.installerInCharge}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  R3SideMirror: {
                    ...prev.vehicleInspection.R3SideMirror,
                    installerInCharge: value,
                  },
                },
              }));
            }}
          />
        </div>
      </div>
      {/* sixth row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <div className="label mt-4">
            <span className="label-text">Others:</span>
          </div>
          {/* check box options */}
          <div className={"'min-[290px]:flex-col sm:flex-row"}>
            <div className={clsx("flex")}>
              <input
                type='checkbox'
                className='checkbox'
                disabled={disabled}
                onChange={(e) => {
                  setSalesObj((prev) => ({
                    ...prev,
                    vehicleInspection: {
                      ...prev.vehicleInspection,
                      changeDarkness: e.target.checked,
                    },
                  }));
                }}
                checked={salesObj?.vehicleInspection?.changeDarkness}
              />
              <div className={"ml-2"}>Change Darkness (RM 50)</div>
            </div>
            <div className={clsx("flex", "mt-2")}>
              <input
                type='checkbox'
                className='checkbox'
                disabled={disabled}
                onChange={(e) => {
                  setSalesObj((prev) => ({
                    ...prev,
                    vehicleInspection: {
                      ...prev.vehicleInspection,
                      removeOldFilmScreen: e.target.checked,
                    },
                  }));
                }}
                checked={salesObj?.vehicleInspection?.removeOldFilmScreen}
              />
              <div className={"ml-2"}>Remove Old Film Screen (RM 80)</div>
            </div>
            <div className={clsx("flex", "mt-2")}>
              <input
                type='checkbox'
                className='checkbox'
                disabled={disabled}
                onChange={(e) => {
                  setSalesObj((prev) => ({
                    ...prev,
                    vehicleInspection: {
                      ...prev.vehicleInspection,
                      ownerWaiting: e.target.checked,
                    },
                  }));
                }}
                checked={salesObj?.vehicleInspection?.ownerWaiting}
              />
              <div className={"ml-2"}>Owner Waiting</div>
            </div>
          </div>
          {/* end of check box options */}
        </div>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  estimateTimeDeliver: value,
                },
              }));
            }}
            inputField={{
              name: "Estimate time deliver",
              type: "date",
              value: salesObj?.vehicleInspection?.estimateTimeDeliver,
            }}
            disabled={disabled}
          />
        </div>
      </div>
    </div>
  );
};

export default VehicleTintInspectionCard;
