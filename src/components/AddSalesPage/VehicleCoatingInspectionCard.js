import { clsx } from "clsx";
import InstallerInput from "./InstallerInput";
import Input from "../Input/Input";

const VehicleCoatingInspectionCard = ({
  salesObj,
  productOptions,
  setSalesObj,
  disabled = false,
  errors,
  installers
}) => {
  const getProductOptions = (field) => {
    const po = _.chain(productOptions)
      .map((po) => {
        const isIncluded = _.includes(po.types, field);
        if (isIncluded) {
          return po;
        } else {
          return null;
        }
      })
      .value();
    return [
      { name: "Please select your product", value: null },
      ..._.compact(po),
    ];
  };

  const setValue = (field, key, value) => {
    setSalesObj((prev) => ({
      ...prev,
      vehicleInspection: {
        ...prev.vehicleInspection,
        [field]: {
          ...prev.vehicleInspection[field],
          [key]: value,
        },
      },
    }));
  };

  return (
    <div
      className={clsx(
        "bg-white",
        "p-8",
        "rounded-2xl",
        "mt-4",
        "shadow-3",
        "shadow-gray-300",
      )}
    >
      <div className={clsx("font-semibold", "capitalize", "text-2xl")}>
        Vehicle&apos;s Inspection
      </div>
      <img
        className={"mt-4 justify-self-center"}
        alt={"ppf-sheet"}
        src={"/images/ppf-sheet.png"}
      />
      {/* first row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  bodyCoating: {
                    ...prev.vehicleInspection.bodyCoating,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "Body Coating",
              type: "select",
              options: getProductOptions("Body Coating"),
              value: salesObj?.vehicleInspection?.bodyCoating?.product,
            }}
            disabled={disabled}
          />
          <InstallerInput
            installers={installers}
            value={salesObj?.vehicleInspection?.bodyCoating?.installerInCharge}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  bodyCoating: {
                    ...prev.vehicleInspection.bodyCoating,
                    installerInCharge: value,
                  },
                },
              }));
            }}
          />
        </div>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  headlampCoating: {
                    ...prev.vehicleInspection.headlampCoating,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "Headlamp Coating",
              type: "select",
              options: getProductOptions("Headlamp Coating"),
              value: salesObj?.vehicleInspection?.headlampCoating?.product,
            }}
            disabled={disabled}
          />
          <InstallerInput
            installers={installers}
            value={
              salesObj?.vehicleInspection?.headlampCoating?.installerInCharge
            }
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  headlampCoating: {
                    ...prev.vehicleInspection.headlampCoating,
                    installerInCharge: value,
                  },
                },
              }));
            }}
          />
        </div>
      </div>
      {/* second row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  engineCoating: {
                    ...prev.vehicleInspection.engineCoating,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "Engine Coating",
              type: "select",
              options: getProductOptions("Engine Coating"),
              value: salesObj?.vehicleInspection?.engineCoating?.product,
            }}
            disabled={disabled}
          />
          <InstallerInput
            installers={installers}
            value={
              salesObj?.vehicleInspection?.engineCoating?.installerInCharge
            }
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  engineCoating: {
                    ...prev.vehicleInspection.engineCoating,
                    installerInCharge: value,
                  },
                },
              }));
            }}
          />
        </div>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  chromeCoating: {
                    ...prev.vehicleInspection.chromeCoating,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "Chrome Coating",
              type: "select",
              options: getProductOptions("Chrome Coating"),
              value: salesObj?.vehicleInspection?.chromeCoating?.product,
            }}
            disabled={disabled}
          />
          <InstallerInput
            installers={installers}
            value={
              salesObj?.vehicleInspection?.chromeCoating?.installerInCharge
            }
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  chromeCoating: {
                    ...prev.vehicleInspection.chromeCoating,
                    installerInCharge: value,
                  },
                },
              }));
            }}
          />
        </div>
      </div>
      {/* third row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <Input
            value={salesObj?.vehicleInspection?.bodyCoating?.installerInCharge}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  windscreenCoating: {
                    ...prev.vehicleInspection.windscreenCoating,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "Windscreen Coating",
              type: "select",
              options: getProductOptions("Windscreen Coating"),
              value: salesObj?.vehicleInspection?.windscreenCoating?.product,
            }}
            disabled={disabled}
          />
          <InstallerInput
            installers={installers}
            value={
              salesObj?.vehicleInspection?.windscreenCoating?.installerInCharge
            }
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  windscreenCoating: {
                    ...prev.vehicleInspection.windscreenCoating,
                    installerInCharge: value,
                  },
                },
              }));
            }}
          />
          {/* check box options */}
          <div
            className={clsx(
              "flex",
              "mt-4",
              "justify-between",
              "min-[290px]:flex-col sm:flex-row",
            )}
          >
            <div className={clsx("flex", "mb-2", "sm:mb-0")}>
              <input
                onChange={(e) => {
                  setValue("windscreenCoating", "front", e.target.checked);
                }}
                type="checkbox"
                checked={salesObj?.vehicleInspection?.windscreenCoating?.front}
                className="checkbox"
              />
              <div className={"ml-3"}>Front</div>
            </div>
            <div className={clsx("flex", "mb-2", "sm:mb-0")}>
              <input
                onChange={(e) => {
                  setValue("windscreenCoating", "rear", e.target.checked);
                }}
                type="checkbox"
                checked={salesObj?.vehicleInspection?.windscreenCoating?.rear}
                className="checkbox"
              />
              <div className={"ml-3"}>Rear</div>
            </div>
            <div className={clsx("flex", "mb-2", "sm:mb-0")}>
              <input
                onChange={(e) => {
                  setValue("windscreenCoating", "side", e.target.checked);
                }}
                type="checkbox"
                checked={salesObj?.vehicleInspection?.windscreenCoating?.side}
                className="checkbox"
              />
              <div className={"ml-3"}>Side</div>
            </div>
            <div className={clsx("flex", "mb-2", "sm:mb-0")}>
              <input
                onChange={(e) => {
                  setValue("windscreenCoating", "sunroof", e.target.checked);
                }}
                type="checkbox"
                checked={
                  salesObj?.vehicleInspection?.windscreenCoating?.sunroof
                }
                className="checkbox"
              />
              <div className={"ml-3"}>Sunroof</div>
            </div>
          </div>
          {/* end of check box options */}
        </div>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(value) => {
              setValue("rimAndCaliperCoating", "product", value);
            }}
            inputField={{
              name: "Rim & Caliper Coating",
              type: "select",
              options: getProductOptions("Rim & Caliper Coating"),
              value: salesObj?.vehicleInspection?.rimAndCaliperCoating?.product,
            }}
            disabled={disabled}
          />
          <InstallerInput
            installers={installers}
            value={
              salesObj?.vehicleInspection?.rimAndCaliperCoating
                ?.installerInCharge
            }
            returnedValue={(value) => {
              setValue("rimAndCaliperCoating", "installerInCharge", value);
            }}
          />
        </div>
      </div>
      {/* forth row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(value) => {
              setValue("interiorCoating", "product", value);
            }}
            inputField={{
              name: "Interior Coating",
              type: "select",
              options: getProductOptions("Interior Coating"),
              value: salesObj?.vehicleInspection?.interiorCoating?.product,
            }}
            disabled={disabled}
          />
          <InstallerInput
            installers={installers}
            value={
              salesObj?.vehicleInspection?.interiorCoating?.installerInCharge
            }
            returnedValue={(value) => {
              setValue("interiorCoating", "installerInCharge", value);
            }}
          />
          {/* check box options */}
          <div
            className={clsx("flex", "mt-4", "min-[290px]:flex-col sm:flex-row")}
          >
            <div className={clsx("flex", "mb-2", "sm:mb-0")}>
              <input
                onChange={(e) => {
                  setValue("interiorCoating", "plastic", e.target.checked);
                }}
                type="checkbox"
                checked={salesObj?.vehicleInspection?.interiorCoating?.plastic}
                className="checkbox"
                disabled={disabled}
              />
              <div className={"ml-3"}>Plastic</div>
            </div>
            <div className={clsx("flex", "mb-2", "sm:mb-0", "sm:ml-6")}>
              <input
                onChange={(e) => {
                  setValue("interiorCoating", "leather", e.target.checked);
                }}
                type="checkbox"
                checked={salesObj?.vehicleInspection?.interiorCoating?.leather}
                className="checkbox"
                disabled={disabled}
              />
              <div className={"ml-3"}>Leather</div>
            </div>
            <div className={clsx("flex", "mb-2", "sm:mb-0", "sm:ml-6")}>
              <input
                onChange={(e) => {
                  setValue("interiorCoating", "fabric", e.target.checked);
                }}
                type="checkbox"
                checked={salesObj?.vehicleInspection?.interiorCoating?.fabric}
                className="checkbox"
                disabled={disabled}
              />
              <div className={"ml-3"}>Fabric</div>
            </div>
          </div>
          {/* end of check box options */}
        </div>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(value) => {
              setValue("exteriorCoating", "product", value);
            }}
            inputField={{
              name: "Exterior Coating",
              type: "select",
              options: getProductOptions("Exterior Coating"),
              value: salesObj?.vehicleInspection?.exteriorCoating?.product,
            }}
            disabled={disabled}
          />
          <InstallerInput
            installers={installers}
            value={
              salesObj?.vehicleInspection?.exteriorCoating?.installerInCharge
            }
            returnedValue={(value) => {
              setValue("exteriorCoating", "installerInCharge", value);
            }}
          />
          {/* check box options */}
          <div
            className={clsx("flex", "mt-4", "min-[290px]:flex-col sm:flex-row")}
          >
            <div className={clsx("flex", "mb-2", "sm:mb-0")}>
              <input
                onChange={(e) => {
                  setValue("exteriorCoating", "plastic", e.target.checked);
                }}
                type="checkbox"
                checked={salesObj?.vehicleInspection?.exteriorCoating?.plastic}
                className="checkbox"
                disabled={disabled}
              />
              <div className={"ml-3"}>Plastic</div>
            </div>
            <div className={clsx("flex", "mb-2", "sm:mb-0", "sm:ml-6")}>
              <input
                onChange={(e) => {
                  setValue("exteriorCoating", "rubber", e.target.checked);
                }}
                type="checkbox"
                checked={salesObj?.vehicleInspection?.exteriorCoating?.rubber}
                className="checkbox"
                disabled={disabled}
              />
              <div className={"ml-3"}>Rubber</div>
            </div>
          </div>
          {/* end of check box options */}
        </div>
      </div>
      {/* fifth row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <div className="label mt-4">
            <span className="label-text">Others:</span>
          </div>
          {/* check box options */}
          <div className={"min-[290px]:flex-col sm:flex-row"}>
            <div className={clsx("flex")}>
              <input
                onChange={(e) => {
                  setSalesObj((prev) => ({
                    ...prev,
                    vehicleInspection: {
                      ...prev.vehicleInspection,
                      detailingWash: e.target.checked,
                    },
                  }));
                }}
                type="checkbox"
                checked={salesObj?.vehicleInspection?.detailingWash}
                className="checkbox"
                disabled={disabled}
              />
              <div className={"ml-2"}>Detailing Wash</div>
            </div>
            <div className={clsx("flex", "mt-2")}>
              <input
                onChange={(e) => {
                  setSalesObj((prev) => ({
                    ...prev,
                    vehicleInspection: {
                      ...prev.vehicleInspection,
                      polishLayer: e.target.checked,
                    },
                  }));
                }}
                type="checkbox"
                checked={salesObj?.vehicleInspection?.polishLayer}
                className="checkbox"
                disabled={disabled}
              />
              <div className={"ml-2"}>Polish Layer</div>
            </div>
            <div className={clsx("flex", "mt-2")}>
              <input
                onChange={(e) => {
                  setSalesObj((prev) => ({
                    ...prev,
                    vehicleInspection: {
                      ...prev.vehicleInspection,
                      windScreenRemovalProcess: e.target.checked,
                    },
                  }));
                }}
                type="checkbox"
                checked={salesObj?.vehicleInspection?.windScreenRemovalProcess}
                className="checkbox"
                disabled={disabled}
              />
              <div className={"ml-2"}>Windscreen Removal Process</div>
            </div>
            <div className={clsx("flex", "mt-2")}>
              <input
                onChange={(e) => {
                  setSalesObj((prev) => ({
                    ...prev,
                    vehicleInspection: {
                      ...prev.vehicleInspection,
                      ownerWaiting: e.target.checked,
                    },
                  }));
                }}
                type="checkbox"
                checked={salesObj?.vehicleInspection?.ownerWaiting}
                className="checkbox"
                disabled={disabled}
              />
              <div className={"ml-2"}>Owner Waiting</div>
            </div>
          </div>
          {/* end of check box options */}
        </div>
        <div className={"w-full sm:m-2"}>
          <Input
            returnedValue={(date) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  estimateTimeDeliver: date,
                },
              }));
            }}
            inputField={{
              name: "Estimate time deliver",
              type: "date",
              value: salesObj?.vehicleInspection?.estimateTimeDeliver,
            }}
            disabled={disabled}
          />
        </div>
      </div>
    </div>
  );
};

export default VehicleCoatingInspectionCard;
