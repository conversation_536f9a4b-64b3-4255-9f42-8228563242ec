import { clsx } from "clsx";
import Input from "../Input/Input";
import InstallerInput from "./InstallerInput";
import _ from "lodash";
import { useContext, useEffect, useState } from "react";
import UserContext from "../../Context/UserContext";

const AddOnDetailsCard = ({
  salesObj,
  productOptions,
  setSalesObj,
  disabled = false,
  errors
}) => {
  const context = useContext(UserContext);
  const [isInstaller, setIsInstaller] = useState(false);

  useEffect(() => {
    const isI = context?.user?.roles?.includes("installer")
    console.log('is installer:', isI)
    setIsInstaller(isI);
  }, []);

  const getProductOptions = (field) => {
    const po = _.chain(productOptions)
      .map((po) => {
        const isIncluded = _.includes(po.types, field);
        if (isIncluded) {
          return po;
        } else {
          return null;
        }
      })
      .value();
    return [
      { name: "Please select your item", value: null },
      ..._.compact(po),
    ];
  };

  return (
    <div
      className={clsx(
        "bg-white",
        "p-8",
        "rounded-2xl",
        "mt-4",
        "shadow-3",
        "shadow-gray-300",
      )}
    >
      <div className={clsx("font-semibold", "capitalize", "text-2xl")}>
        Add On Details
      </div>
      {/* first row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <Input
            disabled={disabled}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                addOnDetails: {
                  ...prev.addOnDetails,
                  product: value,
                },
              }));
            }}
            inputField={{
              name: "Product",
              type: "select",
              options: [
                { name: "Please select your item", value: null },
                ..._.map(productOptions, (product) => ({
                  name: product.name,
                  value: product.value,
                })),
              ],
            }}
            value={salesObj?.addOnDetails?.product}
            error={errors?.addOnDetails?.product}
          />
        </div>
        <div className={"w-full sm:m-2"}>
          <Input
            disabled={disabled}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                addOnDetails: {
                  ...prev.addOnDetails,
                  productType: value,
                },
              }));
            }}
            value={salesObj?.addOnDetails?.productType}
            inputField={{
              name: "Product Type",
              type: "select",
              options: [
                { name: "Please select your item", value: null },
                ..._.chain(productOptions)
                  .find(
                    (p) => p.value === salesObj?.addOnDetails?.product
                  )
                  ?.get("types", [])
                  .map((type) => ({
                    name: type,
                    value: type,
                  }))
                  .value(),
              ],
            }}
            error={errors?.addOnDetails?.productType}
          />
        </div>
      </div>
      {/* first row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <Input
            disabled={disabled}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  fullGlassCoating: {
                    ...prev.vehicleInspection.fullGlassCoating,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "Full Glass Coating",
              type: "select",
              value: salesObj?.vehicleInspection?.fullGlassCoating?.product,
              options: getProductOptions("Full Glass Coating"),
            }}
          />
          <InstallerInput
            value={salesObj?.vehicleInspection?.fullGlassCoating?.installerInCharge}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  fullGlassCoating: {
                    ...prev.vehicleInspection.fullGlassCoating,
                    installerInCharge: value,
                  },
                },
              }));
            }}
            disabled={!isInstaller}
          />
        </div>
        <div className={"w-full sm:m-2"}>
          <Input
            disabled={disabled}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  frontHeadlampPPF: {
                    ...prev.vehicleInspection.frontHeadlampPPF,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "Front Headlamp PPF",
              type: "select",
              value: salesObj?.vehicleInspection?.frontHeadlampPPF?.product,
              options: getProductOptions("Front Headlamp PPF"),
            }}
          />
          <InstallerInput
            value={salesObj?.vehicleInspection?.frontHeadlampPPF?.installerInCharge}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  frontHeadlampPPF: {
                    ...prev.vehicleInspection.frontHeadlampPPF,
                    installerInCharge: value,
                  },
                },
              }));
            }}
            disabled={!isInstaller}
          />
        </div>
      </div>
      {/* second row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <Input
            disabled={disabled}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  fourSideDoorStepsPPF: {
                    ...prev.vehicleInspection.fourSideDoorStepsPPF,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "Four Side Door Steps PPF",
              type: "select",
              value: salesObj?.vehicleInspection?.fourSideDoorStepsPPF?.product,
              options: getProductOptions("Four Side Door Steps PPF"),
            }}
          />
          <InstallerInput
            value={salesObj?.vehicleInspection?.fourSideDoorStepsPPF?.installerInCharge}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  fourSideDoorStepsPPF: {
                    ...prev.vehicleInspection.fourSideDoorStepsPPF,
                    installerInCharge: value,
                  },
                },
              }));
            }}
            disabled={!isInstaller}
          />
        </div>
        <div className={"w-full sm:m-2"}>
          <Input
            disabled={disabled}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  threeTimesCarWash: {
                    ...prev.vehicleInspection.threeTimesCarWash,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "3 Times Car Wash",
              type: "select",
              value: salesObj?.vehicleInspection?.threeTimesCarWash?.product,
              options: getProductOptions("3 Times Car Wash"),
            }}
          />
          <InstallerInput
            value={salesObj?.vehicleInspection?.threeTimesCarWash?.installerInCharge}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  threeTimesCarWash: {
                    ...prev.vehicleInspection.threeTimesCarWash,
                    installerInCharge: value,
                  },
                },
              }));
            }}
            disabled={!isInstaller}
          />
        </div>
      </div>
      {/* third row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <Input
            disabled={disabled}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  carPlate: {
                    ...prev.vehicleInspection.carPlate,
                    product: value,
                  },
                },
              }));
            }}
            inputField={{
              name: "Car Plate",
              type: "select",
              value: salesObj?.vehicleInspection?.carPlate?.product,
              options: getProductOptions("Car Plate"),
            }}
          />
          <InstallerInput
            value={salesObj?.vehicleInspection?.carPlate?.installerInCharge}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleInspection: {
                  ...prev.vehicleInspection,
                  carPlate: {
                    ...prev.vehicleInspection.carPlate,
                    installerInCharge: value,
                  },
                },
              }));
            }}
            disabled={!isInstaller}
          />
        </div>
        <div className={"w-full sm:m-2"} />
      </div>
    </div>
  );
};

export default AddOnDetailsCard;
