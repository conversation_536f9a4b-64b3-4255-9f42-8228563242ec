import React, { useEffect } from "react";
import { HiOutlineHome, HiOutlineTruck } from "react-icons/hi";
import { clsx } from "clsx";

const OrderTypeCard = ({ salesObj, setSalesObj, disabled = false, errors }) => {
	const handleTypeChange = (type) => {
		setSalesObj((prev) => ({
			...prev,
			serviceType: type
		}));
	};

	useEffect(() => {
		// Auto-select order type based on serviceType if salesObj is not empty
		if (salesObj && salesObj.serviceType) {
			if (salesObj.serviceType === "coating" || salesObj.serviceType === "tint") {
				setSalesObj((prev) => ({
					...prev,
					orderType: "car"
				}));
			} else if (salesObj.serviceType === "building-tint") {
				setSalesObj((prev) => ({
					...prev,
					orderType: "building"
				}));
			}
		}
	}, [salesObj.serviceType, setSalesObj]);

	const isDisabled = salesObj.createdAt !== undefined || disabled;

	return (
		<div
			className={clsx(
				"bg-white",
				"p-8",
				"rounded-2xl",
				"mt-4",
				"shadow-3",
				"shadow-gray-300",
			)}
		>
			<div className={clsx("font-semibold", "capitalize", "text-2xl", "mb-4")}>
				Order Type
			</div>
			<div className={clsx("flex", "gap-4", "min-[290px]:flex-col sm:flex-row")}>
				<button
					type="button"
					onClick={() => handleTypeChange("tint")}
					disabled={isDisabled}
					className={clsx(
						"flex-1",
						"p-4",
						"border",
						"rounded-lg",
						"flex",
						"items-center",
						"justify-center",
						"gap-2",
						"transition-colors",
						(salesObj?.serviceType === "tint" || salesObj?.serviceType === "coating") ? "bg-blue-100 border-blue-500" : "border-gray-300",
						isDisabled ? "opacity-50 cursor-not-allowed" : "hover:bg-blue-50"
					)}
				>
					<HiOutlineTruck className="text-xl" />
					<span>Car</span>
				</button>
				<button
					type="button"
					onClick={() => handleTypeChange("building-tint")}
					disabled={isDisabled}
					className={clsx(
						"flex-1",
						"p-4",
						"border",
						"rounded-lg",
						"flex",
						"items-center",
						"justify-center",
						"gap-2",
						"transition-colors",
						salesObj?.orderType === "building" ? "bg-blue-100 border-blue-500" : "border-gray-300",
						isDisabled ? "opacity-50 cursor-not-allowed" : "hover:bg-blue-50"
					)}
				>
					<HiOutlineHome className="text-xl" />
					<span>Building</span>
				</button>
			</div>
			{errors?.orderType && (
				<div className="text-red-500 text-sm mt-2">{errors.orderType}</div>
			)}
		</div>
	);
};

export default OrderTypeCard; 