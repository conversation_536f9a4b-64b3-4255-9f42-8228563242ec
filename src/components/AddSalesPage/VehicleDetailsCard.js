import { clsx } from "clsx";
import Input from "../Input/Input";
import { useEffect, useState } from "react";
import _ from "lodash";

const VehicleDetailsCard = ({
  salesObj,
  vehicleOptions,
  setSalesObj,
  disabled = false,
  errors,
}) => {
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const [modelOptions, setModelOptions] = useState([]);

  useEffect(() => {
    const foundVehicle = _.find(
      vehicleOptions,
      (item) => item.name === salesObj?.vehicleBrand
    );
    setSelectedVehicle(foundVehicle);
    if (foundVehicle) {
      const foundModels = _.get(foundVehicle, "models", []).map((model) => ({
        name: model.name,
        value: model.name,
      }));
      setModelOptions(foundModels);
    }
  }, [salesObj?.vehicleBrand, vehicleOptions]);

  return (
    <div
      className={clsx(
        "bg-white",
        "p-8",
        "rounded-2xl",
        "mt-4",
        "shadow-3",
        "shadow-gray-300",
      )}
    >
      <div className={clsx("font-semibold", "capitalize", "text-2xl")}>
        Vehicle&apos;s Details
      </div>
      {/* first row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <Input
            disabled={disabled}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleBrand: value,
              }));
            }}
            inputField={{
              name: "Vehicle Brand",
              type: "select",
              options: _.map(vehicleOptions, (vehicle) => ({
                name: vehicle.name,
                value: vehicle.name,
              })),
            }}
            value={salesObj?.vehicleBrand}
            error={errors?.vehicleBrand}
          />
        </div>
        <div className={"w-full sm:m-2"}>
          <Input
            disabled={disabled}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                vehicleModel: value,
              }));
            }}
            inputField={{
              name: "Vehicle Model",
              type: "select",
              options: modelOptions,
            }}
            value={salesObj?.vehicleModel}
            error={errors?.vehicleModel}
          />
        </div>
      </div>
      {/* second row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <Input
            disabled={disabled}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                carPlateNo: value,
              }));
            }}
            inputField={{
              name: "Car Plate No.",
            }}
            value={salesObj?.carPlateNo}
            placeholder={"QSW7500"}
            error={errors?.carPlateNo}
          />
        </div>
        <div className={"w-full sm:m-2"}>
          <Input
            disabled={disabled}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                carColor: value,
              }));
            }}
            inputField={{
              name: "Vehicle Colour",
            }}
            value={salesObj?.carColor}
            placeholder="E.g. White"
            error={errors?.carColor}
          />
        </div>
      </div>
      {/* third row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <Input
            disabled={disabled}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                roadTaxExpiryDate: value,
              }));
            }}
            inputField={{
              name: "Road Tax Expiry Date",
              type: "date",
            }}
            value={salesObj?.roadTaxExpiryDate}
            error={errors?.roadTaxExpiryDate}
          />
        </div>
        <div className={"w-full sm:m-2"}></div>
      </div>
    </div>
  );
};

export default VehicleDetailsCard;
