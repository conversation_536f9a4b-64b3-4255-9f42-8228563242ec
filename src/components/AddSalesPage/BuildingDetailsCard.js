import React, { useEffect } from "react";
import { HiOutlineOfficeBuilding } from "react-icons/hi";
import _ from "lodash";
import { clsx } from "clsx";
import Input from "../Input/Input";

const BuildingDetailsCard = ({
	packageOptions = [],
	productOptions = [],
	salesObj,
	setSalesObj,
	disabled = false,
	errors,
	users
}) => {
	const handleChange = (e) => {
		const { name, value } = e.target;
		setSalesObj((prev) => ({
			...prev,
			[name]: value,
		}));
	};

	// Update product options when package changes
	useEffect(() => {
		if (salesObj.package) {
			const selectedPackage = packageOptions.find(p => p._id === salesObj.package);
			if (selectedPackage && selectedPackage.products && selectedPackage.products.length > 0) {
				// Set the first product as default when package changes
				setSalesObj(prev => ({
					...prev,
					product: selectedPackage.products[0].name
				}));
			}
		}
	}, [salesObj.package, packageOptions]);

	// Fetch package options for building-tint service type
	useEffect(() => {
		const buildingTintPackage = packageOptions.find(p => p.serviceType === "building-tint");
		if (buildingTintPackage) {
			setSalesObj(prev => ({
				...prev,
				package: buildingTintPackage._id,
				product: buildingTintPackage.products[0]?.name || ""
			}));
		}
	}, [packageOptions]);

	return (
		<div
			className={clsx(
				"bg-white",
				"p-8",
				"rounded-2xl",
				"mt-4",
				"shadow-3",
				"shadow-gray-300",
			)}
		>
			<div className={clsx("font-semibold", "capitalize", "text-2xl")}>
				Building Details
			</div>
			<div className={"w-full sm:m-2"}>
				<Input
					disabled={disabled}
					returnedValue={(value) => {
						setSalesObj((prev) => ({
							...prev,
							salesman: value,
						}));
					}}
					inputField={{
						name: "Salesman",
						type: "select",
						options: [
							{
								name: "Please select a salesman",
								value: "",
							},
							..._.map(users, (user) => ({
								name: user.name,
								value: user._id,
							})),
						],
					}}
					error={errors?.salesman}
					value={salesObj?.salesman?._id}
				/>
			</div>
			{/* first row */}
			<div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
				<div className={"w-full sm:m-2"}>
					<Input
						disabled={disabled}
						returnedValue={(value) => {
							setSalesObj((prev) => ({
								...prev,
								buildingName: value,
							}));
						}}
						inputField={{
							name: "Building Name",
							type: "text",
						}}
						value={salesObj?.buildingName}
					/>
				</div>
				<div className={"w-full sm:m-2"}>
					<Input
						disabled={disabled}
						returnedValue={(value) => {
							setSalesObj((prev) => ({
								...prev,
								buildingAddress: value,
							}));
						}}
						inputField={{
							name: "Building Address",
							type: "text",
						}}
						value={salesObj?.buildingAddress}
					/>
				</div>
			</div>
			{/* second row */}
			<div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
				<div className={"w-full sm:m-2"}>
					<Input
						disabled={disabled}
						returnedValue={(val) => {
							setSalesObj((prev) => ({
								...prev,
								package: val,
							}));
						}}
						inputField={{
							name: "Package",
							type: "select",
							options: _.map(packageOptions, (option) => ({
								name: _.get(option, "name", ""),
								value: _.get(option, "_id", ""),
							})),
						}}
						value={salesObj?.package}
					/>
				</div>
				<div className={"w-full sm:m-2"}>
					<Input
						disabled={disabled}
						returnedValue={(value) => {
							setSalesObj((prev) => ({
								...prev,
								finalPrice: value,
							}));
						}}
						inputField={{
							name: "Pricing (RM)",
							type: "price",
						}}
						value={salesObj?.finalPrice}
					/>
				</div>
			</div>

			<div className="mb-4">
				<label className="mb-2.5 block text-black dark:text-white">
					Product
				</label>
				<div className="relative z-20 bg-transparent dark:bg-meta-4">
					<select
						name="buildingProduct"
						value={salesObj?.buildingProduct || ""}
						onChange={handleChange}
						className="relative z-20 w-full appearance-none rounded border border-stroke bg-transparent py-3 px-5 outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-meta-4 dark:focus:border-primary"
					>
						{productOptions.map((option) => (
							<option key={option.value} value={option.value}>
								{option.name}
							</option>
						))}
					</select>
					<span className="absolute top-1/2 right-4 z-30 -translate-y-1/2">
						<svg
							className="fill-current"
							width="24"
							height="24"
							viewBox="0 0 24 24"
							fill="none"
							xmlns="http://www.w3.org/2000/svg"
						>
							<g opacity="0.8">
								<path
									fillRule="evenodd"
									clipRule="evenodd"
									d="M5.29289 8.29289C5.68342 7.90237 6.31658 7.90237 6.70711 8.29289L12 13.5858L17.2929 8.29289C17.6834 7.90237 18.3166 7.90237 18.7071 8.29289C19.0976 8.68342 19.0976 9.31658 18.7071 9.70711L12.7071 15.7071C12.3166 16.0976 11.6834 16.0976 11.2929 15.7071L5.29289 9.70711C4.90237 9.31658 4.90237 8.68342 5.29289 8.29289Z"
									fill=""
								></path>
							</g>
						</svg>
					</span>
				</div>
			</div>

			<div className="mb-4">
				<label className="mb-2.5 block text-black dark:text-white">
					Additional Notes
				</label>
				<textarea
					name="buildingNotes"
					value={salesObj.buildingNotes || ""}
					placeholder="Enter any additional notes about the building"
					onChange={handleChange}
					rows="4"
					className="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
				></textarea>
			</div>
		</div>
	);
};

export default BuildingDetailsCard; 