import { clsx } from "clsx";
import Input from "../Input/Input";
import { useEffect, useState } from "react";
import { fetch } from "../../helpers";
import _ from "lodash";
import DatePicker from "react-tailwindcss-datepicker";
import moment from "moment";

const OrderDetailsCard = ({
	salesObj,
	setSalesObj,
	packageOptions,
	users,
	disabled = false,
	errors,
}) => {
	const [value, setValue] = useState({
		startDate: new Date(),
		endDate: new Date(),
	});
	const leadOptions = ["OK go", "FB", "PM", "Walk In"];

	const handleValueChange = (newValue) => {
		setValue(newValue);
		setSalesObj((prev) => ({
			...prev,
			date: newValue,
		}));
	};

	return (
		<div
			className={clsx(
				"bg-white",
				"p-8",
				"rounded-2xl",
				"mt-4",
				"shadow-3",
				"shadow-gray-300",
			)}
		>
			<div className={clsx("font-semibold", "capitalize", "text-2xl")}>
				Order&apos;s Details
			</div>
			{/* first row */}
			<div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
				<div className={"w-full sm:m-2"}>
					<Input
						disabled={disabled}
						returnedValue={(value) => {
							setSalesObj((prev) => ({
								...prev,
								salesman: value,
							}));
						}}
						inputField={{
							name: "Salesman",
							type: "select",
							options: [
								{
									name: "Please select a salesman",
									value: "",
								},
								..._.map(users, (user) => ({
									name: user.name,
									value: user._id,
								})),
							],
						}}
						error={errors?.salesman}
						value={salesObj?.salesman?._id || salesObj?.salesman}
					/>
				</div>
				<div className={"w-full sm:m-2"}>
					<Input
						disabled={disabled}
						returnedValue={(value) => {
							setSalesObj((prev) => ({
								...prev,
								carInDate: value,
							}));
						}}
						inputField={{
							name: "Car In Date",
							type: "date",
						}}
						error={errors?.carInDate}
						value={salesObj?.carInDate || moment().format('YYYY-MM-DD')}
					/>
				</div>
			</div>
			{/* second row */}
			<div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
				<div className={"w-full sm:m-2"}>
					<Input
						disabled={disabled}
						returnedValue={(val) => {
							setSalesObj((prev) => ({
								...prev,
								serviceType: val,
							}));
						}}
						inputField={{
							name: "Service Type",
							type: "select",
							options: [
								{
									value: "tint",
									name: "Tint",
								},
								{
									value: "coating",
									name: "Coating",
								}, {
									value: "building-tint",
									name: "Building Tint",
								},
							],
						}}
						error={errors?.serviceType}
						value={salesObj?.serviceType || ""}
					/>
				</div>
				<div className={"w-full sm:m-2"}>
					<Input
						disabled={disabled}
						returnedValue={(val) => {
							setSalesObj((prev) => ({
								...prev,
								package: val,
							}));
						}}
						inputField={{
							name: "Package",
							type: "select",
							options: [
								{
									value: "",
									name: "Please select a package",
								},
								..._.map(packageOptions, (option) => ({
									name: _.get(option, "name", ""),
									value: _.get(option, "_id", ""),
								})),
							],
						}}
						error={errors?.package}
						value={salesObj?.package}
					/>
				</div>
			</div>
			{/* third row */}
			<div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
				<div className={"w-full sm:m-2"}>
					<Input
						disabled={disabled}
						returnedValue={(value) => {
							console.log("value:", value);
							setSalesObj((prev) => ({
								...prev,
								finalPrice: value,
							}));
						}}
						inputField={{
							name: "Pricing (RM)",
							type: "price",
						}}
						error={errors?.finalPrice}
						value={salesObj?.finalPrice}
					/>
				</div>
				<div className={"w-full sm:m-2"}>
					<Input
						disabled={disabled}
						returnedValue={(value) => {
							setSalesObj((prev) => ({
								...prev,
								paymentMethod: value,
							}));
						}}
						inputField={{
							name: "Payment Pay",
							type: "select",
							options: [
								{
									name: "Card",
									value: "Card",
								},
								{
									name: "Cash",
									value: "Cash",
								},
								{
									name: "Online Transfer",
									value: "Online Transfer",
								},
							],
						}}
						error={errors?.paymentMethod}
						value={salesObj?.paymentMethod}
					/>
				</div>
			</div>
			<div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
				<div className={"w-full sm:m-2"}>
					<Input
						disabled={disabled}
						returnedValue={(value) => {
							setSalesObj((prev) => ({
								...prev,
								customerLead: value,
							}));
						}}
						inputField={{
							name: "Lead Source",
							type: "select",
							options: leadOptions.map((option) => ({
								name: option,
								value: option,
							})),
						}}
						error={errors?.customerLead}
						value={salesObj?.customerLead}
					/>
				</div>
			</div>
		</div>
	);
};

export default OrderDetailsCard;
