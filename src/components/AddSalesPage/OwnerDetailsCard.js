import { clsx } from "clsx";
import Input from "../Input/Input";
import { useEffect, useState } from "react";

const OwnerDetailsCard = ({ salesObj, setSalesObj, disabled = false, errors }) => {
  const [nric, setNric] = useState("");
  const [hpNo, setHPNo] = useState("");

  useEffect(() => {
    console.log("monitor nric", nric);
  }, [nric]);

  return (
    <div
      className={clsx(
        "bg-white",
        "p-8",
        "rounded-2xl",
        "mt-4",
        "shadow-3",
        "shadow-gray-300",
      )}
    >
      <div className={clsx("font-semibold", "capitalize", "text-2xl")}>
        Owner&apos;s Details
      </div>
      {/* first row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <Input
            disabled={disabled}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                ownerName: value,
              }));
            }}
            inputField={{
              name: "Owner Name",
              type: "text",
            }}
            placeholder='E.g. Abraham Wong'
            value={salesObj?.ownerName}
            error={errors?.ownerName}
          />
        </div>
        <div className={"w-full sm:m-2"}>
          <Input
            placeholder='E.g. 980101134455'
            disabled={disabled}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                ownerNRIC: value,
              }));
            }}
            value={salesObj?.ownerNRIC}
            inputField={{
              name: "NRIC No",
              type: "number",
              pattern: "[0-9]*",
            }}
            error={errors?.ownerNRIC}
          />
        </div>
      </div>
      {/* second row */}
      <div className={clsx("flex", "min-[290px]:flex-col sm:flex-row")}>
        <div className={"w-full sm:m-2"}>
          <Input
            disabled={disabled}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                ownerHPNo: value,
              }));
            }}
            value={salesObj?.ownerHPNo}
            inputField={{
              name: "HP No",
              type: "number",
            }}
            placeholder='E.g. 0124459384'
            error={errors?.ownerHPNo}
          />
        </div>
        <div className={"w-full sm:m-2"}>
          <Input
            disabled={disabled}
            returnedValue={(value) => {
              setSalesObj((prev) => ({
                ...prev,
                ownerEmail: value,
              }));
            }}
            inputField={{
              name: "Email",
              type: "email",
            }}
            value={salesObj?.ownerEmail}
            placeholder='E.g. <EMAIL>'
            error={errors?.ownerEmail}
          />
        </div>
      </div>
    </div>
  );
};

export default OwnerDetailsCard;
