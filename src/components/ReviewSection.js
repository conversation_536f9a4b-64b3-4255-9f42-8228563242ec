import React, { useState, useEffect } from 'react';
import { clsx } from "clsx";

const ReviewSection = ({ salesObj, setSalesObj }) => {
	const [reviewText, setReviewText] = useState('');
	const [reviewBy, setReviewBy] = useState('');
	const [score, setScore] = useState(0);
	const [review, setReview] = useState(salesObj.review || {}); // Initialize review from salesObj

	useEffect(() => {
		if (salesObj.review) {
			setReview(salesObj.review); // Set review from salesObj on first load
		}
	}, [salesObj]);

	const handleSubmit = (e) => {
		e.preventDefault();
		if (reviewText && reviewBy) {
			const newReview = { review: reviewText, by: reviewBy, score };
			setReview(newReview); // Set review as an object
			setSalesObj((prev) => ({ ...prev, review: newReview })); // Update salesObj with new review
			setReviewText('');
			setReviewBy('');
			setScore(0);
		}
	};

	const handleStarClick = (rating) => {
		if (score === rating) {
			setScore(0); // Cancel out the score if the same star is clicked again
		} else {
			setScore(rating);
		}
	};

	return (
		<div
			className={clsx(
				"bg-white",
				"p-8",
				"rounded-2xl",
				"mt-4",
				"shadow-3",
				"shadow-gray-300",
			)}
		>
			<div className={clsx("font-semibold", "capitalize", "text-2xl")}>
				Customer Reviews
			</div>
			<form onSubmit={handleSubmit} className="mt-4">
				<div className="mb-4">
					<label className="block text-gray-700 mb-2">Your Name</label>
					<input
						type="text"
						value={reviewBy}
						onChange={(e) => setReviewBy(e.target.value)}
						placeholder="Enter your name"
						className={clsx("w-full", "p-3", "border", "rounded-lg")}
						required
					/>
				</div>
				<div className="mb-4">
					<label className="block text-gray-700 mb-2">Rating</label>
					<div className="flex">
						{[1, 2, 3, 4, 5].map((star) => (
							<button
								key={star}
								type="button"
								onClick={() => handleStarClick(star)} // Handle star click
								className={clsx(
									"text-2xl",
									"mr-1",
									"focus:outline-none",
									score >= star ? "text-yellow-400" : "text-gray-300"
								)}
							>
								★
							</button>
						))}
					</div>
				</div>
				<div>
					<label className="block text-gray-700 mb-2">Your Review</label>
					<textarea
						value={reviewText}
						onChange={(e) => setReviewText(e.target.value)}
						placeholder="Write your review here..."
						className={clsx("w-full", "p-3", "border", "rounded-lg")}
						rows="4"
						required
					/>
				</div>
				<button
					type="submit"
					className={clsx(
						"mt-4",
						"px-4",
						"py-2",
						"bg-blue-600",
						"text-white",
						"rounded-lg",
						"hover:bg-blue-700",
						"transition-colors"
					)}
				>
					Submit Review
				</button>
			</form>
			<div className="mt-6">
				{review.by ? ( // Check if review object has a 'by' property
					<div
						className={clsx(
							"p-4",
							"border",
							"rounded-lg",
							"mb-3",
							"bg-gray-50"
						)}
					>
						<div className="flex justify-between items-center mb-2">
							<span className="font-semibold">{review.by}</span>
							<div className="flex">
								{[...Array(5)].map((_, i) => (
									<span
										key={i}
										className={clsx(
											"text-xl",
											i < review.score ? "text-yellow-400" : "text-gray-300"
										)}
									>
										★
									</span>
								))}
							</div>
						</div>
						<p>{review.review}</p>
					</div>
				) : (
					<p className={clsx("text-gray-500", "italic")}>No reviews yet.</p>
				)}
			</div>
		</div>
	);
};

export default ReviewSection;