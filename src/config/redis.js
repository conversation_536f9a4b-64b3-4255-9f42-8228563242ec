const { createClient } = require('redis');

let redisClient = null;

// redis
// broadcast to all the websockets
const listener = (wss) => (message, channel) => {
  console.log(channel, message);
  wss.clients.forEach((client) => {
    client.send(message);
  });
};
const runRedis = async (wss) => {
  const client = await createClient({ url: 'redis://34.126.136.111:6379', password: '1f6efcec-f779-49ef-a990-fab6d9506f7b' })
    .on('error', (err) => console.log('Redis Client Error', err))
    .connect();

  if (client.isReady) {
    console.log('redis server is ready');
  }

  redisClient = client;
  // const subscriber = client.duplicate()
  // subscriber.on('error', err => console.error(err));
  // await subscriber.connect()
  client.subscribe('channel', listener(wss));

  // await client.disconnect();
};

runRedis();

module.exports = { redisClient, runRedis };
