/* eslint-disable no-case-declarations */
/* eslint-disable no-param-reassign */

const { WebSocketServer } = require('ws');
const { XMLBuilder } = require('fast-xml-parser');
const _ = require('lodash');
const AccessRecordModel = require('#models/AccessRecord.model');
const DeviceModel = require('#models/Device.model');
const AccessModel = require('#models/Access.model');
const fs = require('node:fs');
const sharp = require('sharp');
const { redisClient } = require('#config/redis');

const sockets = [];

const sendToDevice = async (deviceWS, event, from, body) => {
  await redisClient.publish('channel', JSON.stringify(body));
  wsSwitch({ Request: event }, deviceWS, from, body);
};

const addFace = async (premiseId, user) => {
  console.log('adding face');
  const devices = await DeviceModel.find({ premiseId }).lean();
  console.log('devices:', devices);

  const socketsWithinSamePremises = _.map(sockets, (socket) => {
    console.log('web sockets:', socket.serialNo);
   const foundDevice = _.find(devices, { serialNumber: socket.serialNo });

    if (foundDevice) {
      return socket;
    }
  });

    console.log('sockets within same premises:', socketsWithinSamePremises);
  _.map(socketsWithinSamePremises, async (socket) => {
    const {
      imageArrayBuffer,
      fileSize,
    } = await fetch(user?.faceImageUrl)
      .then(async (res) => ({
        imageArrayBuffer: await res.arrayBuffer(),
        fileSize: res.headers.get('Content-Length'),
      }));

    // need to downgrade the face size because the device only accept 32kb.
    console.log('filesize:', fileSize);
    console.log('face size in kb:', fileSize / 1000);

    if (fileSize > 32000) {
      console.log('need to do something here');
      const downgradedFaceSize = await sharp(imageArrayBuffer)
        .jpeg({ quality: 50 })
        .resize(200)
        .toBuffer();

      const enrollFaceData = {
        UserID: 100,
        PhotoSize: Buffer.from(downgradedFaceSize).length,
        PhotoData: Buffer.from(downgradedFaceSize).toString('base64'),
      };

      const name = Buffer.from(user?.fullName, 'utf-16le').toString('base64');
      const userData = {
        UserID: 100,
        Type: 'Set',
        Name: name,
      };

      await sendToDevice(socket, 'EnrollFaceByPhoto', 'server', enrollFaceData);
      await sendToDevice(socket, 'SetUserData', 'server', userData);
    } else {
      const enrollFaceData = {
        UserID: 100,
        PhotoSize: Buffer.from(imageArrayBuffer).length,
        PhotoData: Buffer.from(imageArrayBuffer).toString('base64'),
      };

      const name = Buffer.from(user?.fullName, 'utf-16le').toString('base64');
      const userData = {
        UserID: 100,
        Type: 'Set',
        Name: name,
      };

      await sendToDevice(socket, 'EnrollFaceByPhoto', 'user', enrollFaceData);
      await sendToDevice(socket, 'SetUserData', 'user', userData);
    }
  });
};

const deleteFace = async (premiseId, user) => {
  const devices = await DeviceModel.find({ premiseId }).lean();

  const socketsWithinSamePremises = _.map(sockets, (socket) => {
    console.log('web sockets:', socket.serialNo);
   const foundDevice = _.find(devices, { serialNumber: socket.serialNo });

    if (foundDevice) {
      return socket;
    }
  });

    console.log('sockets within same premises:', socketsWithinSamePremises);
  _.map(socketsWithinSamePremises, async (socket) => {
      const name = Buffer.from(user?.fullName, 'utf-16le').toString('base64');
      const userData = {
        UserID: 100,
        Type: 'Delete',
        Name: name,
      };

      await sendToDevice(socket, 'SetUserData', 'user', userData);
  });
};

const wsSwitch = async (msg, ws, from, body) => {
  const { Request, Response, DeviceSerialNo } = msg;

  console.log(`${from}---message:`, msg);
  if (Response === 'GetUserData') {
    console.log('logging GetUserData:', msg);
  }

  const sendMessage = { '?xml': '', Message: {} };
  const builder = new XMLBuilder();

  switch (Request) {
    case 'Register':
      sendMessage.Message.Response = Request;
      sendMessage.Message.DeviceSerialNo = DeviceSerialNo;
      sendMessage.Message.Token = 'token-001';
      sendMessage.Message.Result = 'OK';
      break;

    case 'Login':
      // need to tell the device that logged in successfully first, if not looping will occur here as the device assume it is not logged in yet.
      sendMessage.Message.Response = Request;
      sendMessage.Message.DeviceSerialNo = DeviceSerialNo;
      sendMessage.Message.Result = 'OK';
      const xmlContent = builder.build(sendMessage);
      console.log('xml content:', xmlContent);
      ws.send(xmlContent);

      ws.serialNo = DeviceSerialNo;
      sockets.push(ws);
      const foundDevice = await DeviceModel.findOne({
        serialNumber: DeviceSerialNo,
      });
      if (!foundDevice) {
        await DeviceModel.create({
          serialNumber: DeviceSerialNo,
          name: _.get(msg, 'ProductName', ''),
          premiseId: '000000000000000000000000',
          departmentId: '000000000000000000000000',
          status: 'online',
        });
      }

      // need to sync users here.
    const accesses = await AccessModel.find({
        premiseId: foundDevice.premiseId,
        isDeleted: false,
      }).populate('uid').lean();

      // every time sync need to delete all faces inside, then put in the new one.
      const deleteFaces = await Promise.all(_.map(accesses, async (access, accessIndex) => {
        const dBody = {};
        dBody.UserID = accessIndex;
        dBody.Type = 'Set';
        dBody.Name = access?.uid?.fullName;

        await sendToDevice(ws, 'SetUserData', 'user', dBody);
      }));
    await Promise.all(deleteFaces);

      // then start adding faces
    const addFaceData = await Promise.all(_.map(accesses, async (access, accessIndex) => {
      const enrollFaceBody = {};
      const dataBody = { };
        if (!_.isEmpty(access?.uid?.faceImageUrl)) {
          const {
            imageArrayBuffer,
            fileSize,
          } = await fetch(access?.uid?.faceImageUrl)
            .then(async (res) => ({
              imageArrayBuffer: await res.arrayBuffer(),
              fileSize: res.headers.get('Content-Length'),
            }));
          // need to downgrade the face size because the device only accept 32kb.
          console.log('filesize:', fileSize);
          console.log('face size in kb:', fileSize / 1000);

          if (fileSize > 32000) {
            console.log('need to do something here');
           const downgradedFaceSize = await sharp(imageArrayBuffer)
              .jpeg({ quality: 50 })
              .resize(200).toBuffer();

            enrollFaceBody.UserID = accessIndex;
            enrollFaceBody.PhotoSize = await downgradedFaceSize.length;
            enrollFaceBody.PhotoData = Buffer.from(downgradedFaceSize)
                .toString('base64');

            const user = access?.uid?.fullName;
            // need to use utf-16le as the system's string is encoded in this format.
            const name = Buffer.from(user, 'utf-16le')
              .toString('base64');

            console.log('name:', name);

            dataBody.UserID = accessIndex;
            dataBody.Type = 'Set';
            dataBody.Name = name;
          } else {
            enrollFaceBody.UserID = accessIndex;
            enrollFaceBody.PhotoSize = Buffer.from(imageArrayBuffer).length;
            enrollFaceBody.PhotoData = Buffer.from(imageArrayBuffer)
              .toString('base64');

            const user = access?.uid?.fullName;
            // need to use utf-16le as the system's string is encoded in this format.
            const name = Buffer.from(user, 'utf-16le')
              .toString('base64');

            console.log('name:', name);

            dataBody.UserID = accessIndex;
            dataBody.Type = 'Set';
            dataBody.Name = name;
          }
    console.log('data:', { dataBody, enrollFaceBody });
          return {
            dataBody,
            enrollFaceBody,
          };
        }
        return null;
      }));

    const sortedFaceOrder = _.compact(_.sortBy(addFaceData, 'dataBody.UserID'));
    for (let i = 0; i < sortedFaceOrder.length; i++) {
      console.log('data:', { dataBody: sortedFaceOrder[i]?.dataBody, enrollFaceBody: sortedFaceOrder[i]?.enrollFaceBody });
        await sendToDevice(ws, 'EnrollFaceByPhoto', 'user', sortedFaceOrder[i]?.enrollFaceBody);
        await sendToDevice(ws, 'SetUserData', 'user', sortedFaceOrder[i]?.dataBody);
    }
      break;

    case 'GetFirstUserData':
      sendMessage.Message.Request = Request;
      break;

    case 'SetUserData':
      sendMessage.Message = {
        Request,
        ...body,
      };
      break;

    case 'GetUserData':
      sendMessage.Message = {
        Request,
        ...body,
      };
      break;

    case 'GetNextUserData':
      sendMessage.Message.Request = Request;
      break;

    case 'GetNextUserDataExt':
      sendMessage.Message.Request = Request;
      sendMessage.Message.UserID = '1';
      break;

    case 'GetUserPhoto':
      sendMessage.Message.Request = Request;
      sendMessage.Message.UserID = '1';
      break;

    case 'SetUserPhoto':
      sendMessage.Message.Request = Request;
      sendMessage.Message.UserID = '1';
      sendMessage.Message.PhotoSize = 1122;
      sendMessage.Message.PhotoData = 'in base64 format';
      break;

    case 'EnrollFaceByPhoto':
      sendMessage.Message = {
        Request,
        ...body,
      };
      // sendMessage.Message.Request = Request;
      // sendMessage.Message.UserID = '1';
      // sendMessage.Message.PhotoSize = 1122;
      // sendMessage.Message.PhotoData = 'in base64 format';
      break;

    case 'GetFaceData':
      sendMessage.Message.Request = Request;
      sendMessage.Message.UserID = '1';
      break;

    case 'SetFaceData':
      sendMessage.Message.Request = Request;
      sendMessage.Message.UserID = '1';
      sendMessage.Message.Privilege = 'user/manager/admin';
      sendMessage.Message.DuplicationCheck = 'Yes';
      sendMessage.Message.FaceData = 'in base64 format';
      break;

    case 'RemoteEnroll':
      sendMessage.Message.Request = Request;
      sendMessage.Message.Backup = 'RemoteEnrollFace/RemoteEnrollFP/RemoteEnrollCard';
      break;

    case 'ExitRemoteEnroll':
      sendMessage.Message.Request = Request;
      break;

    case 'GetDepartment':
      sendMessage.Message.Request = Request;
      sendMessage.Message.DeptNo = '0~29';
      break;

    case 'SetDepartment':
      sendMessage.Message.Request = Request;
      sendMessage.Message.DeptNo = '0~29';
      sendMessage.Message.Data = 'in base64 format';
      break;

    case 'GetProxyDept':
      sendMessage.Message.Request = Request;
      sendMessage.Message.ProxyNo = '0~29';
      break;

    case 'SetProxyDept':
      sendMessage.Message.Request = Request;
      sendMessage.Message.ProxyNo = '0~29';
      sendMessage.Message.Data = 'in base64 format';
      break;

    case 'GetAutoAttendance':
      sendMessage.Message.Request = Request;
      break;

    case 'SetAutoAttendance':
      sendMessage.Message.Request = Request;
      sendMessage.Message.TimeSection_0 = '0,0,0';
      sendMessage.Message.TimeSection_1 = '0,0,0';
      sendMessage.Message.TimeSection_2 = '0,0,0';
      sendMessage.Message.TimeSection_3 = '0,0,0';
      sendMessage.Message.TimeSection_4 = '0,0,0';
      sendMessage.Message.TimeSection_5 = '0,0,0';
      sendMessage.Message.TimeSection_6 = '0,0,0';
      sendMessage.Message.TimeSection_7 = '0,0,0';
      sendMessage.Message.TimeSection_8 = '0,0,0';
      sendMessage.Message.TimeSection_9 = '0,0,0';
      break;

    case 'GetAccessTimeZone':
      sendMessage.Message.Request = Request;
      sendMessage.Message.TimeZoneNo = '0~49';
      break;

    case 'SetAccessTimeZone':
      sendMessage.Message.Request = Request;
      sendMessage.Message.TimeZoneNo = '0~49';
      sendMessage.Message.TimeSection_0 = '0,0';
      sendMessage.Message.TimeSection_1 = '0,0';
      sendMessage.Message.TimeSection_2 = '0,0';
      sendMessage.Message.TimeSection_3 = '0,0';
      sendMessage.Message.TimeSection_4 = '0,0';
      sendMessage.Message.TimeSection_5 = '0,0';
      sendMessage.Message.TimeSection_6 = '0,0';
      break;

    case 'GetBellTime':
      sendMessage.Message.Request = Request;
      break;

    case 'SetBellTime':
      sendMessage.Message.Request = Request;
      sendMessage.Message.BellRingTimes = '3';
      sendMessage.Message.BellCount = '24';
      sendMessage.Message.Bell_0 = '0,0,0,0';
      sendMessage.Message.Bell_1 = '0,0,0,0';
      sendMessage.Message.Bell_2 = '0,0,0,0';
      sendMessage.Message.Bell_3 = '0,0,0,0';
      sendMessage.Message.Bell_4 = '0,0,0,0';
      // till bell 23
      break;

    case 'GetTime':
      sendMessage.Message.Request = Request;
      break;

    case 'SetTime':
      sendMessage.Message.Request = Request;
      sendMessage.Message.Time = '2013-4-11-T11:28:54Z';
      break;

    case 'GetDeviceStatus':
      sendMessage.Message.Request = Request;
      sendMessage.Message.ParamName = 'ManagerCount'; // UserCount, FaceCount, FpCount, CardCount,PwdCount, DoorStatus, AlarmStatus
      break;

    case 'GetDeviceStatusAll':
      sendMessage.Message.Request = Request;
      break;

    case 'GetDeviceInfo':
      sendMessage.Message.Request = Request;
      sendMessage.Message.ParamName = 'ManagersNumber'; // MachineID, Language, LockReleaseTime, SLogWarning, GLogWarning, ReverifyTime, Baudrate, IdentifyMode, LockMode, DoorSensorType, DoorOpenTimeout, AutoSleepTime, EventSendType, WiegandFormat, CommPassword, UseProxyInput, ProxyDlgTimeout, SoundVolume, ShowRealtimeCamera, UseFailLog, FaceEngineThreshold, FaceEngineUseAntiSpoofing, NeedWearingMask, SuggestWearingMask, UseMeasureTemperature, UseVisitorMode, ShowRealtimeTemperature, AbnormalTempDisableDoorOpen, MeasuringDurationType, MeasuringDistanceType, TemperatureUnit, AbnormalTempThreshold_Celcius, AbnormalTempThreshold_Fahrenheit
      break;

    case 'GetDeviceInfoAll':
      sendMessage.Message.Request = Request;
      break;

    case 'SetDeviceInfo':
      sendMessage.Message.Request = Request;
      sendMessage.Message.ParamName = 'ManagersNumber'; // MachineID, Language, LockReleaseTime, SLogWarning, GLogWarning, ReverifyTime, Baudrate, IdentifyMode, LockMode, DoorSensorType, DoorOpenTimeout, AutoSleepTime, EventSendType, WiegandFormat, CommPassword, UseProxyInput, ProxyDlgTimeout, SoundVolume, ShowRealtimeCamera, UseFailLog, FaceEngineThreshold, FaceEngineUseAntiSpoofing, NeedWearingMask, SuggestWearingMask, UseMeasureTemperature, UseVisitorMode, ShowRealtimeTemperature, AbnormalTempDisableDoorOpen, MeasuringDurationType, MeasuringDistanceType, TemperatureUnit, AbnormalTempThreshold_Celcius, AbnormalTempThreshold_Fahrenheit
      sendMessage.Message.Value = '1';
      break;

    case 'EnableDevice':
      sendMessage.Message.Request = Request;
      sendMessage.Message.Enable = 'Yes/No';
      break;

    case 'LockControlStatus':
      sendMessage.Message.Request = Request;
      break;

    case 'LockControl':
      sendMessage.Message.Request = Request;
      sendMessage.Message.Mode = '1~6';
      break;

    case 'TakeOffManager':
      sendMessage.Message.Request = Request;
      break;

    case 'EmptyTimeLog':
      sendMessage.Message.Request = Request;
      break;

    case 'EmptyManageLog':
      sendMessage.Message.Request = Request;
      break;

    case 'EmptyAllData':
      sendMessage.Message.Request = Request;
      break;

    case 'EmptyUserEnrollmentData':
      sendMessage.Message.Request = Request;
      break;

    case 'GetEthernetSetting':
      sendMessage.Message.Request = Request;
      break;

    case 'SetEthernet':
      sendMessage.Message.Request = Request;
      sendMessage.Message.DHCP = 'Yes/No';
      sendMessage.Message.IP = '192.xxx.xx.x';
      sendMessage.Message.Subnet = '255.xxx.xxx.x';
      sendMessage.Message.DefaultGateway = '192.xxx.x.x';
      sendMessage.Message.Port = '8080';
      break;

    case 'GetWiFiSetting':
      sendMessage.Message.Request = Request;
      break;

    case 'SetWiFi':
      sendMessage.Message.Request = Request;
      sendMessage.Message.Use = 'Yes/No';
      sendMessage.Message.SSID = 'arx_5ghz';
      sendMessage.Message.Key = 'arxmedia';
      sendMessage.Message.DHCP = 'Yes/No';
      sendMessage.Message.IP = '192.xxx.x.x';
      sendMessage.Message.Subnet = '255.xxx.xxx.x';
      sendMessage.Message.DefaultGateway = '192.xxx.x.x';
      sendMessage.Message.Port = '500';
      break;

    case 'GetDeviceInfoExt':
      sendMessage.Message.Request = Request;
      sendMessage.Message.ParamName = 'MobileNetwork'; // NTPServer, VPNServer, WebServerUrl, SendLogUrl, DeviceName, GPS
      break;

    case 'SetDeviceInfoExt':
      sendMessage.Message.Request = Request;
      sendMessage.Message.ParamName = 'MobileNetwork'; // NTPServer, VPNServer, WebServerUrl, SendLogUrl, DeviceName, GPS
      sendMessage.Message.Value = '1';
      break;

    case 'GetPowerSetting':
      sendMessage.Message.Request = Request;
      break;

    case 'SetPowerSetting':
      sendMessage.Message.Request = Request;
      sendMessage.Message.wakeup_delay = '0';
      sendMessage.Message.idle_time_for_sleep = '0';
      sendMessage.Message.RestartInfoCount = '12';
      sendMessage.Message.no_0 = '0,0,0,0';
      sendMessage.Message.no_1 = '0,0,0,0';
      sendMessage.Message.no_2 = '0,0,0,0';
      sendMessage.Message.no_3 = '0,0,0,0';
      sendMessage.Message.no_4 = '0,0,0,0';
      // till no_11
      break;

    case 'GetFirstGLog':
      sendMessage.Message.Request = Request;
      sendMessage.Message.BeginLogPos = '0';
      sendMessage.Message.UserID = 'xxx'; // optional
      sendMessage.Message.StartTime = '2013-4-11-T11:28:54Z'; // optional
      sendMessage.Message.EndTime = '2013-4-11-T11:28:54Z'; // optional
      break;

    case 'GetNextGLog':
      sendMessage.Message.Request = Request;
      sendMessage.Message.BeginLogPos = '0'; // previous + 1
      break;

    case 'GetFirmwareVersion':
      sendMessage.Message.Request = Request;
      break;

    case 'FirmwareUpgradeHttp':
      sendMessage.Message.Request = Request;
      sendMessage.Message.Size = 'xxx'; // url string length
      sendMessage.Message.Data = 'xxx'; // url string in base64
      break;

    case 'DeleteGLogWithPos':
      sendMessage.Message.Request = Request;
      sendMessage.Message.EndPos = 'xxx'; // count to delete
      break;

    case 'GetGLogPosInfo':
      sendMessage.Message.Request = Request;
      break;

    default:
      break;
  }

  if (_.get(msg, 'Event', '') === 'TimeLog_v2') {
    const foundDevice = await DeviceModel.findOne({
      serialNumber: _.get(ws, 'serialNo'),
    });
    await AccessRecordModel.create({
      premiseId: foundDevice.premiseId,
      deviceId: foundDevice._id,
      user: '65dc1055355ad86873f62e94',
      score: '100',
      trackId: _.get(msg, 'TransID'),
      department: foundDevice.departmentId,
      attributes: '{name:"andy"}',
      prediction: 'andy',
      serialNumber: _.get(ws, 'serialNo'),
      type: 'check-in',
      faceImageUrl: '',
    });
    // console.log('running transid', _.get(msg,'TransID',''))
    sendMessage.Message.Response = 'TimeLog_v2';
    sendMessage.Message.TransID = _.get(msg, 'TransID');
    sendMessage.Message.Result = 'OK';
  }

  if (_.get(msg, 'Event', '') === 'AdminLog_v2') {
    console.log('running transid', _.get(msg, 'TransID', ''));
    sendMessage.Message.Response = 'AdminLog';
    sendMessage.Message.Result = 'OK';
    sendMessage.Message.TransID = _.get(msg, 'TransID', '');
  }

  // sendMessage.Message.Request = 'SetDeviceInfo';
  // sendMessage.Message.ParamName = 'ShowRealtimeCamera';
  // sendMessage.Message.Value = 'Yes';

  if (Request !== 'Login') {
    const xmlContent = builder.build(sendMessage);
    console.log('xml content:', xmlContent);
    ws.send(xmlContent);
  }
};

module.exports = {
 sockets, sendToDevice, wsSwitch, addFace, deleteFace,
};
