const mongoose = require('mongoose');

const { connection } = mongoose;

const {
  databaseName, databaseHost, databaseUsername, databasePassword,
} = require('#config/env');

const uri = `mongodb+srv://${databaseUsername}:${databasePassword}@${databaseHost}/${databaseName}?replicaSet=atlas-jpftuw-shard-0&retryWrites=true&w=majority`;

mongoose.set('strictQuery', true);
mongoose.connect(uri);

connection.on('connected', () => {
  console.log(`Mongoose connected to ${databaseHost}`);
});

connection.on('disconnected', () => {
  console.log(`Mongoose connected to ${databaseHost}`);
});

connection.on('error', (err) => {
  console.log(`Mongoose connection error ${err}`);
});

process.on('SIGINT', () => {
  mongoose.connection.close();
  process.exit(0);
});
