const DeviceService = require('#services/device.service');
require('#config/mongodb');

const runSetDeviceSettings = async () => {
  const test = await DeviceService.setDeviceSettingsV2({
    serialNumber: 'QV3001',
    rcAttributeAndOcclusionMode: 1,
    searchThreshold: 69,
    livenessThreshold: 55,
    livenessEnabled: false,
    rgbIrLivenessEnabled: false,
    poseThresholdRoll: 35,
    poseThresholdPitch: 35,
    poseThresholdYaw: 35,
    blurThreshold: 0.8,
    lowBrightnessThreshold: 30,
    highBrightnessThreshold: 210,
    brightnessSTDThreshold: 80,
    faceMinThreshold: 100,
    retryCount: 2,
    smileEnabled: false,
    maxFaceEnabled: true,
    FacePoseThresholdPitch: 35,
    FacePoseThresholdRoll: 35,
    FacePoseThresholdYaw: 35,
    FaceBlurThreshold: 0.7,
    FaceLowBrightnessThreshold: 70,
    FaceHighBrightnessThreshold: 220,
    FaceBrightnessSTDThreshold: 60,
    FaceFaceMinThreshold: 100,
    FaceRcAttributeAndOcclusionMode: 2,
  }, '********************************');

  console.log('set device settings:', test);
};

runSetDeviceSettings();
