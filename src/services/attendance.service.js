const AccessModel = require('#models/Access.model');
const AccessRecordModel = require('#models/AccessRecord.model');
const DeviceUsersModel = require('#models/DeviceUsers.model');
const _ = require('lodash');
const ExcelJS = require('exceljs');
const moment = require('moment');
const { ObjectId } = require('mongoose').Types;

exports.getAllPremiseRawScans = async (params) => {
  const data = [];
  const {
    startTime,
    endTime,
    premiseId,
    userId,
  } = params;

  const query = {
    premiseId,
    createdAt: {
      $gte: moment(startTime).toISOString(),
      $lte: moment(endTime).toISOString(),
    },
  };

  const listOfScans = await AccessRecordModel.find(query)
    .populate('user')
    .populate('premiseId', '-departments -theme -apiKey -childPremises -employees -public')
    .lean();

  return listOfScans;
};

exports.getAttendanceRecords = async (params) => {
  const {
    page = 0,
    limit = 10,
    startTime,
    endTime,
    premiseId,
    userId,
  } = params;
  let query = null;

  if (!_.isEmpty(userId)) {
    query = {
      premiseId,
      user: userId,
      createdAt: {
        $gte: moment(startTime).toISOString(),
        $lte: moment(endTime).toISOString(),
      },
    };
  } else {
    query = {
      premiseId,
      createdAt: {
        $gte: moment(startTime).toISOString(),
        $lte: moment(endTime).toISOString(),
      },
    };
  }

  // get all access records.
  const selectedAttendanceRecords = await AccessRecordModel.find(query)
    .populate('user')
    .populate('premiseId', '-departments -theme -apiKey -childPremises -employees -public')
    .lean();
  const groupByUser = _.groupBy(selectedAttendanceRecords, 'user._id');
  const userAttendanceGroupByOrder = await Promise.all(_.map(groupByUser, async (userAttendances, index) => {
    if (ObjectId.isValid(index) == true) {
      const groupedAttendances = _.groupBy(userAttendances, (att) => moment(att.createdAt).format('YYYY-MM-DD'));
      const user = userAttendances[0]?.user;
      const formatted = await Promise.all(_.map(groupedAttendances, async (attendances, date) => {
        const foundLunchInAttendances = _.filter(attendances, { type: 'lunchIn' });
        const foundLunchOutAttendances = _.filter(attendances, { type: 'lunchOut' });

        // lunch in and out.
        let lunchInTime = null;
        let lunchOutTime = null;
        if (!_.isEmpty(foundLunchInAttendances)) {
          lunchInTime = moment.min(_.map(foundLunchInAttendances, (attendance) => moment(attendance.createdAt)));
        }
        if (!_.isEmpty(foundLunchOutAttendances)) {
          lunchOutTime = moment.max(_.map(foundLunchOutAttendances, (attendance) => moment(attendance.createdAt)));
        }

        const userAccess = await AccessModel.findOne({ uid: index }).lean();

        const checkInTime = moment.min(_.map(attendances, (attendance) => moment(attendance.createdAt)));
        const checkOutTime = moment.max(_.map(attendances, (attendance) => moment(attendance.createdAt)));
        let earlierIn = null;
        let earlierOut = null;
        let lateIn = null;
        let lateOut = null;
        if (!_.isEmpty(userAccess?.startWorkingTime?.toISOString())) {
          const shiftStartTime = moment(userAccess.startWorkingTime.toISOString()).format('HH:mm:ss');
          const startWorkingTime = checkInTime.format('HH:mm:ss');
          const difference = moment(`1990-01-01T${startWorkingTime}`).diff(moment(`1990-01-01T${shiftStartTime}`), 'milliseconds');
          const seconds = difference / 1000;
          const minutes = seconds / 60;
          const hours = minutes / 60;

          const finalSeconds = seconds % 60;
          const finalMinutes = minutes % 60;

          if (difference > 0) {
            lateIn = `${Math.floor(hours)}:${Math.floor(finalMinutes)}:${finalSeconds}`;
          }
          if (difference < 0) {
            earlierIn = `${Math.floor(hours * -1)}:${Math.floor(finalMinutes) * -1}:${finalSeconds * -1}`;
          }
        }
        if (!_.isEmpty(userAccess?.endWorkingTime?.toISOString())) {
          const shiftEndTime = moment(userAccess.endWorkingTime.toISOString()).format('HH:mm:ss');
          const endWorkingTime = checkOutTime.format('HH:mm:ss');
          const difference = moment(`1990-01-01T${endWorkingTime}`).diff(moment(`1990-01-01T${shiftEndTime}`), 'milliseconds');
          const seconds = difference / 1000;
          const minutes = seconds / 60;
          const hours = minutes / 60;

          const finalSeconds = seconds % 60;
          const finalMinutes = minutes % 60;

          if (difference > 0) {
            lateOut = `${Math.floor(hours)}:${Math.floor(finalMinutes)}:${finalSeconds}`;
          }
          if (difference < 0) {
            earlierOut = `${Math.floor(hours * -1)}:${Math.floor(finalMinutes) * -1}:${finalSeconds * -1}`;
          }
          console.log('end difference:', `${Math.floor(hours)}:${Math.floor(finalMinutes)}:${finalSeconds}`);
        }
        // console.log('late in :', lateIn);
        return {
          date,
          lateIn,
          lateOut,
          earlierIn,
          earlierOut,
          lunchInTime,
          lunchOutTime,
          checkInTime,
          checkOutTime,
        // attendances: _.map(attendances, (attendance) => {
        //   _.unset(attendance, 'user');
        //   return attendance;
        // }),
        };
      }));
      return {
        user,
        userAttendances: formatted,
      };
    }
  }));
  return _.compact(userAttendanceGroupByOrder);
};

exports.excelAttendanceCheckInCheckOut = async (params) => {
  const {
    attendanceData, startUnix, endUnix, premiseFullName,
  } = params;

  const workbook = new ExcelJS.Workbook();
  const newdata = _.groupBy(attendanceData, 'name');
  _.map(newdata, (data) => {
    const worksheet = workbook.addWorksheet(data[0].name, {
      pageSetup: { paperSize: 9, orientation: 'landscape' },
    });
    /* Column headers */
    worksheet.getRow(5).values = [
      '',
      'Date',
      'Name',
      'Check In',
      'Lunch Out',
      'Lunch In',
      'Check Out',
      'First Scan',
      'Last Scan',
      'Late In',
      'Late Out',
      'Early In',
      'Early Out',

      'Hours',
    ];

    /* Define your column keys because this is what you use to insert your data according to your columns, they're column A, B, C, D respectively being idClient, Name, Tel, and Adresse.
    So, it's pretty straight forward */
    worksheet.columns = [
      { key: 'index', width: 5 },
      { key: 'date', width: 15, alignment: { vertical: 'middle' } },
      { key: 'name', width: 15, alignment: { vertical: 'middle' } },
      { key: 'checkIn', width: 15, alignment: { vertical: 'middle', horizontal: 'right' } },
      { key: 'lunchOut', width: 15, alignment: { vertical: 'middle', horizontal: 'center' } },
      { key: 'lunchIn', width: 15, alignment: { vertical: 'middle', horizontal: 'center' } },
      { key: 'checkOut', width: 15, alignment: { vertical: 'middle', horizontal: 'right' } },
      { key: 'firstScan', width: 15, alignment: { vertical: 'middle', horizontal: 'center' } },
      { key: 'lastScan', width: 15, alignment: { vertical: 'middle', horizontal: 'center' } },
      { key: 'lateIn', width: 15, alignment: { vertical: 'middle', horizontal: 'center' } },
      { key: 'lateOut', width: 15, alignment: { vertical: 'middle', horizontal: 'right' } },
      { key: 'earlyIn', width: 15, alignment: { vertical: 'middle', horizontal: 'center' } },
      { key: 'earlyOut', width: 15, alignment: { vertical: 'middle', horizontal: 'center' } },
      { key: 'hourCount', width: 15, alignment: { vertical: 'middle', horizontal: 'center' } },
    ];
    // const idCol = worksheet.getColumn('id');
    // const nameCol = worksheet.getColumn('B');
    const ageCol = worksheet.getColumn(3); // index starts from 1
    ageCol.eachCell({ includeEmpty: true }, (cell, rowNumber) => {
      // console.log(cell.value);
    });

    // worksheet.addRow({ id: 1, name: 'John Doe', age: 35 });
    // or
    // worksheet.addRow([2, 'Mary Sue', 22]);

    // worksheet.addRows(rows);
    _.forEach(data, (row, index) => {
      _.set(row, 'index', index + 1);
      worksheet.addRow(row);
      // console.log(worksheet.getRow())
    });

    worksheet.eachRow((row, number) => {
      row.eachCell((cell, colNumber) => {
        cell.alignment = { vertical: 'middle' };
        if (_.includes([5, 6, 8, 9, 10, 11, 12, 13, 14], colNumber)) {
          cell.alignment = { wrapText: 'true', horizontal: 'center', vertical: 'middle' };
        }
        if (_.includes([4, 7], colNumber)) {
          cell.alignment = { horizontal: 'right', vertical: 'middle' };
        }
        if (number > 4) {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        }
      });
    });

    worksheet.getCell('B2').value = 'COMPANY';
    worksheet.getCell('B3').value = 'FROM';
    worksheet.getCell('B4').value = 'TO';

    _.map(['B2', 'B3', 'B4'], (key) => {
      worksheet.getCell(key).style = { font: { bold: true } };
      worksheet.getCell(key).alignment = { vertical: 'bottom', horizontal: 'right' };
    });

    const startDate = moment.unix(startUnix).startOf('day').format('DD/MM/YYYY');
    const endDate = moment.unix(endUnix).endOf('day').format('DD/MM/YYYY');
    worksheet.getCell('C2').value = premiseFullName;
    worksheet.getCell('C3').value = startDate;
    worksheet.getCell('C3').alignment = { vertical: 'bottom', horizontal: 'right' };
    worksheet.getCell('C4').value = endDate;
    worksheet.getCell('C4').alignment = { vertical: 'bottom', horizontal: 'right' };
    worksheet.mergeCells('A1:I1');
    worksheet.getCell('A1').value = 'Attendance Worksheet';
    worksheet.getCell('A1').alignment = { vertical: 'bottom', horizontal: 'center' };
  });

  // iterate over each cell
  // row.eachCell(function(cell, colNumber) {
  // });

  // worksheet.getCell('A1').alignment = { vertical: 'top', horizontal: 'left' };

  // BORDER
  // worksheet.getCell('A1').border = {
  // 	top: {style:'double', color: {argb:'FF00FF00'}},
  // 	left: {style:'double', color: {argb:'FF00FF00'}},
  // 	bottom: {style:'double', color: {argb:'FF00FF00'}},
  // 	right: {style:'double', color: {argb:'FF00FF00'}}
  // };

  // FILL
  // worksheet.getCell('A1').fill = {
  // 	type: 'pattern',
  // 	pattern: 'darkTrellis',
  // 	// thin
  // 	// dotted
  // 	// dashDot
  // 	// hair
  // 	// dashDotDot
  // 	// slantDashDot
  // 	// mediumDashed
  // 	// mediumDashDotDot
  // 	// mediumDashDot
  // 	// medium
  // 	// double
  // 	// thick
  // 	fgColor: { argb: 'FFFFFF00' },
  // 	bgColor: { argb: 'FF0000FF' }
  // };
  return workbook;
};
