const { uploadImage } = require('#helpers/index');
const PremiseModel = require('#models/Premise.model');
const { auth } = require('#config/firebase');
const AdminModel = require('#models/Admin.model');
const { Schema, Types, default: mongoose } = require('mongoose');
const responses = require('#helpers/responses');
const { v4: uuidv4 } = require('uuid');

const { readFileSync } = require('fs');
const _ = require('lodash');
const limax = require('limax');
const AccessModel = require('#models/Access.model');
const { ObjectId } = require('mongodb');
const DeviceModel = require('#models/Device.model');
const DeviceUsersModel = require('#models/DeviceUsers.model');
const { default: ShortUniqueId } = require('short-unique-id');
const AdminApikeyModel = require('#models/AdminApikey.model');

const uid = new ShortUniqueId({ length: 8 });
uid.setDictionary('alphanum_upper');

exports.createPremise = async (params) => {
	const {
		premiseLogoImage,
		mainColor,
		textColor,
		departments,
		name,
	} = params;

	// upload logo
	const imageBuffer = readFileSync(_.get(premiseLogoImage, 'path', ''));
	const faceImageUrl = await uploadImage(imageBuffer, 'premises');
	// const apiKey = _.replace(uuidv4().toUpperCase(), /-/g, '');
	const apiKey = uid();

	// add data to database
	return PremiseModel.create({
		departments,
		theme: {
			premiseLogoUrl: faceImageUrl,
			mainColor,
			textColor,
		},
		name,
		premiseId: limax(name),
		public: true,
		apiKey,
	});
};

exports.createPremiseApiAccount = async (params) => {
	const { premiseId, name, _id } = params;
	console.log('params in createPremiseApiAccount', params);
	const adminApiKey = uid();

	const POSITION = 'admin';

	return AdminApikeyModel.create({
		apiKey: adminApiKey,
		projectName: name,
		premises: [{
			premiseId: _id,
			position: POSITION,
		}],
	});
};

exports.getPremiseByAPI = async (apiKey) => {
	const premise = await PremiseModel.findOne({ apiKey }).select(['-departments', '-public', '-childPremises', '-createdAt', '-updatedAt', '-employees']);
	if (_.isEmpty(premise)) {
		return [];
	}
	return premise;
};

exports.addAdminToPremise = async (params) => {
	const { email, premiseId } = params;

	return AdminModel.findOneAndUpdate({
		email,
		'premises.premiseId': {
			$ne: premiseId,
		},
	}, {
		$push: { premises: { premiseId } },
		type: 'premise-admin',
	}, { new: true });
};

exports.addPremiseChildren = async (params) => {
	const {
		name,
		mainColor,
		textColor,
		departments,
		parentPremiseId,
		premiseLogoImage,
	} = params;

	// check if parent premise existed.
	const parentPremise = await PremiseModel.findById(parentPremiseId);
	if (_.isEmpty(parentPremise)) {
		throw responses.failure('premise does not exist.');
	}

	// upload logo
	const imageBuffer = readFileSync(_.get(premiseLogoImage, 'path', ''));
	const faceImageUrl = await uploadImage(imageBuffer, 'faces');

	// start mongoose transaction
	const session = await mongoose.startSession();
	session.startTransaction();

	// add premise to database
	const childPremise = await PremiseModel.create([{
		premiseId: limax(name),
		public: true,
		departments,
		name,
		theme: {
			premiseLogoUrl: faceImageUrl,
			premiseId: limax(name),

			mainColor,
			textColor,
		},
	}], { session });

	// add children to premise.
	const childPremiseId = _.get(childPremise[0], '_id', '').toString();
	const addedChildrenPremise = await PremiseModel.findOneAndUpdate({
		_id: parentPremiseId,
		childPremises: {
			$ne: childPremiseId,
		},
	}, {
		$push: {
			childPremises: childPremiseId,
		},
	}, { session });

	if (_.isEmpty(addedChildrenPremise)) {
		await session.abortTransaction();
		await session.endSession();
	} else {
		await session.commitTransaction();
		await session.endSession();
	}

	return addedChildrenPremise;
};

exports.createPremiseExternal = async (params) => {
	const {
		name,
		mainColor,
		textColor,
		departments,
		premiseLogoImage,
		apiKey,
	} = params;

	// upload logo
	const imageBuffer = readFileSync(_.get(premiseLogoImage, 'path', ''));
	const faceImageUrl = await uploadImage(imageBuffer, 'premises');
	const premiseApiKey = uid();

	// start mongoose transaction
	const session = await mongoose.startSession();
	session.startTransaction();

	// check if the premise id is the same with the other premise under the same admin.
	const admin = await AdminApikeyModel.findOne({
		apiKey,
	}).populate('premises.premiseId').session(session);
	const adminPremises = admin.premises;
	console.log('adminPremises', adminPremises);

	// check if the name under the same admin is having the same name
	_.map(adminPremises, (adminPremise) => {
		if (adminPremise.premiseId.name === name && !adminPremise.premiseId.isDeleted) {
			throw responses.failure('The premise with the same name existed!');
		}
	});

	// add premise to database
	const childPremise = await PremiseModel.create([{
		premiseId: limax(name),
		public: true,
		departments,
		name,
		theme: {
			premiseLogoUrl: faceImageUrl,
			premiseId: limax(name),
			public: true,
			mainColor,
			textColor,
		},
		apiKey: premiseApiKey,
	}], { session });

	// add children to premise.
	const childPremiseId = _.get(childPremise[0], '_id', '').toString();
	const addedChildrenPremise = await AdminApikeyModel.findOneAndUpdate({
		apiKey,
	}, {
		$push: {
			premises: {
				premiseId: childPremiseId,
				position: 'admin',
			},
		},
	}, { session });

	if (_.isEmpty(addedChildrenPremise)) {
		await session.abortTransaction();
		await session.endSession();

		return responses.failure('children is empty');
	}
	await session.commitTransaction();
	await session.endSession();

	return {
		premiseId: childPremiseId,
		apiKey: _.get(childPremise[0], 'apiKey'),
	};
};

exports.updatePremiseExternal = async (params, facepassServer) => {
	const {
		premiseId,
		name,
		mainColor,
		textColor,
		departments,
		premiseLogoImage,
		apiKey,
	} = params;

	console.log('params', params);

	// start mongoose transaction
	const session = await mongoose.startSession();
	session.startTransaction();

	// check if the premise exists
	const premise = await PremiseModel.findOne({ _id: premiseId }).session(session);
	if (!premise) {
		await session.abortTransaction();
		await session.endSession();
		throw responses.failure('Premise does not exist.');
	}

	if (premise.isDeleted) {
		await session.abortTransaction();
		await session.endSession();
		throw responses.failure('The premise does not exist!');
	}

	// upload logo if provided
	let faceImageUrl;
	if (premiseLogoImage) {
		const imageBuffer = readFileSync(_.get(premiseLogoImage, 'path', ''));
		faceImageUrl = await uploadImage(imageBuffer, 'premises');
	}

	// check if the name under the same admin is having the same name
	const admin = await AdminApikeyModel.findOne({ apiKey }).populate('premises.premiseId').session(session);
	const adminPremises = admin.premises;

	// update premise in the database
	const updatedPremise = await PremiseModel.findOneAndUpdate(
		{ _id: premiseId },
		{
			name,
			theme: {
				mainColor,
				textColor,
				...(faceImageUrl && {
					premiseLogoUrl: faceImageUrl,
				}),
			},

		},
		{ new: true, session },
	);

	console.log('updated premise:', faceImageUrl);

	if (!updatedPremise) {
		await session.abortTransaction();
		await session.endSession();
		throw responses.failure('Failed to update premise.');
	}

	// send premise data to the app itself too.
	await facepassServer.emit(premiseId, 'updatePremise', {
		name: updatedPremise.name,
		theme: updatedPremise.theme,
	});

	await session.commitTransaction();
	await session.endSession();

	return {
		premiseId: updatedPremise.premiseId,
		apiKey: updatedPremise.apiKey,
	};
};

exports.getUsersByPremiseId = async (premiseId, serialNumber) => {
	const users = await AccessModel.aggregate([
		{
			$match: {
				isDeleted: false,
			},
		},
		{
			$lookup: {
				from: 'premises',
				localField: 'premiseId',
				foreignField: '_id',
				as: 'premise',
			},
		},
		{ $unwind: '$premise' },
		{
			$lookup: {
				from: 'users',
				localField: 'user',
				foreignField: '_id',
				as: 'user',
			},
		},
		{ $unwind: '$user' },
		{
			$match: {
				'premise._id': mongoose.Types.ObjectId(premiseId),
			},
		},
	]);

	const finalUsers = _.map(users, (user) => user.user);

	return finalUsers;
};
exports.getUsersByPremiseIdAndDepartmentId = async (premiseId, serialNumber) => {
	const device = await DeviceModel.findOne({
		serialNumber,
	}).lean();

	const users = await AccessModel.aggregate([
		{
			$match: {
				isDeleted: false,
			},
		},
		{
			$lookup: {
				from: 'premises',
				localField: 'premiseId',
				foreignField: '_id',
				as: 'premise',
			},
		},
		{ $unwind: '$premise' },
		{
			$lookup: {
				from: 'users',
				localField: 'uid',
				foreignField: '_id',
				as: 'user',
			},
		},
		{ $unwind: '$user' },
		{
			$match: {
				'premise._id': mongoose.Types.ObjectId(premiseId),
				'departments._id': mongoose.Types.ObjectId(device?.departmentId),
				isDeleted: false,
			},
		},
	]);

	const deviceUsers = await DeviceUsersModel.findOne({
		device: device?._id,
		premiseId,
	});

	// get linked users.
	const finalUsers = _.map(users, (user) => {
		const foundUser = _.find(deviceUsers?.users, { user: user.user._id });
		return {
			...user.user,
			faceToken: foundUser?.faceToken,
		};
	});

	// console.log('sync user final users:', finalUsers);
	return finalUsers;
};

exports.updatePremise = async (params, user) => {
	const
		{
			premiseId,
			premiseLogoImage,
			mainColor,
			textColor,
		} = params;

	// validate whether admin is granted access to the premise.
	const premise = await PremiseModel.findOne({ premiseId }).lean();
	const foundAdmin = await AdminModel.findOne({
		'premises.premiseId': premise._id,
	});
	if (_.isEmpty(foundAdmin)) {
		throw responses.failure('This admin has no access to this premise');
	}

	// upload logo
	if (!_.isEmpty(premiseLogoImage)) {
		const imageBuffer = readFileSync(_.get(premiseLogoImage, 'path', ''));
		const faceImageUrl = await uploadImage(imageBuffer, 'premises');
		_.set(params, 'premiseLogoUrl', faceImageUrl);
	}

	// update Premise
	await PremiseModel.findOneAndUpdate(
		{
			premiseId,
		},
		{
			$set: {
				theme: {
					...premise.theme,
					...params,
				},
			},
		},
	);

	return 'Premise updated successfully!';
};

exports.getAllPremises = async (params, admin) => {
	const { } = params;

	const foundAdmin = await AdminModel.findOne({
		uid: admin.uid,
	}).populate('premises.premiseId').lean();

	let tempPremise = null;
	const premises = _.map(foundAdmin.premises, (premise) => {
		tempPremise = _.cloneDeep(premise);
		_.set(tempPremise, 'premise', tempPremise.premiseId);
		_.unset(tempPremise, 'premiseId');
		return tempPremise;
	});

	return premises;
};

exports.getAllPremisesByAPIKey = async (params) => {
	const {
		apiKey, q, limit = 5, page = 1, showDeleted = false,
	} = params;

	// inject or operator based on data type
	const or = [];
	if (mongoose.Types.ObjectId.isValid(q)) {
		or.push({
			_id: q,
		});
	}
	if (!_.isEmpty(q)) {
		or.push({ premiseId: { $regex: q, $options: 'i' } });
	}

	// check if the premises are under the admin
	const adminPremises = await AdminApikeyModel.findOne({
		apiKey,
	}).select('-apiKey').lean();
	console.log('admin premises:', adminPremises.premises);

	// find the premises based on the query given.
	const query = {
		_id: { $in: _.map(adminPremises?.premises, (premise) => premise.premiseId) },
		isDeleted: false,
	};
	if (!_.isEmpty(or)) {
		query.$or = or;
	}
	if (showDeleted) {
		query.isDeleted = true;
	}
	console.log('query:', page);
	const premises = await PremiseModel.find(query).select('-departments').skip((page - 1) * limit).limit(limit);
	const premisesCount = await PremiseModel.count(query);

	console.log('premises:', premises);
	console.log('premises:', premisesCount);

	return {
		premises: [...premises],
		pagination: {
			totalPage: Math.ceil(premisesCount / limit),
			itemsPerPage: limit,
			totalItems: premisesCount,
			previousPageUrl: '',
			nextPageUrl: '',
		},
	};
};

exports.updatePremiseCallbackURL = async (params) => {
	const { callbackUrl, premiseId } = params;
	const updatedCallbackUrl = await PremiseModel.findOneAndUpdate({
		_id: premiseId,
	}, {
		callbackUrl,
	}, {
		new: true,
	});

	return updatedCallbackUrl;
};

exports.deletePremiseExternal = async (params) => {
	const { apiKey, premiseId } = params;

	// validate access
	const validatedApi = await AdminApikeyModel.findOne({ apiKey }).lean();
	if (!validatedApi) {
		throw new Error('You have no access to use this api');
	}

	const foundPremise = await PremiseModel.findOne({
		_id: premiseId,
		isDeleted: true,
	});

	if (foundPremise) {
		throw new Error('Premise does not exist!');
	}

	const foundAdmin = await AdminApikeyModel.findOne({ apiKey, 'premises.premiseId': premiseId });
	if (foundAdmin) {
		await PremiseModel.findOneAndUpdate({
			_id: premiseId,
		}, {
			isDeleted: true,
		});

		return 'Premise deleted successfully!';
	}

	throw new Error('Premise does not belong to this account, kindly try with another one.');
};

exports.getPremiseExternalByPremiseId = async (params) => {
	const { premiseId, apiKey } = params;

	// inject or operator based on data type
	const or = [];
	if (mongoose.Types.ObjectId.isValid(premiseId)) {
		or.push({
			_id: premiseId,
		});
	} else {
		or.push({ premiseId: { $regex: premiseId, $options: 'i' } });
	}

	// validate if the premise is under the admin
	const foundAdminPremises = await AdminApikeyModel.find({ apiKey }).lean();

	if (foundAdminPremises) {
		const foundPremise = await PremiseModel.findOne({
			$or: or,
		}).select('-departments');
		return foundPremise;
	}

	if (!foundAdminPremises) {
		const premises = await PremiseModel.findOne({
			$or: or,
		});

		const fPremise = _.find(premises, (premise) => {
			const foundPremise = _.includes(foundAdminPremises.premises, { premiseId: premise._id });
			return foundPremise;
		});

		return fPremise;
	}

	throw new Error('Premise does not belong to this account');
};
