const PremiseModel = require('#models/Premise.model');
const UserModel = require('#models/User.model');
const _ = require('lodash');
const responses = require('#helpers/responses');
const { startSession, Schema } = require('mongoose');
const AccessModel = require('#models/Access.model');
const EmployeeModel = require('#models/Employee.model');
const AdminModel = require('#models/Admin.model');
const { uploadImage } = require('#helpers/index');
const { readFileSync } = require('fs');
const { v4: uuid } = require('uuid');
const moment = require('moment');

const { default: mongoose } = require('mongoose');
const { ObjectId } = require('mongodb');
const AccessRecordModel = require('#models/AccessRecord.model');
const DeviceUsersModel = require('#models/DeviceUsers.model');
const AdminApikeyModel = require('#models/AdminApikey.model');
const { uploadToGCS } = require('#helpers/uploadData');
const ChangeLogsModel = require('#models/ChangeLogs.model');
const ExcelJS = require('exceljs');
const { default: axios } = require('axios');
const readXlsxFile = require('read-excel-file/node');

exports.addEmployeeToPremise = async (params) => {
  const { uid, premiseId } = params;

  const session = await startSession();
  session.startTransaction();

  try {
    // check if user does exist in the premise.
    const premise = await PremiseModel.findOne({ premiseId }).session(session);
    console.log(premise._id);
    const user = await AccessModel.find({
      uid,
      premiseId: premise._id,
      isDeleted: false,
    }).populate('uid').session(session);
    if (_.isEmpty(user)) {
      throw responses.failure('User not found in the premise');
    }

    const addedEmployee = user[0].uid._id.toString();
    const foundUser = await PremiseModel.findOne({
      premiseId,
      employees: {
        $in: [addedEmployee],
      },
    }).session(session);

    // if the employee is in the premise
    if (!_.isEmpty(foundUser)) {
      throw responses.failure('The employee is already existed in the premise.');
    }

    // if the employee is not in the premise
    const addedToPremise = await PremiseModel.findOneAndUpdate(
      {
        premiseId,
        employees: {
          $nin: [addedEmployee],
        },
      },
      {
        $push: {
          employees: addedEmployee,
        },
      },
      {
        session,
        new: true,
      },
    );

    console.log('added to premise:', addedToPremise);
    if (_.isEmpty(addedToPremise)) {
      throw responses.failure('premise does not exist.');
    } else {
      await session.commitTransaction();
      return addedToPremise;
    }
  } catch (err) {
    console.error(err);
    await session.abortTransaction();
    throw responses.failure('error adding employee', err);
  } finally {
    await session.endSession();
  }
};

exports.createEmployee = (params, admin) => async (facepassServer) => {
  const { premiseId } = params;

  const imageBuffer = readFileSync(_.get(params, 'imageObj.path', ''));
  const faceImageUrl = await uploadImage(imageBuffer, 'faces');
  // check if premise is under the admin.
  const foundAdmin = await AdminModel.findOne({
    uid: admin.uid,
  }).lean();
  const foundPremise = _.find(foundAdmin.premises, (premise) => { if (premise.premiseId.toString() === premiseId) return premise; });
  // console.log('found Premise', foundPremise);
  if (_.isEmpty(foundPremise)) {
    throw responses.failure('You have no access to this premise.');
  }
  const premise = await PremiseModel.findOne({ _id: premiseId });
  console.log('premise:', premise);
  const uid = uuid();
  const adminId = _.get(foundAdmin, 'type');
  const session = await startSession();
  session.startTransaction();
  try {
    const user = await UserModel.create([{
      phoneNumber: _.get(params, 'phoneNumber'),
      fullName: _.get(params, 'fullName'),
      faceImageUrl,
      createdBy: _.get(foundAdmin, 'email'),
      from: adminId,
      NRIC: _.get(params, 'NRIC'),
      passportNumber: _.get(params, 'passportNumber'),
      gender: _.get(params, 'gender'),
      uid,
    }], { session });

    if (_.isEmpty(user)) {
      throw 'Fail to create user';
    }
    const access = await AccessModel.create([{
      uid: new ObjectId(_.get(user, '[0]._id')),
      premiseId: new ObjectId(premiseId),
      isDeleted: false,
      accessStartTime: _.get(params, 'accessStartTime'),
      accessEndTime: _.get(params, 'accessEndTime'),
      startWorkingTime: moment().set({ h: 8, minute: 30 }).set('minutes', 0),
      endWorkingTime: moment().set({ h: 17, minute: 30 }).set('minutes', 0),
      departments: [{
        _id: mongoose.Types.ObjectId(premise.departments[0]._id.toString()),
        departmentId: _.get(params, 'department'),
        role: _.get(params, 'role'),
        access: true,
        type: 'toggle',
      }],
    }], { session });
    if (_.isEmpty(access)) {
      throw 'Fail to create user access';
    }
    const employee = await EmployeeModel.create([{
      user: _.get(user, '[0]._id', ''),
      KWSP: _.get(params, 'KWSP'),
      SOCSO: _.get(params, 'SOCSO'),
      religion: _.get(params, 'religion'),
      birthDate: _.get(params, 'birthDate'),
      race: _.get(params, 'race'),
      maritalStatus: _.get(params, 'maritalStatus'),
      nationality: _.get(params, 'nationality'),
      designation: _.get(params, 'designation'),
      department: _.get(params, 'department'),
      emergencyContact: _.get(params, 'emergencyContact'),
    }], { session });
    if (_.isEmpty(employee)) {
      throw 'Fail to create employee information';
    }
    const foundUser = {
      fullName: _.get(user, '[0].fullName', ''),
      faceImageUrl: _.get(user, '[0].faceImageUrl', ''),
      isDeleted: 'false',
      _id: _.get(user, '[0]._id', ''),
      __v: 0,
    };

    // try {
    //   await facepassServer.emit(premiseId, 'addFace', foundUser);
    // } catch (err) {
    //   console.log(err);
    // }

    await session.commitTransaction();
    await session.endSession();
    return 'Create success';
  } catch (e) {
    await session.abortTransaction();
    throw `Error create employee:${e}`;
  }
};

exports.exportEmployeeListByPremiseId = async (params) => {
  const {
    premiseId,
    idArray,
  } = params;
  let match = {
    premiseId: mongoose.Types.ObjectId(premiseId),

    isDeleted: false,
  };
  if (!_.isEmpty(idArray)) {
    match = {
      premiseId: mongoose.Types.ObjectId(premiseId),
      uid: { $in: idArray },
      isDeleted: false,
    };
  }
  console.log(match);
  const usersAccesses = await AccessModel.aggregate([
    {
      $match: match,

    },
    {
      $lookup:
    {
      from: 'premises',
      localField: 'premiseId',
      foreignField: '_id',
      as: 'premises',
    },
    },
    {
      $lookup:
    {
      from: 'users',
      localField: 'uid',
      foreignField: '_id',
      as: 'users',
    },
    },

    {
      $project: {
        premise: { $arrayElemAt: ['$premises', 0] },
        user: { $arrayElemAt: ['$users', 0] },
        departments: 1,
        accessStartTime: 1,
        accessEndTime: 1,
      },
    },
    {
      $project: {
        'premise.apiKey': 0,
        'premise.theme': 0,
        'premise.childPremises': 0,
        'premise.employees': 0,
      },
    },
    {
      $match: {
        'user.isDeleted': false,
      },
    },

  ]);

  console.log('accesses:', usersAccesses);

  // populate departments data into access.
  const newUserAccesses = _.flatten(_.map(usersAccesses, (userAccess) => {
    return _.map(
      userAccess.departments,
      (userDepartment) => {
        const foundDepartment = _.find(
          userAccess.premise.departments,
          { _id: userDepartment._id },
        );
        userAccess.user.role = userAccess.departments[0].role || '';
        userAccess.user.accessStartTime = userAccess.accessStartTime || '';
        userAccess.user.accessEndTime = userAccess.accessEndTime || '';

        const formattedData = {
          department: foundDepartment,
          ...userAccess,
        };
        _.unset(formattedData, 'departments');
        _.unset(formattedData, 'premise.departments');
        _.unset(formattedData, 'department.isDeleted');
        return formattedData;
      },
    );
  }));

  // combined the user access with the records.
  const employee = await Promise.all(_.map(
    newUserAccesses,
    async (userAccess) => {
      const employees = await EmployeeModel.find({
        user: userAccess.user._id,
      });
      if (!_.isEmpty(employees)) {
        return {

          ...userAccess,
          employee: { ...employees },
        };
      }
    },
  ));
  const employeeData = _.flatten(employee);
  console.log(employeeData);
  // return usersAccessRecords;
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Employee_name', {
    pageSetup: { paperSize: 9, orientation: 'landscape' },
  });
  /* Column headers */
  worksheet.getRow(1).values = [
    '',
    'Full Name',
    'Phone Number',
    'NRIC',
    'Gender',
    'Role',
    'Department',
    'Premise',
    'KWSP Number',
    'SOCSO Number',
    'Religion',
    'Birth Date',
    'Race',
    'Marital Status',
    'Nationality',
    'Designation',
    'Department',
    'Emergency Contact Name',
    'Emergency Contact Number',
    'Emergency Contact Email',
  ];
  const worksheetData = _.map(employeeData, (data) => {
    return {
      fullName: _.get(data, 'user.fullName'),
      phoneNumber: _.get(data, 'user.phoneNumber'),
      NRIC: _.get(data, 'user.NRIC'),
      gender: _.get(data, 'user.gender'),
      role: _.get(data, 'user.role'),
      department: _.get(data, 'department.departmentName'),
      premise: _.get(data, 'premise.premiseId'),
      kwsp: _.get(data, 'employee.KWSP'),
      socso: _.get(data, 'employee.SOCSO'),
      religion: _.get(data, 'employee.religion'),
      birthDate: _.get(data, 'employee.birthDate'),
      race: _.get(data, 'employee.race'),
      maritalStatus: _.get(data, 'employee.maritalStatus'),
      nationality: _.get(data, 'employee.nationality'),
      designation: _.get(params, 'designation'),

      emergencyContactName: _.get(data, 'employee.emergencyContact.name'),
      emergencyContactEmail: _.get(data, 'employee.emergencyContact.email'),
      emergencyContactPhoneNumber: _.get(data, 'employee.emergencyContact.phoneNumber'),

    };
  });
  /* Define your column keys because this is what you use to insert your data according to your columns, they're column A, B, C, D respectively being idClient, Name, Tel, and Adresse.
	So, it's pretty straight forward */
  worksheet.columns = [
    { key: 'index', width: 5 },
    { key: 'fullName', width: 15, alignment: { vertical: 'middle' } },
    { key: 'phoneNumber', width: 15, alignment: { vertical: 'middle' } },
    { key: 'NRIC', width: 15, alignment: { vertical: 'middle' } },
    { key: 'gender', width: 15, alignment: { vertical: 'middle' } },
    { key: 'role', width: 15, alignment: { vertical: 'middle' } },
    { key: 'department', width: 15, alignment: { vertical: 'middle' } },
    { key: 'premise', width: 15, alignment: { vertical: 'middle' } },
    { key: 'kwsp', width: 15, alignment: { vertical: 'middle' } },
    { key: 'socso', width: 15, alignment: { vertical: 'middle' } },
    { key: 'religion', width: 15, alignment: { vertical: 'middle' } },
    { key: 'birthDate', width: 15, alignment: { vertical: 'middle' } },
    { key: 'race', width: 15, alignment: { vertical: 'middle' } },
    { key: 'maritalStatus', width: 15, alignment: { vertical: 'middle' } },
    { key: 'nationality', width: 15, alignment: { vertical: 'middle' } },
    { key: 'designation', width: 15, alignment: { vertical: 'middle' } },
    { key: 'emergencyContactName', width: 15, alignment: { vertical: 'middle' } },
    { key: 'emergencyContactEmail', width: 15, alignment: { vertical: 'middle' } },
    { key: 'emergencyContactPhoneNumber', width: 15, alignment: { vertical: 'middle' } },
  ];
  // const idCol = worksheet.getColumn('id');
  // const nameCol = worksheet.getColumn('B');
  const ageCol = worksheet.getColumn(3); // index starts from 1
  ageCol.eachCell({ includeEmpty: true }, (cell, rowNumber) => {
    // console.log(cell.value);
  });

  _.forEach(worksheetData, (row, index) => {
    _.set(row, 'index', index + 1);
    worksheet.addRow(row);
    // console.log(worksheet.getRow())
  });

  return workbook;
};
