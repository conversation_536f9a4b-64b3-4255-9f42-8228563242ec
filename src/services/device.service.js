const DeviceModel = require('#models/Device.model');
const PremiseModel = require('#models/Premise.model');
const _ = require('lodash');
const responses = require('#helpers/responses');
const DeviceSettingsRecord = require('#models/DeviceSettingsRecord');
const moment = require('moment');
const AccessRecordModel = require('#models/AccessRecord.model');
const { ObjectId } = require('mongodb');
const { default: mongoose } = require('mongoose');
const AdminModel = require('#models/Admin.model');
const AdminApikeyModel = require('#models/AdminApikey.model');
const UserModel = require('#models/User.model');
const AccessModel = require('#models/Access.model');

// deprecated
exports.setDeviceSettings = async (params, apiKey, facepassServer) => {
  const { serialNumber } = params;
  console.log('premise:', apiKey);
  const premise = await PremiseModel.findOne({
    apiKey,
  });
  console.log('premise:', premise);
  if (_.isEmpty(premise)) {
    throw responses.failure('Premise does not exist.');
  }

  _.unset(params, 'serialNumber');

  const device = await DeviceModel.findOneAndUpdate({
    serialNumber,
    premiseId: premise._id,
  }, {
    settings: params,
    cameraSettings: params,
  }, {
    new: true,
  }).lean();
  console.log('NEWDEVICEPARAMS', params);
  console.log('NEWDEVICESETTING', device);

  try {
    await facepassServer.emitToDevice(
      premise._id.toString(),
      serialNumber,
      'updateSettings',
      {
        settings: device?.settings,
        cameraSettings: device?.cameraSettings,
      },
    );
  } catch (err) {
    console.log(err);
  }

  return device;
};

exports.getDeviceSettings = async (params) => {
  const { serialNumber, apiKey } = params;
  console.log('params', params);
  const premise = await PremiseModel.findOne({
    apiKey,
  });
  if (_.isEmpty(premise)) {
    throw responses.failure('Premise does not exist.');
  }
  const device = await DeviceSettingsRecord.findOne({
    serialNumber,
    premiseId: premise._id,
  }).sort({ createdAt: -1 }).lean();
  return {
    _id: device?._id,
    settings: device?.settings,
    cameraSettings: device?.cameraSettings,
    timestamps: device?.timestamps,
  };
};

exports.setDeviceSettingsV2 = async (params, from, facepassServer = null) => {
  const {
    serialNumber, settings, cameraSettings, premiseId,
    timestamps,
  } = params;
  const premise = await PremiseModel.findOne({
    _id: premiseId,
  }).lean();
  const deviceFound = await DeviceModel.findOne({
    serialNumber,
  });
  console.log('premise:', premise);
  console.log('from:', from);
  console.log('serial number:', serialNumber);
  console.log('timestamps:', params);
  let finalTimeStamps = timestamps;
  if (_.isEmpty(timestamps)) {
    finalTimeStamps = moment().toISOString();
  }

  if (_.isEmpty(premise)) {
    throw responses.failure('Premise does not exist.');
  }
  if (_.isEmpty(deviceFound)) {
    throw responses.failure('Device does not exist.');
  }

  const device = await DeviceSettingsRecord.create({
    premiseId: premise._id.toString(),
    serialNumber,
    settings,
    from,
    cameraSettings,
    timestamps: from === 'device' ? finalTimeStamps : moment().toISOString(),
  });
  console.log('device', device);

  if (!_.isEmpty(facepassServer)) {
    try {
      console.log('serial number:', premise._id);
      console.log('serial number:', serialNumber);
      await facepassServer.emitToDevice(
        premise._id.toString(),
        serialNumber,
        'updateSettings',
        {
          settings: device?.settings,
          cameraSettings: device?.cameraSettings,
        },
      );
    } catch (err) {
      console.log(err);
    }
  }
  return device;
};

// Chze add
exports.getAllDevices = async (params) => {
  const { premiseId, page = 1, limit = 10 } = params;
  const skip = (page - 1) * limit;
  console.log('params:', params);

  // Get total count for pagination
  const total = await DeviceModel.countDocuments({ premiseId });

  const devices = await DeviceModel.find({
    premiseId,
  })
    .populate('premiseId')
    .skip(skip)
    .limit(limit)
    .lean();

  const formattedDevices = _.map(devices, (device) => {
    const d = {
      ...device,
      premise: device.premiseId,
      department: _.find(device.premiseId.departments, { _id: device.departmentId }),
    };
    _.unset(d, 'premiseId');
    _.unset(d, 'premise.departments');
    _.unset(d, 'premise.apiKey');
    _.unset(d, 'departmentId');
    return d;
  });

  return {
    devices: formattedDevices,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
  };
};

exports.testGetAllRecords = async () => {
  const access = await AccessRecordModel.find({});
  return access;
};

exports.updateDevice = async (params, admin) => {
  const {
    name,
    departmentId,
    serialNumber,
    premiseId,
  } = params;

  console.log('params', params);

  const updateFields = {};

  if (!_.isEmpty(name)) {
    _.set(updateFields, 'name', name);
  }
  if (!_.isEmpty(departmentId)) {
    _.set(updateFields, 'departmentId', departmentId);
  }

  // validate if admin has access to the premise.
  const foundAdmin = await AdminModel.findOne({
    uid: admin.uid,
  }).populate('premises.premiseId').lean();
  const foundPremise = _.find(foundAdmin.premises, (premise) => {
    if (premise.premiseId?._id.toString() === premiseId) return premise;
  });
  if (_.isEmpty(foundPremise)) {
    throw responses.failure('You have no access to this premise.');
  }

  // validate whether the department is under the premise.
  if (!_.isEmpty(departmentId)) {
    const foundDepartment = _.find(foundPremise.premiseId.departments, { _id: mongoose.Types.ObjectId(departmentId) });
    if (_.isEmpty(foundDepartment)) {
      throw responses.failure('Department does not exist in this premise.');
    }
  }

  console.log('update fields:', updateFields);

  // update device.
  const updatedDevice = await DeviceModel.findOneAndUpdate(
    {
      serialNumber,
      premiseId,
    },
    updateFields,
    {
      new: true,
    },
  );

  if (_.isEmpty(updatedDevice)) {
    throw responses.failure('This device is not found within this premise!');
  }
  return updatedDevice;
};

exports.deleteDevice = async (params, admin) => {
  const {
    serialNumber,
    premiseId,
  } = params;

  // validate if admin has access to the premise.
  const foundAdmin = await AdminModel.findOne({
    uid: admin.uid,
  }).lean();
  const foundPremise = _.find(foundAdmin.premises, (premise) => { if (premise.premiseId.toString() === premiseId) return premise; });
  if (_.isEmpty(foundPremise)) {
    throw responses.failure('You have no access to this premise.');
  }

  // delete device
  const deletedDevice = await DeviceModel.findOneAndUpdate({
    serialNumber,
    premiseId,
  }, {
    isDeleted: true,
  }, {
    new: true,
  });

  return deletedDevice;
};

exports.rebootDevice = async (params, admin, facepassServer) => {
  const {
    serialNumber,
    premiseId,
  } = params;

  // validate if admin has access to the premise.
  const foundAdmin = await AdminModel.findOne({
    uid: admin.uid,
  }).lean();
  const foundPremise = _.find(foundAdmin.premises, (premise) => { if (premise.premiseId.toString() === premiseId) return premise; });
  // if (_.isEmpty(foundPremise)) {
  //   throw responses.failure('You have no access to this premise.');
  // }

  // validate if device is under the premise.
  const foundDevice = await DeviceModel.findOne({ serialNumber });
  if (_.isEmpty(foundDevice)) {
    throw responses.failure('Device does not exist in this premise.');
  }

  try {
    await facepassServer.emitToDevice(foundDevice.premiseId.toString(), serialNumber, 'reboot', null);
    return 'reboot successfully!';
  } catch (err) {
    console.log(err);
    throw responses.failure(err);
  }
};

exports.rebootDeviceWithApiKey = async (params, facepassServer) => {
  const {
    serialNumber,
    premiseId,
  } = params;

  // validate if admin has access to the premise.
  // const foundAdmin = await AdminModel.findOne({
  //   uid: admin.uid,
  // }).lean();
  // const foundPremise = _.find(foundAdmin.premises, (premise) => { if (premise.premiseId.toString() === premiseId) return premise; });
  // if (_.isEmpty(foundPremise)) {
  //   throw responses.failure('You have no access to this premise.');
  // }

  // validate if device is under the premise.
  const foundDevice = await DeviceModel.findOne({ serialNumber });
  if (_.isEmpty(foundDevice)) {
    throw responses.failure('Device does not exist in this premise.');
  }

  try {
    await facepassServer.emitToDevice(foundDevice.premiseId.toString(), serialNumber, 'reboot', null);
    return 'reboot successfully!';
  } catch (err) {
    console.log(err);
    throw responses.failure(err);
  }
};

exports.restartDevice = async (params, admin, facepassServer) => {
  const {
    serialNumber,
    premiseId,
  } = params;

  // validate if admin has access to the premise.
  const foundAdmin = await AdminModel.findOne({
    uid: admin.uid,
  }).lean();
  const foundPremise = _.find(foundAdmin.premises, (premise) => { if (premise.premiseId.toString() === premiseId) return premise; });
  if (_.isEmpty(foundPremise)) {
    throw responses.failure('You have no access to this premise.');
  }

  // validate if device is under the premise.
  const foundDevice = await DeviceModel.findOne({ serialNumber });
  if (_.isEmpty(foundDevice)) {
    throw responses.failure('Device does not exist in this premise.');
  }

  try {
    await facepassServer.emitToDevice(foundDevice.premiseId.toString(), serialNumber, 'restart', null);
    return 'restart successfully!';
  } catch (err) {
    console.log(err);
    throw responses.failure(err);
  }
};

exports.openDoorManual = async (params, admin, facepassServer) => {
  const {
    serialNumber,
    premiseId,
  } = params;

  // validate if device is under the premise.
  const foundDevice = await DeviceModel.findOne({ serialNumber, premiseId });
  console.log('found device', foundDevice);
  if (_.isEmpty(foundDevice)) {
    throw responses.failure('Device does not exist in this premise.');
  }

  if (_.isNil(params?.durationOpen)) {
    _.set(params, 'durationOpen', 7000);
  }
  console.log('Open door', params);
  console.log(foundDevice);
  try {
    const open = await facepassServer.emitToDevice(foundDevice.premiseId.toString(), serialNumber, 'doorOpen', { duration: 7000 });
    console.log('open', open);
    return 'opened door';
  } catch (error) {
    console.log(error);
    throw responses.failure(error);
  }
};

exports.forceSyncDevice = async (params, admin, facepassServer) => {
  const {
    serialNumber,
    premiseId,
  } = params;

  // validate if admin has access to the premise.
  const foundAdmin = await AdminModel.findOne({
    uid: admin.uid,
  }).lean();
  const foundPremise = _.find(foundAdmin.premises, (premise) => { if (premise.premiseId.toString() === premiseId) return premise; });
  if (_.isEmpty(foundPremise)) {
    throw responses.failure('You have no access to this premise.');
  }

  // validate if device is under the premise.
  const foundDevice = await DeviceModel.findOne({ serialNumber });
  if (_.isEmpty(foundDevice)) {
    throw responses.failure('Device does not exist in this premise.');
  }

  try {
    await facepassServer.emitToDevice(foundDevice.premiseId.toString(), serialNumber, 'force-sync', null);
    return 'force sync successfully!';
  } catch (err) {
    console.log(err);
    throw responses.failure(err);
  }
};

exports.forceSyncDeviceWithApiKey = async (params, facepassServer) => {
  const {
    serialNumber,
    premiseId,
  } = params;

  // validate if device is under the premise.
  const foundDevice = await DeviceModel.findOne({ serialNumber });
  if (_.isEmpty(foundDevice)) {
    throw responses.failure('Device does not exist in this premise.');
  }

  try {
    await facepassServer.emitToDevice(foundDevice.premiseId.toString(), serialNumber, 'force-sync', null);
    return 'force sync successfully!';
  } catch (err) {
    console.log(err);
    throw responses.failure(err);
  }
};

exports.getDeviceUserCount = async (params) => {
  const { serialNumber } = params;

  const device = await DeviceModel.findOne({
    serialNumber,
  }).sort({ createdAt: -1 }).lean();

  const userCount = await AccessModel.countDocuments({ isDeleted: false, premiseId: device?.premiseId }).lean();
  const userList = await AccessModel.aggregate([
    {
      $match: {
        isDeleted: false,
        premiseId: mongoose.Types.ObjectId(device?.premiseId),
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'uid',
        foreignField: '_id',
        as: 'users',
      },
    }]);
  const faceList = _.compact(_.map(userList, (user) => {
    if (user?.users[0]?.faceImageUrl) {
      return { faceImage: user?.users[0]?.faceImageUrl, fullName: user?.users[0]?.fullName };
    }
    return undefined;
  }));

  return { userCount, faceCount: faceList?.length, faceList };
};
