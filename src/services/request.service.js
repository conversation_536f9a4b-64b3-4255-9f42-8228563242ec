const moment = require('moment');
const responses = require('#helpers/responses');
const RequestModel = require('#models/Request.model');
const _ = require('lodash');
const { default: mongoose } = require('mongoose');

exports.getAllRequests = async (params, user) => {
  const { premiseId } = params;
  try {
    const request = await RequestModel.find({ premise: premiseId, isDeleted: false });
    return request;
  } catch (e) {
    throw responses.failure('getAllRequests failure:', e);
  }
};

exports.getRequestById = async (params, user) => {
  const { id } = params;
  try {
    const request = await RequestModel.findOne({ _id: id, premise: { $in: _.get(user, 'premises') } });
    return request;
  } catch (e) {
    throw responses.failure('getRequestById failure', e);
  }
};

exports.createRequest = async (params, user) => {
  const session = await mongoose.startSession();
  await session.startTransaction();
  try {
    const request = await RequestModel.create([{ ...params, isDeleted: false, status: 'pending' }], { session });
    await session.commitTransaction();
    await session.endSession();
    return request;
  } catch (e) {
    await session.abortTransaction();
    throw responses.failure('createRequest failure', e);
  }
};

exports.updateRequest = async (params, user) => {
  const { id } = params;
  const session = await mongoose.startSession();
  await session.startTransaction();
  try {
    const request = await RequestModel.findByIdAndUpdate(id, { ...params }, { session, returnDocument: 'after' });
    await session.commitTransaction();
    await session.endSession();
    return request;
  } catch (e) {
    await session.abortTransaction();

    throw responses.failure('updateRequest failure', e);
  }
};
exports.updateRequestStatus = async (params, user) => {
  const { id, status } = params;
  const session = await mongoose.startSession();
  await session.startTransaction();
  try {
    const request = await RequestModel.findByIdAndUpdate(id, { status }, { session, returnDocument: 'after' });
    await session.commitTransaction();
    await session.endSession();
    return request;
  } catch (e) {
    await session.abortTransaction();

    throw responses.failure('updateRequestStatus failure', e);
  }
};

exports.deleteRequest = async (params, user) => {
  const { id } = params;
  const session = await mongoose.startSession();
  await session.startTransaction();
  try {
    const request = await RequestModel.findByIdAndUpdate(id, { isDeleted: true }, { session });
    await session.commitTransaction();
    await session.endSession();
    return request;
  } catch (e) {
    await session.abortTransaction();
    throw responses.failure('deleteRequest failure', e);
  }
};
