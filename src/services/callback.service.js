const fetch = require('node-fetch');
const PremiseModel = require('#models/Premise.model');
const UserModel = require('#models/User.model');
const _ = require('lodash');

exports.callback = async (params) => {
  const {
    premiseId,
    user,
    score,
    serialNumber,
    trackId,
    attributes,
    type,
    createdAt,
    prediction,
    deviceId,
  } = params;
  const foundPremise = await PremiseModel.findOne({
    _id: premiseId.toString(),
  });
  const foundUser = await UserModel.findOne({ _id: user.toString() });

  if (!_.isEmpty(foundPremise?.callbackUrl)) {
    await fetch(foundPremise?.callbackUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Callback-Key': foundPremise.apiKey || foundPremise.callbackKey,
      },
      body: JSON.stringify({
        success: true,
        uid: foundUser?.toString(),
        name: foundUser?.fullName,
        photoUrl: foundUser?.faceImageUrl,
        temperature: 0.0,
        score,
        serialNumber,
        createdBy: null,
        premiseId,
        trackId,
        attributes,
        employee: false,
        checkType: type,
        createdAt,
        prediction,
        deviceId,
      }),
    });
  }
};
