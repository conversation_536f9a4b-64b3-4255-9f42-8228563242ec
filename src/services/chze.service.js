const DeviceModel = require('#models/Device.model');
const PremiseModel = require('#models/Premise.model');
const _ = require('lodash');
const responses = require('#helpers/responses');
const DeviceSettingsRecord = require('#models/DeviceSettingsRecord');
const moment = require('moment');
const AccessRecordModel = require('#models/AccessRecord.model');
const CallModel = require('#models/Call.model');
const AccessModel = require('#models/Access.model');
const UserModel = require('#models/User.model');
// Chze add
exports.getAllDevices = async () => {
  const devices = await DeviceModel.find({}).populate('premiseId');
  return devices;
};

exports.testGetAllRecords = async () => {
  const access = await AccessRecordModel.find({}).limit(10).sort({ createdAt: -1 });
  return access;
};

exports.getAllPremises = async () => {
  const premises = await PremiseModel.find({});
  return premises;
};

exports.test = async () => {
  // const id = ['659541767f86f2d44f2cb12c',
  //   '659542937f86f2d44f2cb182',
  //   '659542b07f86f2d44f2cb193',
  //   '659543477f86f2d44f2cb1f9',
  //   '659543247f86f2d44f2cb1e8',
  //   '659543067f86f2d44f2cb1d7',
  //   '659542ea7f86f2d44f2cb1c6',
  //   '659542d17f86f2d44f2cb1b5',
  //   '659542c07f86f2d44f2cb1a4',
  //   '6595469d7f86f2d44f2cb376',
  //   '659545707f86f2d44f2cb2b7',
  //   '659546587f86f2d44f2cb34a',
  //   '659544fa7f86f2d44f2cb2a1',
  //   '6595467d7f86f2d44f2cb360',
  //   '659546177f86f2d44f2cb334',
  //   '659545d37f86f2d44f2cb2da',
  //   '659545f57f86f2d44f2cb30f'];
  // const test = await AccessModel.deleteMany({ uid: { $nin: id }, premiseId: '654c931699313cac33e46652' });
  // const idUser = [];
  // _.map(test, (data) => {
  //   idUser.push(_.get(data, 'uid'));
  // });
  // console.log(idUser);
  // const test2 = await UserModel.deleteMany({ _id: { $in: idUser } });
  // console.log(_.size(test));
  // console.log(_.size(test2));
  // return test2;
  // const test = await UserModel.updateMany({ $unset: { type: 1 } });
};

exports.getDevicesSettingWithoutPremises = async (params) => {
  const { serialNumber } = params;
  const device = await DeviceSettingsRecord.findOne({
    serialNumber,
  }).sort({ createdAt: -1 }).lean();
  return {
    _id: device?._id,
    settings: device?.settings,
    cameraSettings: device?.cameraSettings,
    timestamps: device?.timestamps,
  };
};

exports.createCall = async (params) => {
  console.log(params);
  const { serialNumber, status } = params.params;
  console.log('SERIAL', serialNumber);
  console.log('STATUS', status);
  await CallModel.create({
    serialNumber,
    status,
  });
};

exports.createNewDepartment = async (newDepartment) => {
  console.log(newDepartment);

  const { premise, name, createdAt } = newDepartment;
  try {
    const premiseUpdate = await PremiseModel.findById(premise);
    if (!premiseUpdate) {
      console.log('Premise not found');
    }
    premiseUpdate.departments.push({ departmentName: name });
    await premiseUpdate.save();
    const addedDepartment = premiseUpdate.departments.find((departments) => departments.departmentName === name);

    return { id: addedDepartment._id, name: addedDepartment.departmentName };
  } catch (e) {
    throw responses.failure({ data: { message: `Error ${e}` } });
  }
};
