/* eslint-disable consistent-return */
// sync user access to facial recognition device.
/*
pre-condition:
1. the device is registered with the department details.

1. get all the user that is register under that device department.
*/
const ExcelJS = require('exceljs');

const AccessModel = require('#models/Access.model');
const AccessRecordModel = require('#models/AccessRecord.model');
const DeviceUsersModel = require('#models/DeviceUsers.model');
const PremiseModel = require('#models/Premise.model');
const _ = require('lodash');
const { default: mongoose } = require('mongoose');
const responses = require('#helpers/responses');
const AdminModel = require('#models/Admin.model');
const AdminApikeyModel = require('#models/AdminApikey.model');
const UserModel = require('#models/User.model');
const { ObjectId } = require('mongodb');
const moment = require('moment');
const ChangeLogsModel = require('#models/ChangeLogs.model');

exports.createAccessRecord = async (deviceData, socket, type) => {
  const { searchScore, trackID, faceToken, department, createdAt } = deviceData;
  const { devicePremiseId, handshake } = socket;

  console.log('device premiseId:', devicePremiseId);
  console.log(
    'device serial number:',
    _.get(handshake, 'headers.serialnumber', '')
  );
  console.log('face token:', faceToken);
  // console.log('face token:', handshake);

  const deviceUser = await DeviceUsersModel.aggregate([
    {
      $lookup: {
        from: 'devices',
        localField: 'device',
        foreignField: '_id',
        as: 'devices',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'users.user',
        foreignField: '_id',
        as: 'up',
      },
    },
    {
      $match: {
        'devices.serialNumber': _.get(handshake, 'headers.serialnumber', ''),
        premiseId: mongoose.Types.ObjectId(devicePremiseId),
        'users.faceToken': faceToken,
      },
    },
  ]);

  if (!_.isEmpty(deviceUser[0]?.users)) {
    const foundUserId = _.find(
      deviceUser[0].users,
      (user) => user?.faceToken === faceToken
    );

    const foundUser = _.find(
      deviceUser[0].up,
      (user) => user._id.toString() === foundUserId.user.toString()
    );

    const createdAccessRecord = await AccessRecordModel.create({
      type,
      department,
      serialNumber: _.get(handshake, 'headers.serialnumber', ''),
      premiseId: devicePremiseId,
      deviceId: _.get(deviceUser[0], 'devices[0]._id', ''),
      user: foundUser._id,
      score: searchScore,
      trackId: trackID,
      attributes: JSON.stringify(deviceData),
      prediction: 'testing',
      createdAt,
    });
  }
};

exports.assignUserToDepartments = async (params, admin) => {
  const { userId, premiseId, departments } = params;

  // validate if admin has access to the premise.
  const foundAdmin = await AdminModel.findOne({
    uid: admin.uid,
  })
    .populate('premises.premiseId')
    .lean();
  const foundPremise = _.find(foundAdmin.premises, (premise) => {
    if (premise.premiseId?._id.toString() === premiseId) return premise;
  });
  if (_.isEmpty(foundPremise)) {
    throw responses.failure('You have no access to this premise.');
  }

  // validate if user is in the premise.
  const foundAccess = await AccessModel.findOne({
    uid: userId,
    premiseId,
    isDeleted: false,
  });
  if (_.isEmpty(foundAccess)) {
    throw responses.failure('User not found in this premise.');
  }

  // check department
  _.chain(departments)
    .map((dep) => {
      const foundDepartment = _.find(foundPremise.premiseId.departments, {
        _id: mongoose.Types.ObjectId(dep.departmentId),
      });
      if (!_.isEmpty(foundDepartment)) {
        return true;
      }
      throw responses.failure(
        `department ${dep.departmentName} does not exist.`
      );
    })
    .compact()
    .value();

  // update access
  const updatedAccess = await AccessModel.findOneAndUpdate(
    {
      uid: userId,
      premiseId,
      isDeleted: false,
    },
    {
      departments,
    }
  );

  return updatedAccess;
};

const getLastCheckOutTime = (dataset) => {
  let time = '-';
  const firstData = _.findLast(dataset, (x) => {
    if (x.type === 'checkOut') {
      return true;
    }
  });
  if (!_.isNil(firstData)) {
    time = moment(_.get(firstData, 'createdAt')); // .format('h:mmA');
  }
  return time;
};

const getFirstCheckInTime = (dataset) => {
  let time = '-';
  const firstData = _.find(dataset, (x) => {
    if (x.type === 'checkIn') {
      return true;
    }
  });
  if (!_.isNil(firstData)) {
    time = moment(_.get(firstData, 'createdAt')); // .format('h:mmA');
  }
  return time;
};

const getFirstScan = (dataset) => {
  let time = '-';
  const firstData = _.first(dataset);
  if (!_.isNil(firstData)) {
    time = moment(_.get(firstData, 'createdAt')); // .format('h:mmA');
  }
  return time;
};

const getLastScan = (dataset) => {
  let time = '-';
  if (_.size(dataset) > 1) {
    const lastData = _.last(dataset);
    if (!_.isNil(lastData)) {
      time = moment(_.get(lastData, 'createdAt')); // .format('h:mmA');
    }
  }
  return time;
};

const getFirstLunchOutTime = (dataset) => {
  let time = '-';
  const firstData = _.find(dataset, (x) => {
    if (x.type === 'lunchOut') {
      return true;
    }
  });
  if (!_.isNil(firstData)) {
    time = moment(_.get(firstData, 'createdAt')); // .format('h:mmA');
  }
  return time;
};

const getLastLunchInTime = (dataset) => {
  let time = '-';
  const lastData = _.findLast(dataset, (x) => {
    if (x.type === 'lunchIn') {
      return true;
    }
  });
  if (!_.isNil(lastData)) {
    time = moment(_.get(lastData, 'createdAt')); // .format('h:mmA');
  }
  return time;
};

const getLateInTime = (dataset, workStartTime) => {
  if (_.isDate(workStartTime)) {
    let intime = '-';
    const firstData = _.first(dataset);
    if (!_.isNil(firstData)) {
      intime = moment(_.get(firstData, 'createdAt')).set({
        date: 1,
        month: 0,
        year: 1970,
      }); // .format('h:mmA');
      const startTime = moment(workStartTime).set({
        date: 1,
        month: 0,
        year: 1970,
      });
      let lateIn = '-';
      if (moment(intime).isAfter(moment(startTime))) {
        lateIn = intime.diff(startTime);
        const duration = moment.duration(lateIn);

        // Extract hours and minutes
        const hours = Math.floor(duration.asHours());
        const minutes = Math.floor(duration.minutes());

        // Format the result as HH:mm
        const result = moment.utc({ hours, minutes }).format('HH:mm');
        return result;
      }
      return '-';
    }

    return '-';
  }
  return '-';
};

const getEarlyInTime = (dataset, workStartTime) => {
  if (_.isDate(workStartTime)) {
    let intime = '-';
    const firstData = _.first(dataset);
    if (!_.isNil(firstData)) {
      intime = moment(_.get(firstData, 'createdAt')).set({
        date: 1,
        month: 0,
        year: 1970,
      }); // .format('h:mmA');
      const startTime = moment(workStartTime).set({
        date: 1,
        month: 0,
        year: 1970,
      }); // .format('h:mmA');
      let earlyIn = '-';
      if (moment(intime).isBefore(startTime)) {
        earlyIn = startTime.diff(intime);
        const duration = moment.duration(earlyIn);

        // Extract hours and minutes
        const hours = Math.floor(duration.asHours());
        const minutes = Math.floor(duration.minutes());

        // Format the result as HH:mm
        const result = moment.utc({ hours, minutes }).format('HH:mm');
        return result;
      }
      return '-';
    }

    return '-';
  }
  return '-';
};

const getLateOutTime = (dataset, workEndTime) => {
  if (_.isDate(workEndTime)) {
    let outtime = '-';
    let lateOut = '-';

    if (_.size(dataset) > 1) {
      const lastData = _.last(dataset);
      if (!_.isNil(lastData)) {
        outtime = moment(_.get(lastData, 'createdAt')).set({
          date: 1,
          month: 0,
          year: 1970,
        }); // .format('h:mmA');
        const endTime = moment(workEndTime).set({
          date: 1,
          month: 0,
          year: 1970,
        });
        if (moment(outtime).isAfter(endTime)) {
          lateOut = outtime.diff(endTime);
          const duration = moment.duration(lateOut);

          // Extract hours and minutes
          const hours = Math.floor(duration.asHours());
          const minutes = Math.floor(duration.minutes());

          // Format the result as HH:mm
          const result = moment.utc({ hours, minutes }).format('HH:mm');
          return result;
        }
        return '-';
      }
    }
    return '-';
  }
  return '-';
};

const getEarlyOutTime = (dataset, workEndTime) => {
  if (!_.isEmpty(workEndTime)) {
    let outtime = '-';
    let earlyOut = '-';

    if (_.size(dataset) > 1) {
      const lastData = _.last(dataset);
      if (!_.isNil(lastData)) {
        outtime = moment(_.get(lastData, 'createdAt')).set({
          date: 1,
          month: 0,
          year: 1970,
        }); // .format('h:mmA');
        const endTime = moment(workEndTime).set({
          date: 1,
          month: 0,
          year: 1970,
        }); // .format('h:mmA');
        if (moment(outtime).isBefore(endTime)) {
          earlyOut = endTime.diff(outtime);
          const duration = moment.duration(earlyOut);

          // Extract hours and minutes
          const hours = Math.floor(duration.asHours());
          const minutes = Math.floor(duration.minutes());

          // Format the result as HH:mm
          const result = moment.utc({ hours, minutes }).format('HH:mm');
          return result;
        }
        return '-';
      }
      return '-';
    }

    return '-';
  }
  return '-';
};

const calculateHours = (
  checkIn,
  checkOut,
  lunchOut,
  lunchIn,
  firstScan,
  lastScan
) => {
  let workDuration = 0;
  let lunchDuration = 0;

  if (
    (checkIn !== '-' || firstScan !== '-') &&
    (checkOut !== '-' || lastScan !== '-')
  ) {
    const first = checkIn === '-' ? firstScan : checkIn;
    const last = checkOut === '-' ? lastScan : checkOut;
    workDuration = moment(last).diff(first);
    if (lunchIn !== '-' && lunchOut !== '-') {
      lunchDuration = moment(lunchIn).diff(lunchOut, 'hours');
    }
    // workDuration -= lunchDuration;
    // console.log('workDuration', workDuration);
    const duration = moment.duration(workDuration);

    // Extract hours and minutes
    const hours = Math.floor(duration.asHours());
    const minutes = Math.floor(duration.minutes());

    // Format the result as HH:mm
    const result = moment.utc({ hours, minutes }).format('HH:mm');
    return result;
  }
  workDuration = '-';
  return '-';
};

exports.getAllAccessRecords = async (params) => {
  const {
    premiseId,
    page = 1,
    limit = 10,
    startDate,
    endDate,
    searchText,
  } = params; // Default: page 1, limit 10
  const skip = (page - 1) * limit;
  console.log('params', params);

  // Build query object
  const query = { premiseId };

  // Add date filtering if provided
  if (startDate || endDate) {
    query.createdAt = {};
    if (startDate) {
      query.createdAt.$gte = new Date(startDate);
    }
    if (endDate) {
      query.createdAt.$lte = new Date(endDate);
    }
  }

  // If searchText is provided, create a pipeline to search in populated fields
  let accessRecords;
  let totalItems;

  if (searchText) {
    // Use aggregation pipeline for text search in populated fields
    const pipeline = [
      // { $match: query },
      {
        $lookup: {
          from: 'users',
          localField: 'user',
          foreignField: '_id',
          as: 'userDetails',
        },
      },
      {
        $lookup: {
          from: 'premises',
          localField: 'premiseId',
          foreignField: '_id',
          as: 'premiseDetails',
        },
      },
      // {
      //   $match: {
      //     $or: [
      //       { 'userDetails.fullName': { $regex: searchText, $options: 'i' } },
      //       { serialNumber: { $regex: searchText, $options: 'i' } },
      //     ],
      //   },
      // },
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: parseInt(limit, 10) },
    ];

    // Count total items matching the search criteria
    const countPipeline = [...pipeline];
    countPipeline.push({ $count: 'total' });

    const countResult = await AccessRecordModel.aggregate(countPipeline);
    totalItems = countResult.length > 0 ? countResult[0].total : 0;

    // Execute the main query
    accessRecords = await AccessRecordModel.aggregate(pipeline);

    // // Transform the result to match the structure of the original query
    accessRecords = await AccessRecordModel.populate(accessRecords, [
      { path: 'user', select: 'fullName faceImageUrl phoneNumber role' },
      { path: 'premiseId', select: 'name address' },
    ]);

    _.map(accessRecords, (record) => {
      _.unset(record, 'userDetails');
      _.unset(record, 'premiseDetails');
    });

    console.log('accessRecords', accessRecords);
  } else {
    // Use the original query method if no search text
    accessRecords = await AccessRecordModel.find(query)
      .populate('user', 'fullName faceImageUrl phoneNumber role')
      .populate('premiseId', 'name address')
      .skip(skip)
      .limit(parseInt(limit, 10))
      .sort({ createdAt: -1 });

    totalItems = await AccessRecordModel.countDocuments(query);
  }

  console.log('params:', params);

  return {
    accessRecords,
    pagination: {
      page: parseInt(page, 10),
      limit: parseInt(limit, 10),
      totalItems,
    },
  };
};

exports.getTodayVisitorRecords = async (params) => {
  const { premiseId } = params;
  const premiseObjectId = mongoose.Types.ObjectId.isValid(premiseId)
    ? new mongoose.Types.ObjectId(premiseId)
    : null;

  if (!premiseObjectId) {
    throw new Error('Invalid premiseId');
  }

  const startOfDay = moment().startOf('day').toDate();
  const endOfDay = moment().endOf('day').toDate();
  const accessRecords = await AccessRecordModel.find({
    premiseId,
    isDeleted: false,
    createdAt: { $gte: startOfDay, $lte: endOfDay },
  })
    .sort({ createdAt: -1 })
    .lean();

  // Extract all user IDs and fetch them in one query
  const userIds = accessRecords.map((record) => record?.user).filter(Boolean);
  const users = await UserModel.find({ _id: { $in: userIds } }).lean();

  // Create a user lookup map for quick access
  const userMap = new Map(users.map((user) => [user._id.toString(), user]));

  const formattedRecords = accessRecords.map((record) => {
    const user = userMap.get(record?.user?.toString()) || {}; // Fallback to empty object if user not found

    return {
      faceImage: user.faceImageUrl || '',
      fullName: user.fullName || '',
      gender: record.attributes?.gender || '',
      role: user.role || 'staff',
      phoneNumber: user.phoneNumber || '',
      visitTime: moment(record.createdAt).format('DD-MM-YYYY HH:mm:ss'),
    };
  });

  return formattedRecords;
};

exports.getAllAccessRecordsExternal = async (params) => {
  const { premiseId } = params;
  const accessRecords = await AccessRecordModel.find({ premiseId }).lean();
  return accessRecords;
};

exports.processAttendanceExternal = async (params, premiseId) => {
  const processedData = [];
  const { startDate, endDate } = params;
  const hideEmpty = _.get(params, 'hideEmpty') || true;
  const premiseName = (await PremiseModel.findById(premiseId).lean()).name;
  const rawAggregateData = await AccessRecordModel.aggregate([
    {
      $match: {
        // $or: [{ type: 'checkIn' }, { type: 'checkOut' }, { type: 'lunchIn' }, { type: 'lunchOut' }],
        type: { $exists: true, $ne: 'none' },
        premiseId: mongoose.Types.ObjectId(premiseId),
        createdAt: {
          $gte: params.startDate,
          $lte: params.endDate,
        },
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'fromUsers',
      },
    },
    // {
    //   $replaceRoot: {
    //     newRoot: {
    //       $mergeObjects: [{ $arrayElemAt: ['$fromUsers', 0] }, '$$ROOT'],
    //     },
    //   },
    // },
    // {
    //   $project: {
    //     uid: 1,
    //     fullName: 1,
    //     premiseId: 1,
    //     type: 1,
    //     createdAt: 1,
    //     serialNumber: 1,
    //     department: 1,
    //   },
    // },
  ]);
  const userIds = _.uniq(
    _.map(rawAggregateData, (data) => _.toString(_.get(data, 'user')))
  );

  const userList = !_.isEmpty(_.get(params, 'userList'))
    ? _.get(params, 'userList')
    : userIds;

  const getUsersInfo = await UserModel.find({
    _id: {
      $in: userList,
    },
  }).lean();
  const getUsersAccessInfo = await AccessModel.find({
    uid: {
      $in: userList,
    },
  }).lean();

  for (
    let count = moment(startDate);
    moment(count).isSameOrBefore(moment(endDate));
    count.add(1, 'day')
  ) {
    const allThisDayRecords = _.filter(rawAggregateData, (doc) => {
      const docDate = moment(_.get(doc, 'createdAt')).toDate();
      return moment(count).isSame(docDate, 'day');
    });
    // console.log('atdd', count);

    _.map(userList, (u) => {
      const perUserRecordThisDay = _.filter(allThisDayRecords, (r) => {
        return _.toString(r.user) === _.toString(u);
      });
      // console.log('PUR', u);
      const getWorkHour = _.find(getUsersAccessInfo, (usr) => {
        if (_.toString(usr.uid) === _.toString(u)) {
          return true;
        }
      });
      // if (perUserRecordThisDay.length) {
      const checkIn = getFirstCheckInTime(perUserRecordThisDay);
      const checkOut = getLastCheckOutTime(perUserRecordThisDay);
      const lunchOut = getFirstLunchOutTime(perUserRecordThisDay);
      const lunchIn = getLastLunchInTime(perUserRecordThisDay);
      const firstScan = getFirstScan(perUserRecordThisDay);
      const lastScan = getLastScan(perUserRecordThisDay);
      const lateIn = getLateInTime(
        perUserRecordThisDay,
        _.get(getWorkHour, 'startWorkingTime')
      );
      const lateOut = getLateOutTime(
        perUserRecordThisDay,
        _.get(getWorkHour, 'endWorkingTime')
      );
      const earlyIn = getEarlyInTime(
        perUserRecordThisDay,
        _.get(getWorkHour, 'startWorkingTime')
      );
      const earlyOut = getEarlyOutTime(
        perUserRecordThisDay,
        _.get(getWorkHour, 'endWorkingTime')
      );
      const hourCount = calculateHours(
        checkIn,
        checkOut,
        lunchOut,
        lunchIn,
        firstScan,
        lastScan
      );

      const getData = _.find(getUsersInfo, (usr) => {
        if (_.toString(usr._id) === _.toString(u)) {
          return true;
        }
      });
      const getName = _.get(getData, 'fullName');
      const newExcelRow = {
        date: moment(count).format('DD/MM/YYYY , ddd'),
        name: _.isNil(getName) ? 'ERROR_NO_NAME' : getName,
        checkIn: checkIn === '-' ? '-' : moment(checkIn).format('h:mmA'),
        checkOut: checkOut === '-' ? '-' : moment(checkOut).format('h:mmA'),
        lunchOut: lunchOut === '-' ? '-' : moment(lunchOut).format('h:mmA'),
        lunchIn: lunchIn === '-' ? '-' : moment(lunchIn).format('h:mmA'),
        firstScan: firstScan === '-' ? '-' : moment(firstScan).format('h:mmA'),
        lastScan: lastScan === '-' ? '-' : moment(lastScan).format('h:mmA'),
        lateIn: lateIn === '-' ? '-' : lateIn,
        lateOut: lateOut === '-' ? '-' : lateOut,
        earlyIn: earlyIn === '-' ? '-' : earlyIn,
        earlyOut: earlyOut === '-' ? '-' : earlyOut,

        hourCount: hourCount === '-' ? '-' : hourCount,
        // minuteCount: '',
      };
      // console.log('HE', hideEmpty);
      // console.log('PLEN', perUserRecordThisDay.length);
      if (hideEmpty === false && perUserRecordThisDay.length > 0) {
        processedData.push(newExcelRow);
      } else if (hideEmpty === false) {
        processedData.push(newExcelRow);
      } else if (hideEmpty === true && perUserRecordThisDay.length < 1) {
        return null;
      } else if (hideEmpty === true && perUserRecordThisDay.length > 0) {
        processedData.push(newExcelRow);
      }

      // }
    });
  }

  return {
    attendanceData: processedData,
    startUnix: moment(startDate).unix(),
    endUnix: moment(endDate).unix(),
    premiseFullName: premiseName,
  };
};

exports.exportAccessRecordExternal = async (params, premiseId) => {
  const processedData = [];
  const { startDate, endDate } = params;
  const hideEmpty = _.get(params, 'hideEmpty') || true;
  const premiseName = (await PremiseModel.findById(premiseId).lean()).name;
  const startUnix = moment(startDate).unix();
  const endUnix = moment(endDate).unix();
  const premiseFullName = premiseName;
  const rawAggregateData = await AccessRecordModel.aggregate([
    {
      $match: {
        // $or: [{ type: 'checkIn' }, { type: 'checkOut' }, { type: 'lunchIn' }, { type: 'lunchOut' }],
        type: { $exists: true, $ne: 'none' },
        premiseId: mongoose.Types.ObjectId(premiseId),
        createdAt: {
          $gte: params.startDate,
          $lte: params.endDate,
        },
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'fromUsers',
      },
    },
  ]);
  console.log(premiseName);
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Access Record', {
    pageSetup: { paperSize: 9, orientation: 'landscape' },
  });
  worksheet.getRow(5).values = ['', 'Name', 'Check Type', 'Date Time'];
  worksheet.columns = [
    { key: 'index', width: 5 },
    { key: 'name', width: 15, alignment: { vertical: 'middle' } },
    { key: 'checkType', width: 15, alignment: { vertical: 'middle' } },
    {
      key: 'dateTime',
      width: 15,
      alignment: { vertical: 'middle', horizontal: 'right' },
    },
  ];
  const exportData = _.map(rawAggregateData, (data) => {
    return {
      name: data?.fromUsers[0]?.fullName,
      checkType: _.startCase(data?.type),
      dateTime: moment(data?.createdAt).format('DD/MM/YYYY HH:mm:ss'),
    };
  });
  _.forEach(exportData, (row, index) => {
    _.set(row, 'index', index + 1);
    worksheet.addRow(row);
  });

  worksheet.eachRow((row, number) => {
    row.eachCell((cell, colNumber) => {
      cell.alignment = { vertical: 'middle' };
      if (_.includes([5, 6, 8, 9, 10, 11, 12, 13, 14], colNumber)) {
        cell.alignment = {
          wrapText: 'true',
          horizontal: 'center',
          vertical: 'middle',
        };
      }
      if (_.includes([4, 7], colNumber)) {
        cell.alignment = { horizontal: 'right', vertical: 'middle' };
      }
      if (number > 4) {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      }
    });
  });

  worksheet.getCell('B2').value = 'COMPANY';
  worksheet.getCell('B3').value = 'FROM';
  worksheet.getCell('B4').value = 'TO';

  _.map(['B2', 'B3', 'B4'], (key) => {
    worksheet.getCell(key).style = { font: { bold: true } };
    worksheet.getCell(key).alignment = {
      vertical: 'bottom',
      horizontal: 'right',
    };
  });

  const formattedStartDate = moment
    .unix(startUnix)
    .startOf('day')
    .format('DD/MM/YYYY');
  const formattedEndDate = moment
    .unix(endUnix)
    .endOf('day')
    .format('DD/MM/YYYY');
  worksheet.getCell('C2').value = premiseFullName;
  worksheet.getCell('C3').value = formattedStartDate;
  worksheet.getCell('C3').alignment = {
    vertical: 'bottom',
    horizontal: 'right',
  };
  worksheet.getCell('C4').value = formattedEndDate;
  worksheet.getCell('C4').alignment = {
    vertical: 'bottom',
    horizontal: 'right',
  };
  worksheet.mergeCells('A1:I1');
  worksheet.getCell('A1').value = 'Access Record Worksheet';
  worksheet.getCell('A1').alignment = {
    vertical: 'bottom',
    horizontal: 'center',
  };
  try {
    // const data = await workbook.xlsx.writeFile('users.xlsx');
    // const filebuffer = await workbook.xlsx.writeBuffer();
    worksheet.columns.forEach((column, i) => {
      let maxLength = 0;
      column.eachCell({ includeEmpty: true }, (cell) => {
        const columnLength = cell.value ? cell.value.toString().length : 10;
        if (columnLength > maxLength) {
          maxLength = columnLength;
        }
      });
      column.width = maxLength < 10 ? 10 : maxLength;
    });
    return workbook;
  } catch (e) {
    console.log(e);
  }
};

exports.createUserAccess = async (params, key) => {
  try {
    const { userId, premiseIdToQuery } = params;
    // ⚠️ if access already exist just change isDelete
    // if not exist create new

    // check if admin key is valid
    // console.log('KEY:', key);
    const foundAdmin = await AdminApikeyModel.findOne({
      apiKey: key,
    }).lean();
    const foundPremise = _.find(foundAdmin.premises, (premise) => {
      if (premise.premiseId.toString() === premiseIdToQuery) return premise;
    });
    if (_.isEmpty(foundPremise)) {
      throw responses.failure('You have no access to this premise.');
    }

    // check if user-premise pair does not exist
    const foundAccess = await AccessModel.findOne({
      uid: userId,
      premiseId: premiseIdToQuery,
    });
    // console.log('foundAccessDoc', foundAccess);
    const session = await mongoose.startSession();
    const response = {};

    if (!_.isEmpty(foundAccess)) {
      // console.log('SOMETHING HERE');
      if (foundAccess.isDeleted) {
        await AccessModel.findByIdAndUpdate(foundAccess._id, {
          isDeleted: false,
        })
          .then(async () => {
            _.set(response, 'data', `access restored ${foundAccess._id}`);
            console.log('☀️ access restored');
            await ChangeLogsModel.create(
              [
                {
                  userId: new ObjectId(userId),
                  premiseId: new ObjectId(premiseIdToQuery),
                  documentId: new ObjectId(foundAccess._id),
                  isDeletedChange: false,
                  adminId: foundAdmin._id,
                },
              ],
              { session }
            )
              .then(() => console.log('added new LOG'))
              .catch((e) => {
                throw e;
              });
          })
          .catch((e) => {
            throw e;
          });
      } else {
        throw responses.failure('User already has access.');
      }
    } else {
      const user = await UserModel.findById(userId);
      const premiseDoc = await PremiseModel.findById(premiseIdToQuery);
      // console.log('✨ check user id :', _.get(user, '_id'));
      // console.log('🟢 compare with params :', userId);
      // console.log('🏢 Premise doc:', premiseDoc);

      const update = AccessModel.create(
        [
          {
            uid: new ObjectId(user._id), // ObjectId(_.get(user, '_id')),
            premiseId: new ObjectId(premiseIdToQuery), // ObjectId(premiseIdToQuery),
            isDeleted: false,
            startWorkingTime: moment().set('hour', 8).set('minutes', 30), // Need change to manual provide
            endWorkingTime: moment().set('hour', 17).set('minutes', 30),
            departments: [
              {
                _id: mongoose.Types.ObjectId(
                  premiseDoc.departments[0]._id.toString()
                ),
                departmentId: 'main',
                access: true,
                type: 'toggle',
              },
            ],
          },
        ],
        { session }
      );

      await update
        .then(async () => {
          // console.log('🟢 NEW ACCESS DOC CREATED', update);
          await ChangeLogsModel.create(
            [
              {
                userId: new ObjectId(user._id),
                premiseId: new ObjectId(premiseIdToQuery),
                documentId: null,
                isDeletedChange: false,
                adminId: foundAdmin._id,
              },
            ],
            { session }
          )
            .then((res) => {
              // console.log('📄 LOGS CREATED:', res);
              _.set(response, 'data', res);
              return res;
            })
            .catch((e) => {
              throw e;
            });
        })
        .catch((e) => {
          throw e;
        });

      return response;
    }
  } catch (error) {
    throw responses.failure(error);
  }
};

exports.deleteUserAccessWithAdminKey = async (params, key) => {
  const { userId, premiseId } = params;
  // validate if admin has access to the premise.
  const foundAdmin = await AdminApikeyModel.findOne({
    apiKey: key,
  }).lean();
  const foundPremise = _.find(foundAdmin.premises, (premise) => {
    if (premise.premiseId.toString() === premiseId) return premise;
  });
  if (_.isEmpty(foundPremise)) {
    throw responses.failure('You have no access to this premise.');
  }

  console.log('🟠 premiseId:,', premiseId);
  const foundAccess = await AccessModel.findOne({
    uid: userId,
    premiseId: new ObjectId(premiseId),
  });
  if (_.isEmpty(foundAccess)) {
    throw responses.failure('User access not found in this premise.');
  }
  console.log('✨ Found Access id', foundAccess._id);

  const session = await mongoose.startSession();
  // revoking user access.
  const deletedUser = AccessModel.findByIdAndUpdate(
    { _id: foundAccess._id },
    {
      isDeleted: true,
      'departments[0].access': false,
    },
    { new: true }
  );

  await deletedUser.then(async (res) => {
    console.log('✅ USER ACCESS REVOKED ✅', res.data);
    await ChangeLogsModel.create(
      [
        {
          userId: foundAccess.uid,
          premiseId: foundAccess.premiseId,
          documentId: foundAccess._id,
          isDeletedChange: true,
          adminId: foundAdmin._id,
        },
      ],
      { session }
    )
      .then((res2) => console.log('📄 LOGS CREATED:', res2))
      .catch((e) => {
        throw e;
      });
  });

  console.log('Deleted User', deletedUser);

  return {
    message: 'User access removed',
    parameters: {
      userId,
      premiseId,
    },
  };
};

exports.getScanRecordsByPremise = async (params, user) => {
  const { premiseIdToQuery, startDate, endDate, role } = params;
  console.log(user);
  const foundAdmin = await AdminModel.findOne({
    _id: _.get(user, '_id'),
  }).lean();
  const foundPremise = _.find(foundAdmin.premises, (premise) => {
    if (premise.premiseId.toString() === premiseIdToQuery) return premise;
  });
  if (_.isEmpty(foundPremise)) {
    throw responses.failure('You have no access to this premise.');
  }

  const processedData = [];

  const hideEmpty = _.get(params, 'hideEmpty') || true;
  const premiseName = (await PremiseModel.findById(premiseIdToQuery).lean())
    .name;
  const rawAggregateData = await AccessRecordModel.aggregate([
    {
      $match: {
        // $or: [{ type: 'checkIn' }, { type: 'checkOut' }, { type: 'lunchIn' }, { type: 'lunchOut' }],
        type: { $exists: true, $ne: 'none' },
        premiseId: mongoose.Types.ObjectId(premiseIdToQuery),
        createdAt: {
          $gte: startDate,
          $lt: endDate,
        },
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'fromUsers',
      },
    },
  ]);

  let userListTemp = await AccessModel.find({ premiseId: premiseIdToQuery });
  if (!_.isEmpty(role)) {
    userListTemp = await AccessModel.find({
      premiseId: premiseIdToQuery,
      'departments.0.role': role,
    });
  }

  const userList = _.map(userListTemp, (data) => _.get(data, 'uid'));
  const getUsersInfo = await UserModel.find({
    _id: {
      $in: userList,
    },
  }).lean();
  const getUsersAccessInfo = await AccessModel.find({
    uid: {
      $in: userList,
    },
  }).lean();

  for (
    let count = moment(startDate);
    moment(count).isSameOrBefore(moment(endDate));
    count.add(1, 'day')
  ) {
    const allThisDayRecords = _.filter(rawAggregateData, (doc) => {
      const docDate = moment(_.get(doc, 'createdAt')).toDate();
      return moment(count).isSame(docDate, 'day');
    });
    // console.log('atdd', count);

    _.map(userList, (u) => {
      const perUserRecordThisDay = _.filter(allThisDayRecords, (r) => {
        return _.toString(r.user) === _.toString(u);
      });
      // console.log('PUR', u);
      const getWorkHour = _.find(getUsersAccessInfo, (usr) => {
        if (_.toString(usr.uid) === _.toString(u)) {
          return true;
        }
      });
      // if (perUserRecordThisDay.length) {
      const checkIn = getFirstCheckInTime(perUserRecordThisDay);
      const checkOut = getLastCheckOutTime(perUserRecordThisDay);
      const lunchOut = getFirstLunchOutTime(perUserRecordThisDay);
      const lunchIn = getLastLunchInTime(perUserRecordThisDay);
      const firstScan = getFirstScan(perUserRecordThisDay);
      const lastScan = getLastScan(perUserRecordThisDay);
      const lateIn = getLateInTime(
        perUserRecordThisDay,
        _.get(getWorkHour, 'startWorkingTime')
      );
      const lateOut = getLateOutTime(
        perUserRecordThisDay,
        _.get(getWorkHour, 'endWorkingTime')
      );
      const earlyIn = getEarlyInTime(
        perUserRecordThisDay,
        _.get(getWorkHour, 'startWorkingTime')
      );
      const earlyOut = getEarlyOutTime(
        perUserRecordThisDay,
        _.get(getWorkHour, 'endWorkingTime')
      );
      const hourCount = calculateHours(
        checkIn,
        checkOut,
        lunchOut,
        lunchIn,
        firstScan,
        lastScan
      );

      const getName = _.get(
        _.find(getUsersInfo, (usr) => {
          if (_.toString(usr._id) === _.toString(u)) {
            return true;
          }
        }),
        'fullName'
      );
      const newExcelRow = {
        date: moment(count).format('DD/MM/YYYY , ddd'),
        name: _.isNil(getName) ? 'ERROR_NO_NAME' : getName,
        checkIn: checkIn === '-' ? '-' : moment(checkIn).format('h:mmA'),
        checkOut: checkOut === '-' ? '-' : moment(checkOut).format('h:mmA'),
        lunchOut: lunchOut === '-' ? '-' : moment(lunchOut).format('h:mmA'),
        lunchIn: lunchIn === '-' ? '-' : moment(lunchIn).format('h:mmA'),
        firstScan: firstScan === '-' ? '-' : moment(firstScan).format('h:mmA'),
        lastScan: lastScan === '-' ? '-' : moment(lastScan).format('h:mmA'),
        lateIn: lateIn === '-' ? '-' : lateIn,
        lateOut: lateOut === '-' ? '-' : lateOut,
        earlyIn: earlyIn === '-' ? '-' : earlyIn,
        earlyOut: earlyOut === '-' ? '-' : earlyOut,

        hourCount: hourCount === '-' ? '-' : hourCount,
        // minuteCount: '',
      };
      // console.log('NER', newExcelRow);
      // console.log('HE', hideEmpty);
      // console.log('PLEN', perUserRecordThisDay.length);
      if (hideEmpty === false && perUserRecordThisDay.length > 0) {
        processedData.push(newExcelRow);
      } else if (hideEmpty === false) {
        processedData.push(newExcelRow);
      } else if (hideEmpty === true && perUserRecordThisDay.length < 1) {
        return null;
      } else if (hideEmpty === true && perUserRecordThisDay.length > 0) {
        processedData.push(newExcelRow);
      }

      // }
    });
  }

  return processedData;
};

exports.restoreUserAccess = async (userId) => {
  console.log('userId', userId);
  const accessPermission = await AccessModel.findOneAndUpdate(
    { uid: userId },
    { isDeleted: false },
    { new: true }
  );
  if (_.isNil(accessPermission)) {
    throw responses.failure(`user ${userId} not exist or already has access`);
  }
  // console.log(accessPermission);
  return accessPermission;
};

exports.resetUserAllUserAccess = async (key) => {
  const accessDocs = await AccessModel.find({ isDeleted: true });
  return accessDocs;
};

exports.getAccessRecordsByUser = async (params) => {
  const data = [];
  const {
    startTime,
    endTime,
    premiseId,
    userId,
    page = 0,
    limit = 10,
  } = params;

  console.log('params', params);

  // ensure endTime is set to the end of the day (23:59:59)
  // const formattedEnd = moment(endTime).endOf('day').toISOString();
  // let query = {
  //   premiseId,
  //   createdAt: {
  //     $gte: moment(startTime).toISOString(),
  //     $lte: formattedEnd,
  //   },
  // };

  const formattedStart = moment.utc(startTime).startOf('day').toISOString();
  const formattedEnd = moment.utc(endTime).endOf('day').toISOString();
  let query = {
    premiseId,
    createdAt: {
      $gte: formattedStart,
      $lte: formattedEnd,
    },
  };
  if (!_.isEmpty(userId)) {
    query = {
      user: { $in: userId },
      premiseId,
      createdAt: {
        $gte: formattedStart,
        $lte: formattedEnd,
      },
    };
  }

  console.log('query', query);

  const listOfScans = await AccessRecordModel.find(query)
    .populate('user', 'fullName _id')
    .populate(
      'premiseId',
      '-departments -theme -apiKey -childPremises -employees -public'
    )
    .skip(page * limit)
    .limit(limit)
    .lean();

  console.log('listOfScans', listOfScans);

  const countOfScans = await AccessRecordModel.find(query);

  return {
    scans: listOfScans,
    pagination: {
      total: countOfScans.length,
      page,
      limit,
      pages: Math.floor(countOfScans.length / limit),
    },
  };
};

exports.bulkExternalAccess = async (params, facepassServer) => {
  const { premiseId, apiKey, userIds, status } = params;

  // validate if the premise is under this admin
  const isValidated = await AdminApikeyModel.exists({
    apiKey,
    'premises.premiseId': premiseId,
  });

  if (!isValidated) {
    throw new Error(
      'The premise is not under this admin account. Please try another premise.'
    );
  }

  // grant access to the users based on the status
  if (status === 'remove') {
    await AccessModel.deleteMany({
      premiseId,
      uid: { $in: userIds },
    });

    await facepassServer.emit(premiseId, 'force-sync', {});
    return 'Users removed successfully!';
  }

  if (status === 'grant') {
    // check if user granted before.
    const foundAccesses = await AccessModel.find({
      premiseId,
      uid: { $in: userIds },
    }).lean();
    console.log('found accesses:', foundAccesses);

    const usersWithNoAccess = _.map(userIds, (userId) => {
      console.log('user id:', userId);

      if (
        !_.find(foundAccesses, (access) => access.uid.toString() === userId)
      ) {
        return userId;
      }
      return null;
    });
    console.log('users with no access:', usersWithNoAccess);

    const premise = await PremiseModel.findOne({ _id: premiseId });
    const users = _.map(_.compact(usersWithNoAccess), (user) => ({
      uid: user,
      premiseId,
      departments: premise.departments,
      isDeleted: false,
      startWorkingTime: moment().toISOString(),
      endWorkingTime: moment().toISOString(),
    }));
    await AccessModel.insertMany(users);

    await facepassServer.emit(premiseId, 'force-sync', {});
    return 'Users granted successfully!';
  }
};

exports.createUserAccessByBody = async (params, key, facepassServer) => {
  const session = await mongoose.startSession();
  await session.startTransaction();
  try {
    const { userId, premiseId, access } = params; // accessType: 'grant' or 'revoke'

    // Validate access type
    if (!['grant', 'revoke'].includes(access)) {
      throw responses.failure(
        'Invalid access type. Must be "grant" or "revoke".'
      );
    }

    // Check if admin key is valid
    const foundAdmin = await AdminApikeyModel.findOne({
      apiKey: key,
    }).lean();

    if (!foundAdmin) {
      throw responses.failure('Invalid API key.');
    }

    // Check if admin has access to this premise
    const foundPremise = _.find(foundAdmin.premises, (premise) => {
      return premise.premiseId.toString() === premiseId;
    });

    if (_.isEmpty(foundPremise)) {
      throw responses.failure('You have no access to this premise.');
    }

    // Check if user exists
    const user = await UserModel.findById(userId);
    if (!user) {
      throw responses.failure('User not found.');
    }

    // Check if premise exists
    const premiseDoc = await PremiseModel.findById(premiseId);
    if (!premiseDoc) {
      throw responses.failure('Premise not found.');
    }

    // Check if user-premise pair exists
    const foundAccess = await AccessModel.findOne({
      uid: userId,
      premiseId,
    });

    console.log('foundAccess:', `${userId} and ${premiseId}`);
    console.log('foundAccess:', foundAccess);

    const response = {};

    // GRANT ACCESS
    if (access === 'grant') {
      if (!_.isEmpty(foundAccess)) {
        // Access record exists, check if it's deleted
        throw responses.failure('User already has access.');
      } else {
        // Create new access
        const newAccess = await AccessModel.create(
          [
            {
              uid: new ObjectId(user._id),
              premiseId: new ObjectId(premiseId),
              isDeleted: false,
              startWorkingTime: moment().set('hour', 8).set('minutes', 30),
              endWorkingTime: moment().set('hour', 17).set('minutes', 30),
              departments: [
                {
                  _id: mongoose.Types.ObjectId(
                    premiseDoc.departments[0]._id.toString()
                  ),
                  departmentId: 'main',
                  access: true,
                  type: 'toggle',
                },
              ],
            },
          ],
          { session }
        );

        // Create change log
        await ChangeLogsModel.create(
          [
            {
              userId: new ObjectId(user._id),
              premiseId: new ObjectId(premiseId),
              documentId: new ObjectId(newAccess[0]._id),
              isDeletedChange: false,
              adminId: foundAdmin._id,
              action: 'create_access',
            },
          ],
          { session }
        );

        _.set(response, 'data', {
          message: 'Access granted successfully',
          accessId: newAccess[0]._id,
        });
      }
    }
    // REVOKE ACCESS
    else if (access === 'revoke') {
      if (!_.isEmpty(foundAccess)) {
        // Revoke access by setting isDeleted to true
        await AccessModel.findByIdAndDelete(foundAccess._id, { session });

        // Create change log
        await ChangeLogsModel.create(
          [
            {
              userId: new ObjectId(userId),
              premiseId: new ObjectId(premiseId),
              documentId: new ObjectId(foundAccess._id),
              isDeletedChange: true,
              adminId: foundAdmin._id,
              action: 'revoke_access',
            },
          ],
          { session }
        );

        _.set(response, 'data', {
          message: 'Access revoked successfully',
          accessId: foundAccess._id,
        });
      } else {
        // No active access to revoke
        throw responses.failure('User does not have active access to revoke.');
      }
    }

    await session.commitTransaction();
    await session.endSession();

    await facepassServer.emit(premiseId, 'force-sync', {});
    return response;
  } catch (error) {
    await session.abortTransaction();
    await session.endSession();
    throw error;
  }
};

exports.searchUsersAccessByPremise = async (params) => {
  try {
    const { premiseId, q, limit = 10, page = 1 } = params;

    // Validate premise ID
    if (!premiseId) {
      throw responses.failure('Premise ID is required');
    }

    // Check if premise exists
    const premise = await PremiseModel.findById(premiseId);
    if (!premise) {
      throw responses.failure('Premise not found');
    }

    const filter = { premiseId };
    // const accesses = await AccessModel.find(filter).populate('uid').sort({ createdAt: -1 }).lean();
    const rawAccesses = await AccessModel.find(filter)
      .populate('uid')
      .sort({ createdAt: -1 })
      .lean();
    const accesses = rawAccesses.filter((a) => a.uid && !a.uid.isDeleted);
    let users = null;
    if (q) {
      console.log('accesses:', q);

      users = _.compact(
        _.map(accesses, (access) => {
          const regex = new RegExp(`.*${q}.*`, 'i');
          console.log('true:', access?.uid?.fullName);
          console.log('true:', q);
          console.log('true:', regex.test(q));
          if (
            access?.uid?._id.toString() === q ||
            regex.test(access?.uid?.fullName.toString())
          ) {
            const obj = {
              ...access,
              _id: access.uid?._id,
              user: {
                fullName: access.uid?.fullName,
                _id: access.uid?._id,
              },
            };

            _.unset(obj, 'uid');
            return obj;
          }
        })
      );
    } else {
      users = _.map(accesses, (access) => {
        const obj = {
          ...access,
          _id: access.uid?._id,
          user: {
            fullName: access.uid?.fullName,
            _id: access.uid?._id,
          },
        };

        _.unset(obj, 'uid');
        return obj;
      });
    }

    const paginatedUsers = users.slice((page - 1) * limit, page * limit);

    return {
      premise: {
        _id: premise._id,
        name: premise.name,
      },
      users: paginatedUsers,
      pagination: {
        totalItems: users.length,
        totalPages: Math.ceil(users.length / limit),
        limit,
        page,
      },
    };
  } catch (error) {
    console.log(error);
    throw responses.failure(error);
  }
};

exports.searchPremisesBelongToUser = async (params, apiKey) => {
  const { userId, q, limit = 10, page = 0 } = params;

  const user = await UserModel.findById(userId);

  const admin = await AdminApikeyModel.findOne({ apiKey });

  const adminPremises = _.map(admin.premises, (adminPremise) =>
    adminPremise.premiseId.toString()
  );

  const accesses = await AccessModel.find({
    uid: userId,
    premiseId: { $in: adminPremises },
  })
    .populate('premiseId')
    .lean();

  let premises = null;
  if (q) {
    premises = _.compact(
      _.map(accesses, (access) => {
        const regex = new RegExp(`.*${q}.*`, 'i');
        if (
          access?.premiseId?._id.toString() === q ||
          regex.test(access?.premiseId?.name.toString())
        ) {
          const obj = {
            ...access,
            _id: access.premiseId?._id,
            name: access.premiseId?.name,
          };

          _.unset(obj, 'uid');
          _.unset(obj, 'premiseId');
          return obj;
        }
      })
    );
  } else {
    premises = _.map(accesses, (access) => {
      const obj = {
        ...access,
        _id: access.premiseId?._id,
        name: access.premiseId?.name,
      };

      _.unset(obj, 'uid');
      _.unset(obj, 'premiseId');
      return obj;
    });
  }

  return {
    user: {
      _id: user._id,
      name: user.fullName,
    },
    premises,
    pagination: {
      total: premises.length,
      page,
      limit,
      pages: Math.ceil(premises.length / limit),
    },
  };
};
