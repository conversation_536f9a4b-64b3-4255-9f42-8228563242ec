const AdminModel = require('#models/Admin.model');
const UserModel = require('#models/User.model');
const _ = require('lodash');
const responses = require('#helpers/responses');
const bcrypt = require('bcrypt');
const PremiseModel = require('#models/Premise.model');

// DESCRIPTION
// main admin will be able to add a new admin by using an email, with a preset password.
// when the added admin wanted to login, they have to key in the email and password.
// the reason of not using google login or let the user sign up themselves due to
// only one admin own the premise. the rest of the admins are under that premise,
// thus not necessarily for every admin to sign up an account.
// the plaform admin will create/sign up for the owner of the premise.

const validateEmailPremiseAndAdmin = async (email, premiseId) => {
  const foundUser = await UserModel.findOne({ email });
  const foundPremiseWithinAdmin = await AdminModel.findOne({ email, 'premises.premiseId': premiseId });
  const foundPremise = await PremiseModel.findOne({ _id: premiseId });
  if (_.isEmpty(foundUser)) {
    throw responses.failure('User does not exist.');
  }
  if (_.isEmpty(foundPremise)) {
    throw responses.failure('Premise does not exist.');
  }
  if (_.isEmpty(foundPremiseWithinAdmin)) {
    throw responses.failure('Admin does exist in the premise.');
  }

  return { foundUser, foundPremise, foundPremiseWithinAdmin };
};

exports.addAdminToPremiseByEmail = async (params) => {
  const { email, password, premiseId } = params;

  // validation
  const { foundUser } = await validateEmailPremiseAndAdmin(email, premiseId);

  // hash password
  const salt = bcrypt.genSaltSync(10);
  const hashedPassword = bcrypt.hashSync(password, salt);

  // add the user to firebase.

  // created admin
  const createdAdmin = await AdminModel.findOneAndUpdate({
    email,
  }, {
    uid: foundUser.uid, // use firebase uid.
    email,
    password: hashedPassword,
    type: 'premise-admin',
    $push: {
      premises: {
        premiseId,
        position: 'admin',
      },
    },
  }, {
    upsert: true,
  });

  return createdAdmin;
};

exports.updateAdminInPremise = async (params) => {
  const {
    email, password, premiseId, premiseRole,
  } = params;

  // validation
  const { foundUser } = await validateEmailPremiseAndAdmin(email, premiseId);

  // hash password
  const salt = bcrypt.genSaltSync(10);
  const hashedPassword = bcrypt.hashSync(password, salt);

  // created admin
  const createdAdmin = await AdminModel.findOneAndUpdate({
    email,
    'premises.premiseId': premiseId,
  }, {
    uid: foundUser.uid,
    email,
    password: hashedPassword,
    type: 'premise-admin',
    $push: {
      premises: {
        premiseId,
        position: premiseRole,
      },
    },
  });

  return createdAdmin;
};

exports.getAllAdminsInPremise = async (params) => {
  const { } = params;

  return AdminModel.find();
};

exports.getAdminInPremise = async (params) => {
  const { uid } = params;
};
