const AdminModel = require('#models/Admin.model');
const PremiseModel = require('#models/Premise.model');
const _ = require('lodash');
const responses = require('#helpers/responses');
const DeviceModel = require('#models/Device.model');
const { default: mongoose } = require('mongoose');
const { response } = require('express');

exports.createDepartmentByPremiseId = async (params, user) => {
  const {
    premiseId,
    departmentName,
  } = params;

  // session
  const session = await mongoose.startSession();
  session.startTransaction();

  // validate premise
  const foundPremise = await PremiseModel.findOne(
    {
      premiseId,
    },
  ).session(session);
  const foundAdmin = await AdminModel.findOne(
    {
      uid: user.uid,
      'premises.premiseId': foundPremise?._id,
    },
  ).populate('premises.premiseId').session(session);
  if (_.isEmpty(foundAdmin)) {
    await session.abortTransaction();
    throw responses.failure(
      'This admin does not have access to this premise.',
    );
  }

  // create department
  const createdDepartment = await PremiseModel.findOneAndUpdate(
    {
      premiseId,
      'departments.departmentName': {
        $ne: departmentName,
      },
    },
    {
      $push: {
        departments: {
          departmentName,
        },
      },
    },
    {
      new: true,
      session,
    },
  );

  if (_.isEmpty(createdDepartment)) {
    await session.abortTransaction();
    throw responses.failure(
      'The department name is used in this premise.',
    );
  }
  await session.commitTransaction();
  await session.endSession();
  return createdDepartment;
};

exports.getDepartmentsByPremiseId = async (params, user) => {
  const {
    premiseId,
  } = params;
  const admin = await AdminModel.findOne(
    {
      uid: user.uid,
    },
  );
  if (_.isEmpty(admin)) {
    throw responses.failure('Admin not found');
  }
  // validate premise
  const foundPremise = await PremiseModel.findOne(
    {
      premiseId,
    },
  );
  if (_.isEmpty(foundPremise)) {
    throw responses.failure('Premise not found');
  }
  const foundAdmin = await AdminModel.findOne(
    {
      uid: user.uid,
      'premises.premiseId': foundPremise?._id,
    },
  ).populate('premises.premiseId');
  if (_.isEmpty(foundAdmin)) {
    throw responses.failure(
      'This admin does not have access to this premise.',
    );
  }

  if (_.isEmpty(foundPremise)) {
    throw responses.failure('Premise does not exist.');
  }

  return _.filter(foundPremise.departments, (dep) => !dep.isDeleted);
};

exports.getDepartmentByAPIKeyAndSerialNumber = async (params) => {
  const {
    apiKey,
    serialNumber,
  } = params;

  // validate device and premise
  const premise = await PremiseModel.findOne({ apiKey });
  if (_.isEmpty(premise)) {
    throw responses.failure('Invalid api key.');
  }
  const foundDevice = await DeviceModel.findOne({
    serialNumber,
    premiseId: premise._id,
  }).populate('departmentId');
  if (_.isEmpty(foundDevice)) {
    throw responses.failure('Serial number is not correct.');
  }

  const foundDepartment = _.find(premise.departments, { _id: foundDevice.departmentId, isDeleted: false });
  return foundDepartment;
};

exports.deleteDepartmentByPremiseId = async (params, user) => {
  const {
    premiseId,
    departmentId,
  } = params;
  const admin = await AdminModel.findOne(
    {
      uid: user.uid,
    },
  );
  if (_.isEmpty(admin)) {
    throw responses.failure('Admin not found');
  }
  // session
  const session = await mongoose.startSession();
  session.startTransaction();

  // validate premise
  const foundPremise = await PremiseModel.findOne(
    {
      premiseId,
    },
  ).session(session);
  if (_.isEmpty(foundPremise)) {
    throw responses.failure('Premise not found');
  }

  const foundAdmin = await AdminModel.findOne(
    {
      uid: user.uid,
      'premises.premiseId': foundPremise?._id,
    },
  ).populate('premises.premiseId').session(session);
  if (_.isEmpty(foundAdmin)) {
    await session.abortTransaction();
    throw responses.failure(
      'This admin does not have access to this premise.',
    );
  }

  // delete premise
  const deletedPremise = await PremiseModel.findOneAndUpdate(
    {
      premiseId,
      'departments._id': departmentId,
    },
    {
      $set: {
        'departments.$.isDeleted': true,
      },
    },
    {
      new: true,
      session,
    },
  );

  await session.commitTransaction();
  await session.endSession();
  return deletedPremise;
};
