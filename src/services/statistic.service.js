const PremiseModel = require('#models/Premise.model');
const DeviceModel = require('#models/Device.model');
const AccessModel = require('#models/Access.model');
const _ = require('lodash');
const responses = require('#helpers/responses');
const moment = require('moment');
const AccessRecordModel = require('#models/AccessRecord.model');

exports.getDashboardData = async (premiseId) => {
  const premiseData = await PremiseModel.findOne({
    premiseId,
  }).select(['-theme', '-public', '-apiKey', '-childPremises']).lean();
  if (_.isEmpty(premiseData)) {
    throw responses.failure('Premise not found');
  }
  const deviceData = await DeviceModel.find({
    premiseId,
  }).select(['name']).lean();

  const accessData = await AccessModel.find({
    premiseId,
    isDeleted: false,
  }).populate('uid').select(['-premiseId']).lean();

  const userList = _.map(accessData, (access) => access.uid);

  return {
    premiseId,
    premiseName: premiseData.name,
    totalUsers: accessData.length,
    totalDepartments: premiseData.departments.length,
    totalDevices: deviceData.length,
    userList,
  };
};

exports.getAttendanceRates = async (params) => {
  const { premiseId } = params;

  try {
    const todayStart = moment().tz('Asia/Singapore').startOf('day');
    const todayEnd = moment().tz('Asia/Singapore').endOf('day');
    const user = await AccessModel.find({ premiseId, isDeleted: false });
    const userNumber = _.size(user);
    const todayAccess = await AccessRecordModel.find({ premiseId, createdAt: { $gte: todayStart, $lte: todayEnd }, isDeleted: false });
    const uniqueTodayAccess = _.uniqBy(todayAccess, (obj) => _.toString(_.get(obj, 'user')));
    const todayAccessNumber = _.size(uniqueTodayAccess);

    const attendanceRate = (todayAccessNumber / userNumber) * 100.0;
    return attendanceRate;
  } catch (e) {
    throw responses.failure('getAttendanceRates error:', e);
  }
};

exports.getScanStatistic = async (params) => {
  const { premiseId, startTime, endTime } = params;

  try {
    const todayAccess = await AccessRecordModel.find({ premiseId, createdAt: { $gte: startTime, $lte: endTime }, isDeleted: false });
    const uniqueTodayAccess = _.groupBy(todayAccess, 'type');
    const uniqueTodayAccessSize = _.mapValues(uniqueTodayAccess, (group) => group.length);
    const attendanceRate = (uniqueTodayAccessSize);
    return attendanceRate;
  } catch (e) {
    throw responses.failure('getAttendanceRates error:', e);
  }
};
