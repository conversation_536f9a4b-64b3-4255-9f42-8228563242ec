/* eslint-disable no-restricted-syntax */
/* eslint-disable no-await-in-loop */
/* eslint-disable no-throw-literal */
const moment = require('moment');
const _ = require('lodash');

const firebaseAdmin = require('firebase-admin');

const User = require('#models/User.model');
const { uploadImage } = require('#helpers/index');
const { readFileSync } = require('fs');
const responses = require('#helpers/responses');
const { default: mongoose } = require('mongoose');
const AccessModel = require('#models/Access.model');
const { v4: uuid } = require('uuid');
const AdminModel = require('#models/Admin.model');
const { ObjectId } = require('mongodb');
const PremiseModel = require('#models/Premise.model');
const AccessRecordModel = require('#models/AccessRecord.model');
const DeviceUsersModel = require('#models/DeviceUsers.model');
const UserModel = require('#models/User.model');
const AdminApikeyModel = require('#models/AdminApikey.model');
const { uploadToGCS } = require('#helpers/uploadData');
const ChangeLogsModel = require('#models/ChangeLogs.model');
const ExcelJS = require('exceljs');
const { default: axios } = require('axios');
const readXlsxFile = require('read-excel-file/node');
const DeviceModel = require('#models/Device.model');
const {
  addFace,
  deleteFace,
} = require('#config/websocket');

exports.fetchList = async (params) => {
  const { fetchType } = params;

  const premiseList = [];
  const premiseKeyName = {};
};

// TO DO LIST
// by default user will have access to all the device which is a security concern.
exports.createUserByAdmin = (params, userObj) => async (facepassServer) => {
  const {
    phoneNumber, fullName, imageObj, premiseId, NRIC, passportNumber, gender, role, accessEndTime, accessStartTime,
  } = params;

  const imageBuffer = readFileSync(_.get(imageObj, 'path', ''));
  const faceImageUrl = await uploadImage(imageBuffer, 'faces');

  // check if premise is under the admin.
  const foundAdmin = await AdminModel.findOne({
    uid: userObj.uid,
  }).lean();
  const foundPremise = _.find(foundAdmin.premises, (premise) => { if (premise.premiseId.toString() === premiseId) return premise; });
  // console.log('found Premise', foundPremise);
  if (foundAdmin.type !== 'platform-admin') {
    if (_.isEmpty(foundPremise)) {
      throw responses.failure('You have no access to this premise.');
    }
  }

  // fake data population
  //-------------------------------------------------------------
  const premise = await PremiseModel.findOne({ _id: premiseId });
  console.log('premise:', premise);
  const uid = uuid();
  const adminId = 'spo-admin';
  //-------------------------------------------------------------

  const session = await mongoose.startSession();
  let user = null;
  await session.withTransaction(async () => {
    // if (!_.isEmpty(foundUsers)) {
    //   throw ' This user is found, Please try a new phone number or email to continue';
    // } else {
    user = await User.create([{
      phoneNumber,
      fullName,
      faceImageUrl,
      createdBy: 'admin',
      from: adminId,
      NRIC,
      passportNumber,
      gender,
      uid,
    }], { session });
    await AccessModel.create([{
      uid: new ObjectId(_.get(user, '[0]._id')),
      premiseId: new ObjectId(premiseId),
      isDeleted: false,
      ...(accessStartTime && { accessStartTime }),
      ...(accessEndTime && { accessEndTime }),

      startWorkingTime: moment().set('hour', 8).set('minutes', 0),
      endWorkingTime: moment().set('hour', 17).set('minutes', 0),
      departments: [{
        _id: mongoose.Types.ObjectId(premise.departments[0]._id.toString()),
        departmentId: 'main',
        role,
        access: true,
        type: 'toggle',
      }],
    }], { session });
    // }
  });

  const foundUser = {
    fullName: _.get(user, '[0].fullName', ''),
    faceImageUrl: _.get(user, '[0].faceImageUrl', ''),
    isDeleted: 'false',
    _id: _.get(user, '[0]._id', ''),
    __v: 0,
  };

  try {
    await facepassServer.emit(premiseId, 'addFace', foundUser);
    await addFace(premiseId, foundUser);
  } catch (err) {
    console.log(err);
  }

  return 'OK';
};

exports.createUserWithoutPremise = async (params, key, facepassServer) => {
  const {
    phoneNumber, fullName, imageObj, premiseId,
  } = params;

  const imageBuffer = readFileSync(_.get(imageObj, 'path', ''));
  const faceImageUrl = await uploadImage(imageBuffer, 'faces');

  const uid = uuid();
  const adminId = 'spo-admin';

  const user = await User.create({
    phoneNumber,
    fullName,
    faceImageUrl,
    createdBy: premiseId,
    from: adminId,
    uid,
  });

  const facepassResult = await facepassServer.emitToDevice('64e6c0100703004f6d5430af', 'QV3041', 'check-face', { faceImageUrl }).then(async (res) => {
    await User.findOneAndUpdate({ _id: user.toObject()._id.toString() }, { facialPhotoStatus: 'passed', facialPhotoFailedReason: '' });
    return { ...user.toObject(), facialPhotoStatus: 'passed', facialPhotoFailedReason: '' };
  }).catch(async (err) => {
    console.log('the user is not validated:', err);
    if (_.includes(err.toString(), 'timed out')) {
      await User.findOneAndUpdate({ _id: user.toObject()._id.toString() }, { facialPhotoStatus: 'failed', facialPhotoFailedReason: 'timed out' });
      throw responses.failure('time out');
    }

    // await User.deleteOne({ _id: user[0]._id });
    await User.findOneAndUpdate({ _id: user.toObject()._id.toString() }, { facialPhotoStatus: 'failed', facialPhotoFailedReason: err.toString() });
    return {
      ...user.toObject(),
      facialPhotoStatus: 'failed',
      facialPhotoFailedReason: err.toString(),
    };
  });

  return facepassResult;
};

exports.createUserByApiKey = (params, key) => async (facepassServer) => {
  const {
    phoneNumber, fullName, imageObj, premiseId,
  } = params;

  // console.log('userObj', userObj);
  const imageBuffer = readFileSync(_.get(imageObj, 'path', ''));
  const faceImageUrl = await uploadImage(imageBuffer, 'faces');

  // check if premise is under the apiKey.
  const foundKey = await AdminApikeyModel.findOne({
    apiKey: key,
  }).lean();
  const foundPremise = _.find(foundKey.premises, (premise) => { if (premise.premiseId.toString() === premiseId) return premise; });
  console.log('found Premise', foundPremise);
  if (_.isEmpty(foundPremise)) {
    throw responses.failure('You have no access to this premise.');
  }

  // fake data population
  //-------------------------------------------------------------
  const premise = await PremiseModel.findOne({ _id: premiseId });
  console.log('premise:', premise);
  const uid = uuid();
  const adminId = 'spo-admin';
  //-------------------------------------------------------------

  const session = await mongoose.startSession();
  let user = null;
  await session.withTransaction(async () => {
    // if (!_.isEmpty(foundUsers)) {
    //   throw ' This user is found, Please try a new phone number or email to continue';
    // } else {
    user = await User.create([{
      phoneNumber,
      fullName,
      faceImageUrl,
      createdBy: 'admin',
      from: adminId,
      uid,
    }], { session });

    await AccessModel.create([{
      uid: new ObjectId(_.get(user, '[0]._id')),
      premiseId: new ObjectId(premiseId),
      isDeleted: false,
      startWorkingTime: moment().set('hour', 8).set('minutes', 0),
      endWorkingTime: moment().set('hour', 17).set('minutes', 0),
      departments: [{
        _id: mongoose.Types.ObjectId(premise.departments[0]._id.toString()),
        departmentId: 'main',
        access: true,
        type: 'toggle',
      }],
    }], { session });
    // }
  });

  const foundUser = {
    fullName: _.get(user, '[0].fullName', ''),
    faceImageUrl: _.get(user, '[0].faceImageUrl', ''),
    isDeleted: 'false',
    _id: _.get(user, '[0]._id', ''),
    __v: 0,
  };

  try {
    console.log('THIS IS THE PREMISE ID 10NOV: ', premiseId);
    await facepassServer.emit(premiseId, 'addFace', foundUser);
    await addFace(premiseId, foundUser);
  } catch (err) {
    console.log('facepassServer', err);
    throw responses.failure('Emit failed', err);
  }

  return user;
};

exports.createUserToPremiseByApiKey = (params, key) => async (facepassServer) => {
  const {
    phoneNumber, fullName, imageObj, premiseToAdd, foundKey,
  } = params;

  // console.log('userObj', userObj);
  const imageBuffer = readFileSync(_.get(imageObj, 'path', ''));
  const faceImageUrl = await uploadImage(imageBuffer, 'faces');

  const getPremiseId = (await PremiseModel.findOne({ premiseId: premiseToAdd }).lean())._id;
  const foundPremise = _.find(foundKey.premises, (premise) => { if (premise.premiseId.toString() === getPremiseId.toString()) return premise; });
  console.log('found Premise', foundPremise);
  if (_.isEmpty(foundPremise)) {
    throw responses.failure('You have no access to this premise.');
  }

  // fake data population
  //-------------------------------------------------------------
  const premise = await PremiseModel.findOne({ _id: getPremiseId });
  console.log('premise:', premise);
  const uid = uuid();
  const adminId = 'spo-admin';
  //-------------------------------------------------------------

  const session = await mongoose.startSession();
  let user = null;
  await session.withTransaction(async () => {
    user = await User.create([{
      phoneNumber,
      fullName,
      faceImageUrl,
      createdBy: 'admin',
      from: adminId,
      uid,
    }], { session });

    await AccessModel.create([{
      uid: new ObjectId(_.get(user, '[0]._id')),
      premiseId: new ObjectId(getPremiseId),
      isDeleted: false,
      startWorkingTime: moment().set('hour', 8).set('minutes', 0),
      endWorkingTime: moment().set('hour', 17).set('minutes', 0),
      departments: [{
        _id: mongoose.Types.ObjectId(premise.departments[0]._id.toString()),
        departmentId: 'main',
        access: true,
        type: 'toggle',
      }],
    }], { session });
    // }
  });

  const foundUser = {
    fullName: _.get(user, '[0].fullName', ''),
    faceImageUrl: _.get(user, '[0].faceImageUrl', ''),
    isDeleted: 'false',
    _id: _.get(user, '[0]._id', ''),
    __v: 0,
  };

  try {
    const serialNumbers = await DeviceModel.find({ premiseId: getPremiseId });
    _.map(serialNumbers, async (sn) => {
      await facepassServer.emitToDevice(getPremiseId, sn.serialNumber, 'force-sync', null);
      await addFace(premise._id, foundUser);
    });
  } catch (err) {
    console.log(err);
  }

  return user;
};

exports.getUser = async (params) => {
  const { userId, premiseId, showNoPremiseUser } = params;

  if (!showNoPremiseUser) {
    const foundAccess = await AccessModel.findOne({
      uid: userId,
      premiseId,
      isDeleted: false,
    });
    if (_.isEmpty(foundAccess)) {
      throw responses.failure('User not found in this premise.');
    }
    console.log(foundAccess);
  }
  const user = await User.findById(userId);
  return user;
};

exports.getAllUsers = async () => {
  const users = await User.find();

  return users;
};

exports.deleteUser = async (params, admin) => {
  const {
    userId,
    premiseId,
  } = params;
  // validate if admin has access to the premise.
  const foundAdmin = await AdminModel.findOne({
    uid: admin.uid,
  }).lean();
  const foundPremise = _.find(foundAdmin.premises, (premise) => { if (premise.premiseId.toString() === premiseId) return premise; });
  if (_.isEmpty(foundPremise)) {
    throw responses.failure('You have no access to this premise.');
  }

  // validate if user is in the premise.
  const foundAccess = await AccessModel.findOne({
    uid: userId,
    premiseId,
  });
  if (_.isEmpty(foundAccess)) {
    throw responses.failure('User not found in this premise.');
  }

  // delete user.
  const deletedUser = await User.findByIdAndUpdate(userId, {
    isDeleted: true,
  }, { new: true });

  await deleteFace(premiseId, deletedUser);
  return deletedUser;
};

exports.deleteUserUpdateAccess = async (params, admin) => {
  const {
    userId,
    premiseId,
  } = params;
  // validate if admin has access to the premise.
  const foundAdmin = await AdminModel.findOne({
    uid: admin.uid,
  }).lean();
  const foundPremise = _.find(foundAdmin.premises, (premise) => { if (premise.premiseId.toString() === premiseId) return premise; });
  if (_.isEmpty(foundPremise)) {
    throw responses.failure('You have no access to this premise.');
  }
  // validate if user is in the premise.
  const foundAccess = await AccessModel.findOne({
    uid: userId,
    premiseId,
  });
  if (_.isEmpty(foundAccess)) {
    throw responses.failure('User not found in this premise.');
  } else {
    console.log(foundAccess);
  }
  const session = await mongoose.startSession();
  session.startTransaction();
  try {
    const deletedUser = await User.findByIdAndUpdate(userId, {
      isDeleted: true,
    }, { new: true }).session(session);
    const removeAccess = await foundAccess.update({ isDeleted: true }, { new: true }).session(session);
    await deleteFace(premiseId, deletedUser);
    await session.commitTransaction();
    await session.endSession();
  } catch (error) {
    await session.abortTransaction();
    await session.endSession();
    throw responses.failure(error);
  }
  return 'done';
};

exports.updateUserAccess = async (params) => {
  const {
    userId, premiseId, type, access, devices,
  } = params;

  // TO DO LIST
};

exports.fetchUserListByPremiseId = async (params) => {
  const {
    premiseId,
    page,
    limit,
    searchText,
  } = params;
  console.log(params);

  // First, get the total count for pagination
  const totalCount = await AccessModel.aggregate([
    {
      $match: {
        premiseId: mongoose.Types.ObjectId(premiseId),
        isDeleted: false,
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'uid',
        foreignField: '_id',
        as: 'users',
      },
    },
    {
      $match: {
        'users.isDeleted': false,
        $or: [
          {
            'premises.name': {
              $regex: _.isEmpty(searchText) ? '' : searchText,
              $options: 'i',
            },
          },
          {
            'users.fullName': {
              $regex: _.isEmpty(searchText) ? '' : searchText,
              $options: 'i',
            },
          },
        ],
      },
    },
    {
      $count: 'total',
    },
  ]);

  const totalPages = Math.ceil((totalCount[0]?.total || 0) / limit);

  const usersAccesses = await AccessModel.aggregate([
    {
      $match: {
        premiseId: mongoose.Types.ObjectId(premiseId),
        // 'departments.0.role': 'visitor',
        isDeleted: false,
      },
    },
    {
      $lookup:
      {
        from: 'premises',
        localField: 'premiseId',
        foreignField: '_id',
        as: 'premises',
      },
    },
    {
      $lookup:
      {
        from: 'users',
        localField: 'uid',
        foreignField: '_id',
        as: 'users',
      },
    },
    {
      $match: {
        $or: [
          {
            'premises.name': {
              $regex: _.isEmpty(searchText) ? '' : searchText,
              $options: 'i',
            },
          },
          {
            'users.fullName': {
              $regex: _.isEmpty(searchText) ? '' : searchText,
              $options: 'i',
            },
          },
        ],
      },
    },
    {
      $project: {
        premise: { $arrayElemAt: ['$premises', 0] },
        user: { $arrayElemAt: ['$users', 0] },
        departments: 1,
        accessStartTime: 1,
        accessEndTime: 1,
      },
    },
    {
      $project: {
        'premise.apiKey': 0,
        'premise.theme': 0,
        'premise.childPremises': 0,
        'premise.employees': 0,
      },
    },
    {
      $match: {
        'user.isDeleted': false,
      },
    },
    {
      $skip: page * limit,
    },
    {
      $limit: limit,
    },
  ]);

  // populate departments data into access.
  const newUserAccesses = _.flatten(_.map(usersAccesses, (userAccess) => {
    return _.map(
      userAccess.departments,
      (userDepartment) => {
        const foundDepartment = _.find(
          userAccess.premise.departments,
          { _id: userDepartment._id },
        );
        userAccess.user.role = userAccess.departments[0].role || '';
        userAccess.user.accessStartTime = userAccess.accessStartTime || '';
        userAccess.user.accessEndTime = userAccess.accessEndTime || '';

        const formattedData = {
          department: foundDepartment,
          ...userAccess,
        };
        _.unset(formattedData, 'departments');
        _.unset(formattedData, 'premise.departments');
        _.unset(formattedData, 'department.isDeleted');
        return formattedData;
      },
    );
  }));

  // combined the user access with the records.
  const usersAccessRecords = await Promise.all(_.map(
    newUserAccesses,
    async (userAccess) => {
      const premiseAccessRecords = await AccessRecordModel.find({
        user: userAccess.user._id,
      });
      return {
        ...userAccess,
        accessRecords: premiseAccessRecords,
      };
    },
  ));

  return {
    data: usersAccessRecords,
    totalPages,
    currentPage: page,
    totalCount: totalCount[0]?.total || 0,
  };
};

exports.fetchVisitorListByPremiseId = async (params) => {
  const {
    premiseId,
    page,
    limit,
    searchText,
  } = params;
  console.log(params);
  const usersAccesses = await AccessModel.aggregate([
    {
      $match: {
        premiseId: mongoose.Types.ObjectId(premiseId),
        'departments.0.role': 'visitor',
        isDeleted: false,
      },
    },
    {
      $lookup:
      {
        from: 'premises',
        localField: 'premiseId',
        foreignField: '_id',
        as: 'premises',
      },
    },
    {
      $lookup:
      {
        from: 'users',
        localField: 'uid',
        foreignField: '_id',
        as: 'users',
      },
    },
    {
      $match: {
        $or: [
          {
            'premises.name': {
              $regex: _.isEmpty(searchText) ? '' : searchText,
              $options: 'i',
            },
          },
          {
            'users.fullName': {
              $regex: _.isEmpty(searchText) ? '' : searchText,
              $options: 'i',
            },
          },
        ],
      },
    },
    {
      $project: {
        premise: { $arrayElemAt: ['$premises', 0] },
        user: { $arrayElemAt: ['$users', 0] },
        departments: 1,
        accessStartTime: 1,
        accessEndTime: 1,
      },
    },
    {
      $project: {
        'premise.apiKey': 0,
        'premise.theme': 0,
        'premise.childPremises': 0,
        'premise.employees': 0,
      },
    },
    {
      $match: {
        'user.isDeleted': false,
      },
    },
    {
      $skip: page * limit,
    },
    {
      $limit: limit,
    },
  ]);

  console.log('accesses:', usersAccesses);

  // populate departments data into access.
  const newUserAccesses = _.flatten(_.map(usersAccesses, (userAccess) => {
    return _.map(
      userAccess.departments,
      (userDepartment) => {
        const foundDepartment = _.find(
          userAccess.premise.departments,
          { _id: userDepartment._id },
        );
        userAccess.user.role = userAccess.departments[0].role || '';
        userAccess.user.accessStartTime = userAccess.accessStartTime || '';
        userAccess.user.accessEndTime = userAccess.accessEndTime || '';

        const formattedData = {
          department: foundDepartment,
          ...userAccess,
        };
        _.unset(formattedData, 'departments');
        _.unset(formattedData, 'premise.departments');
        _.unset(formattedData, 'department.isDeleted');
        return formattedData;
      },
    );
  }));

  // combined the user access with the records.
  const usersAccessRecords = await Promise.all(_.map(
    newUserAccesses,
    async (userAccess) => {
      console.log('user access:', userAccess.user);
      const premiseAccessRecords = await AccessRecordModel.find({
        user: userAccess.user._id,
      });
      return {
        ...userAccess,
        accessRecords: premiseAccessRecords,
      };
    },
  ));

  return usersAccessRecords;
};

exports.fetchEmployeeListByPremiseId = async (params) => {
  const {
    premiseId,
    page,
    limit,
    searchText,
  } = params;
  console.log(params);
  const usersAccesses = await AccessModel.aggregate([
    {
      $match: {
        premiseId: mongoose.Types.ObjectId(premiseId),
        'departments.0.role': 'employee',
        isDeleted: false,
      },
    },
    {
      $lookup:
      {
        from: 'premises',
        localField: 'premiseId',
        foreignField: '_id',
        as: 'premises',
      },
    },
    {
      $lookup:
      {
        from: 'users',
        localField: 'uid',
        foreignField: '_id',
        as: 'users',
      },
    },
    {
      $match: {
        $or: [
          {
            'premises.name': {
              $regex: _.isEmpty(searchText) ? '' : searchText,
              $options: 'i',
            },
          },
          {
            'users.fullName': {
              $regex: _.isEmpty(searchText) ? '' : searchText,
              $options: 'i',
            },
          },
        ],
      },
    },
    {
      $project: {
        premise: { $arrayElemAt: ['$premises', 0] },
        user: { $arrayElemAt: ['$users', 0] },
        departments: 1,
        accessStartTime: 1,
        accessEndTime: 1,
      },
    },
    {
      $project: {
        'premise.apiKey': 0,
        'premise.theme': 0,
        'premise.childPremises': 0,
        'premise.employees': 0,
      },
    },
    {
      $match: {
        'user.isDeleted': false,
      },
    },
    {
      $skip: page * limit,
    },
    {
      $limit: limit,
    },
  ]);

  console.log('accesses:', usersAccesses);

  // populate departments data into access.
  const newUserAccesses = _.flatten(_.map(usersAccesses, (userAccess) => {
    return _.map(
      userAccess.departments,
      (userDepartment) => {
        const foundDepartment = _.find(
          userAccess.premise.departments,
          { _id: userDepartment._id },
        );
        userAccess.user.role = userAccess.departments[0].role || '';
        userAccess.user.accessStartTime = userAccess.accessStartTime || '';
        userAccess.user.accessEndTime = userAccess.accessEndTime || '';

        const formattedData = {
          department: foundDepartment,
          ...userAccess,
        };
        _.unset(formattedData, 'departments');
        _.unset(formattedData, 'premise.departments');
        _.unset(formattedData, 'department.isDeleted');
        return formattedData;
      },
    );
  }));

  // combined the user access with the records.
  const usersAccessRecords = await Promise.all(_.map(
    newUserAccesses,
    async (userAccess) => {
      console.log('user access:', userAccess.user);
      const premiseAccessRecords = await AccessRecordModel.find({
        user: userAccess.user._id,
      });
      return {
        ...userAccess,
        accessRecords: premiseAccessRecords,
      };
    },
  ));

  return usersAccessRecords;
};

exports.updateUser = async (params, admin) => {
  const {
    fullName,
    phoneNumber,
    userId,
    premiseId,
    faceImageUrl,
    NRIC, passportNumber, gender, role,
    accessStartTime,
    accessEndTime,
  } = params;

  // validate if admin has access to the premise.
  // TO DO LIST
  // update user face image.

  const foundAdmin = await AdminModel.findOne({
    uid: admin.uid,
  }).lean();
  if (foundAdmin.type !== 'platform-admin') {
    const foundPremise = _.find(foundAdmin.premises, (premise) => { if (premise.premiseId.toString() === premiseId) return premise; });
    if (_.isEmpty(foundPremise)) {
      throw responses.failure('You have no access to this premise.');
    }
  }
  const foundAccess = await AccessModel.findOne({
    uid: userId,
    premiseId,
    isDeleted: false,
  });

  if (_.isEmpty(foundAccess)) {
    throw responses.failure('User not found in this premise.');
  }
  const session = await mongoose.startSession();
  await session.startTransaction();
  try {
    const updatedUser = await UserModel.findOneAndUpdate(
      {
        _id: userId,
      },
      {
        fullName,
        phoneNumber,
        ...(NRIC && { NRIC }),
        ...(gender && { gender }),
        ...(passportNumber && { passportNumber }),
        faceImageUrl,
      },
      {
        new: true, session,
      },
    );
  } catch (e) {
    await session.abortTransaction();
    throw responses.failure(e);
  }

  if (!_.isEmpty(role) && _.toString(role) !== 'undefined') {
    try {
      const updatedAccess = await AccessModel.findOneAndUpdate({
        uid: userId,
      }, {
        ...(accessStartTime && { accessStartTime }),
        ...(accessEndTime && { accessEndTime }),
        $set: { 'departments.0.role': role },
      }, { session });
      console.log('updatedAccess', updatedAccess);
    } catch (e) {
      await session.abortTransaction();
      throw responses.failure(e);
    }
  }
  await session.commitTransaction();
  await session.endSession();
  return 'User updated';
};

exports.updateUserByApiKeyWithoutPremise = async (params, key, facepassServer) => {
  const {
    fullName,
    phoneNumber,
    userId,
    faceImageUrl,
  } = params;

  const foundKey = await AdminApikeyModel.findOne({
    apiKey: key,
  }).lean();

  if (_.isEmpty(foundKey)) {
    throw responses.failure('You have no access to this premise.');
  }

  // check if user is deleted.
  const foundDeletedUser = await User.findOne({ _id: userId, isDeleted: true });
  console.log('found deleted user:', foundDeletedUser);
  if (!_.isEmpty(foundDeletedUser)) {
    throw responses.failure('The user not found.');
  }

  const updateField = {
    fullName,
    phoneNumber,
  };

  console.log('image path:', faceImageUrl);

  if (!_.isEmpty(faceImageUrl)) {
    _.set(updateField, 'faceImageUrl', faceImageUrl);
  }

  console.log('update field:', updateField);

  const updatedUser = await UserModel.findOneAndUpdate(
    {
      _id: userId,
    },
    updateField,
    {
      new: true,
    },
  ).lean();

  const facepassResult = await facepassServer.emitToDevice('64e6c0100703004f6d5430af', 'QV3041', 'check-face', { faceImageUrl }).then(async (res) => {
    await User.findOneAndUpdate({ _id: updatedUser._id.toString() }, { facialPhotoStatus: 'passed', facialPhotoFailedReason: '' });
    return { ...updatedUser, facialPhotoStatus: 'passed', facialPhotoFailedReason: '' };
  }).catch(async (err) => {
    console.log('the user is not validated:', err);
    if (_.includes(err.toString(), 'timed out')) {
      await User.findOneAndUpdate({ _id: updatedUser._id.toString() }, { facialPhotoStatus: 'failed', facialPhotoFailedReason: 'timed out' });
      throw responses.failure('the user is not validated due to time out.');
    }

    await User.findOneAndUpdate({ _id: updatedUser._id.toString() }, { facialPhotoStatus: 'failed', facialPhotoFailedReason: err.toString() });

    return {
      ...updatedUser,
      facialPhotoStatus: 'failed',
      facialPhotoFailedReason: err.toString(),
    };
  });

  return facepassResult;
};

exports.updateUserByApiKey = (params, key) => async (facepassServer) => {
  const {
    fullName,
    phoneNumber,
    userId,
    premiseId,
    faceImageUrl,
  } = params;

  const foundKey = await AdminApikeyModel.findOne({
    apiKey: key,
  }).lean();
  console.log('foundkey,', foundKey);
  const foundPremise = _.find(foundKey.premises, (premise) => { if (premise.premiseId.toString() === premiseId) return premise; });
  if (_.isEmpty(foundPremise)) {
    throw responses.failure('You have no access to this premise.');
  }
  const foundAccess = await AccessModel.findOne({
    uid: userId,
    premiseId,
  });

  if (_.isEmpty(foundAccess)) {
    throw responses.failure('User not found in this premise.');
  }

  console.log('faceImageUrl', faceImageUrl);

  const updateField = {
    fullName,
    phoneNumber,
  };

  console.log('image path:', faceImageUrl);

  if (!_.isEmpty(faceImageUrl)) {
    _.set(updateField, 'faceImageUrl', faceImageUrl);
  }

  console.log('update field:', updateField);

  const updatedUser = await UserModel.findOneAndUpdate(
    {
      _id: userId,
    },
    updateField,
    {
      new: true,
    },
  );

  return updatedUser;
};

exports.deleteUserWithAdminKey = async (params, key) => {
  const {
    userId,
    premiseId,
  } = params;
  // validate if admin has access to the premise.
  const foundAdmin = await AdminApikeyModel.findOne({
    apiKey: key,
  }).lean();
  const foundPremise = _.find(foundAdmin.premises, (premise) => { if (premise.premiseId.toString() === premiseId) return premise; });
  if (_.isEmpty(foundPremise)) {
    throw responses.failure('You have no access to this premise.');
  }
  // validate if user is in the premise.
  const foundAccess = await AccessModel.findOne({
    uid: userId,
    premiseId,
  });
  if (_.isEmpty(foundAccess)) {
    throw responses.failure('User not found in this premise.');
  }
  // delete user.
  const deletedUser = await User.findByIdAndUpdate(userId, {
    isDeleted: true,
  }, { new: true });
  await deleteFace(premiseId, deletedUser);
  return deletedUser;
};

exports.fetchUserList = async (params) => {
  // return 'this endpoint is still in progress';
  const {
    premiseId, q, premiseIds, limit = 5, page = 0,
  } = params;
  try {
    const allPremiseUserAccess = await Promise.all(_.map(premiseIds, async (pId) => {
      const usersAccesses = await AccessModel.aggregate([
        {
          $match: {
            premiseId: mongoose.Types.ObjectId(pId),
            isDeleted: false,
          },
        },
        {
          $lookup: {
            from: 'premises',
            localField: 'premiseId',
            foreignField: '_id',
            as: 'premises',
          },
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'uid',
            foreignField: '_id',
            as: 'users',
          },
        },
        // {
        //   $match: {
        //     premiseId: q,
        //   },
        // },
        {
          $project: {
            premise: { $arrayElemAt: ['$premises', 0] },
            user: { $arrayElemAt: ['$users', 0] },
            departments: 1,
            accessStartTime: 1,
            accessEndTime: 1,
          },
        },
        { $skip: limit * page },
        { $limit: limit },
      ]);
      const users = _.map(usersAccesses, (ua) => ua.user);
      return {
        premise: usersAccesses[0]?.premise,
        users,
      };
    }));

    if (!_.isEmpty(premiseIds)) {
      return allPremiseUserAccess;
    }

    const usersAccesses = await AccessModel.aggregate([
      {
        $match: {
          premiseId: mongoose.Types.ObjectId(premiseId),
          isDeleted: false,
        },
      },
      {
        $lookup: {
          from: 'premises',
          localField: 'premiseId',
          foreignField: '_id',
          as: 'premises',
        },
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'uid',
          foreignField: '_id',
          as: 'users',
        },
      },
      {
        $project: {
          premise: { $arrayElemAt: ['$premises', 0] },
          user: { $arrayElemAt: ['$users', 0] },
          departments: 1,
          accessStartTime: 1,
          accessEndTime: 1,
        },
      },
    ]);

    const users = _.map(usersAccesses, (userAccess) => userAccess.user);

    return {
      premise: usersAccesses[0]?.premise,
      users,
    };
  } catch (error) {
    throw responses.failure(error);
  }
};

exports.exportUserListByPremiseId = async (params) => {
  const {
    premiseId,
    role,
    idArray,
  } = params;
  let match = {
    premiseId: mongoose.Types.ObjectId(premiseId),

    isDeleted: false,
  };
  if (!_.isEmpty(role) && !_.isEmpty(idArray)) {
    match = {
      premiseId: mongoose.Types.ObjectId(premiseId),
      'departments.0.role': role,
      uid: { $in: idArray },
      isDeleted: false,
    };
  } else if (!_.isEmpty(role) && _.isEmpty(idArray)) {
    match = {
      premiseId: mongoose.Types.ObjectId(premiseId),
      'departments.0.role': role,
      isDeleted: false,
    };
  } else if (!_.isEmpty(idArray) && _.isEmpty(role)) {
    match = {
      premiseId: mongoose.Types.ObjectId(premiseId),
      uid: { $in: idArray },
      isDeleted: false,
    };
  }
  console.log(role);
  console.log(match);
  const usersAccesses = await AccessModel.aggregate([
    {
      $match: match,

    },
    {
      $lookup:
      {
        from: 'premises',
        localField: 'premiseId',
        foreignField: '_id',
        as: 'premises',
      },
    },
    {
      $lookup:
      {
        from: 'users',
        localField: 'uid',
        foreignField: '_id',
        as: 'users',
      },
    },

    {
      $project: {
        premise: { $arrayElemAt: ['$premises', 0] },
        user: { $arrayElemAt: ['$users', 0] },
        departments: 1,
        accessStartTime: 1,
        accessEndTime: 1,
      },
    },
    {
      $project: {
        'premise.apiKey': 0,
        'premise.theme': 0,
        'premise.childPremises': 0,
        'premise.employees': 0,
      },
    },
    {
      $match: {
        'user.isDeleted': false,
      },
    },

  ]);

  console.log('accesses:', usersAccesses);

  // populate departments data into access.
  const newUserAccesses = _.flatten(_.map(usersAccesses, (userAccess) => {
    return _.map(
      userAccess.departments,
      (userDepartment) => {
        const foundDepartment = _.find(
          userAccess.premise.departments,
          { _id: userDepartment._id },
        );
        userAccess.user.role = userAccess.departments[0].role || '';
        userAccess.user.accessStartTime = userAccess.accessStartTime || '';
        userAccess.user.accessEndTime = userAccess.accessEndTime || '';

        const formattedData = {
          department: foundDepartment,
          ...userAccess,
        };
        _.unset(formattedData, 'departments');
        _.unset(formattedData, 'premise.departments');
        _.unset(formattedData, 'department.isDeleted');
        return formattedData;
      },
    );
  }));

  // combined the user access with the records.
  const usersAccessRecords = await Promise.all(_.map(
    newUserAccesses,
    async (userAccess) => {
      // console.log('user access:', userAccess.user);
      // const premiseAccessRecords = await AccessRecordModel.find({
      //   user: userAccess.user._id,
      // });
      return {
        ...userAccess,
        // accessRecords: premiseAccessRecords,
      };
    },
  ));

  // return usersAccessRecords;
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Employee_name', {
    pageSetup: { paperSize: 9, orientation: 'landscape' },
  });
  /* Column headers */
  worksheet.getRow(1).values = [
    '',
    'Full Name',
    'Phone Number',
    'NRIC',
    'Gender',
    'Role',
    'Department',
    'Premise',
  ];
  const worksheetData = _.map(usersAccessRecords, (data) => {
    return {
      fullName: _.get(data, 'user.fullName'),
      phoneNumber: _.get(data, 'user.phoneNumber'),
      NRIC: _.get(data, 'user.NRIC'),
      gender: _.get(data, 'user.gender'),
      role: _.get(data, 'user.role'),
      department: _.get(data, 'department.departmentName'),
      premise: _.get(data, 'premise.premiseId'),
    };
  });
  /* Define your column keys because this is what you use to insert your data according to your columns, they're column A, B, C, D respectively being idClient, Name, Tel, and Adresse.
  So, it's pretty straight forward */
  worksheet.columns = [
    { key: 'index', width: 5 },
    { key: 'fullName', width: 15, alignment: { vertical: 'middle' } },
    { key: 'phoneNumber', width: 15, alignment: { vertical: 'middle' } },
    { key: 'NRIC', width: 15, alignment: { vertical: 'middle' } },
    { key: 'gender', width: 15, alignment: { vertical: 'middle' } },
    { key: 'role', width: 15, alignment: { vertical: 'middle' } },
    { key: 'department', width: 15, alignment: { vertical: 'middle' } },
    { key: 'premise', width: 15, alignment: { vertical: 'middle' } },
  ];
  // const idCol = worksheet.getColumn('id');
  // const nameCol = worksheet.getColumn('B');
  const ageCol = worksheet.getColumn(3); // index starts from 1
  ageCol.eachCell({ includeEmpty: true }, (cell, rowNumber) => {
    // console.log(cell.value);
  });

  // worksheet.addRow({ id: 1, name: 'John Doe', age: 35 });
  // or
  // worksheet.addRow([2, 'Mary Sue', 22]);

  // worksheet.addRows(rows);
  _.forEach(worksheetData, (row, index) => {
    _.set(row, 'index', index + 1);
    worksheet.addRow(row);
    // console.log(worksheet.getRow())
  });

  // worksheet.eachRow((row, number) => {
  //   row.eachCell((cell, colNumber) => {
  //     cell.alignment = { vertical: 'middle' };
  //     if (_.includes([5, 6, 8, 9, 10, 11, 12, 13, 14], colNumber)) {
  //       cell.alignment = { wrapText: 'true', horizontal: 'center', vertical: 'middle' };
  //     }
  //     if (_.includes([4, 7], colNumber)) {
  //       cell.alignment = { horizontal: 'right', vertical: 'middle' };
  //     }
  //     if (number > 4) {
  //       cell.border = {
  //         top: { style: 'thin' },
  //         left: { style: 'thin' },
  //         bottom: { style: 'thin' },
  //         right: { style: 'thin' },
  //       };
  //     }
  //   });
  // });

  // worksheet.getCell('B2').value = 'COMPANY';
  // worksheet.getCell('B3').value = 'FROM';
  // worksheet.getCell('B4').value = 'TO';

  // _.map(['B2', 'B3', 'B4'], (key) => {
  //   worksheet.getCell(key).style = { font: { bold: true } };
  //   worksheet.getCell(key).alignment = { vertical: 'bottom', horizontal: 'right' };
  // });

  // const startDate = moment.unix(startUnix).startOf('day').format('DD/MM/YYYY');
  // const endDate = moment.unix(endUnix).endOf('day').format('DD/MM/YYYY');
  // worksheet.getCell('C2').value = premiseFullName;
  // worksheet.getCell('C3').value = startDate;
  // worksheet.getCell('C3').alignment = { vertical: 'bottom', horizontal: 'right' };
  // worksheet.getCell('C4').value = endDate;
  // worksheet.getCell('C4').alignment = { vertical: 'bottom', horizontal: 'right' };
  // worksheet.mergeCells('A1:I1');
  // worksheet.getCell('A1').value = 'Attendance Worksheet';
  // worksheet.getCell('A1').alignment = { vertical: 'bottom', horizontal: 'center' };

  // iterate over each cell
  // row.eachCell(function(cell, colNumber) {
  // });

  // worksheet.getCell('A1').alignment = { vertical: 'top', horizontal: 'left' };

  // BORDER
  // worksheet.getCell('A1').border = {
  // 	top: {style:'double', color: {argb:'FF00FF00'}},
  // 	left: {style:'double', color: {argb:'FF00FF00'}},
  // 	bottom: {style:'double', color: {argb:'FF00FF00'}},
  // 	right: {style:'double', color: {argb:'FF00FF00'}}
  // };

  // FILL
  // worksheet.getCell('A1').fill = {
  // 	type: 'pattern',
  // 	pattern: 'darkTrellis',
  // 	// thin
  // 	// dotted
  // 	// dashDot
  // 	// hair
  // 	// dashDotDot
  // 	// slantDashDot
  // 	// mediumDashed
  // 	// mediumDashDotDot
  // 	// mediumDashDot
  // 	// medium
  // 	// double
  // 	// thick
  // 	fgColor: { argb: 'FFFFFF00' },
  // 	bgColor: { argb: 'FF0000FF' }
  // };
  return workbook;
};

exports.importXLSX = async (url, admin) => {
  try {
    const { data: dataStream } = await axios({
      method: 'get',
      url,
      responseType: 'stream',
    });

    const schema = {

      'Full Name': {
        prop: 'Full Name',
        type: String,
      },

      'Phone Number': {
        prop: 'Phone Number',
        type: String,
      },

      Gender: {
        prop: 'Gender',
        type: String,
      },

      NRIC: {
        prop: 'NRIC',
        type: String,
      },
      Role: {
        prop: 'Role',
        type: String,
      },
      Department: {
        prop: 'Department',
        type: String,

      },
      Premise: {
        prop: 'Premise',
        type: String,
      },
    };
    let data = [];
    try {
      data = await readXlsxFile(dataStream, { schema });
      console.log('DATA', data);
    } catch (e) {
      throw ('Import xlsx error', e);
    }
    const jsonData = _.compact(_.get(data, 'rows'));

    // const done = await Promise.all(
    const errorList = [];

    for (const json of jsonData) {
      // if (session.hasEnded === false) {

      const fulljson = _.filter(json, (obj) => {
        // Check if any value in the object is not an empty string
        return Object.values(obj).some((value) => value !== '');
      });
      if (!_.isEmpty(fulljson)) {
        const { NRIC } = json;

        delete json.NRIC;
        const session = await mongoose.startSession();
        session.startTransaction();
        try {
          const existUser = await UserModel.find({ phoneNumber: _.get(json, 'Phone Number'), fullName: _.get(json, 'Full Name'), isDeleted: false });
          console.log(existUser);
          if (_.size(existUser) === 0) {
            const userData = {
              ...json,
              ...(!_.isEmpty(NRIC) && NRIC != '' ? { NRIC } : {}),

              gender: _.get(json, 'Gender'),
              phoneNumber: _.get(json, 'Phone Number'),
              fullName: _.get(json, 'Full Name'),
              from: _.get(admin, 'type'),
              uid: uuid(),
              createdBy: _.get(admin, 'email'),
            };
            if (_.isEmpty(_.get(userData, 'phoneNumber'))) {
              throw ('Phone Number is required');
            }
            if (_.isEmpty(_.get(json, 'Premise'))) {
              throw ('Premise is required');
            }
            const existPhoneNumber = await UserModel.find({ phoneNumber: _.get(userData, 'phoneNumber'), isDeleted: false });
            if (!_.isEmpty(existPhoneNumber)) {
              throw ('User with same phone number is exist');
            }
            if (!_.isEmpty(_.get(userData, 'NRIC'))) {
              const existNRIC = await UserModel.find({ NRIC: _.get(userData, 'NRIC'), isDeleted: false });
              if (!_.isEmpty(existNRIC)) {
                throw ('User with same NRIC is exist');
              }
            }

            const user = await UserModel.create([{ ...userData, isDeleted: false }], { session });
            if (!_.isEmpty(user)) {
              const premiseData = await PremiseModel.findOne({ premiseId: _.get(json, 'Premise') });
              const adminData = await AdminModel.findById(admin?._id).session(session).lean();
              if (!_.includes(adminData?.premises, _.get(premiseData, '_id'))) {
                throw ('You no have permission to access this premise');
              }
              const access = await AccessModel.create([{
                premiseId: _.get(premiseData, '_id'),
                uid: _.get(user[0], '_id'),
                departments: {
                  access: true,
                  type: 'toggle',
                  role: _.get(json, 'role'),
                  departmentId: _.get(json, 'Department'),
                },
                startWorkingTime: _.get(premiseData, 'startWorkingTime'),
                endWorkingTime: _.get(premiseData, 'endWorkingTime'),
                isDeleted: false,
              }], { session });
              console.log('access', access);
            }
          }
          await session.commitTransaction();
          await session.endSession();
        } catch (e) {
          console.log('REASON', e);
          if (!_.isEmpty(_.get(e, 'errors'))) {
            errorList.push({
              rowWithError: json,
              errors: _.map(e.errors, (err) => ({
                reason: err.properties.message,
                path: err.properties.path,
                errValue: err.properties.value,
              })),
            });
          } else {
            errorList.push({
              rowWithError: json,
              errors: e,
            });
          }
          await session.abortTransaction();
        }
      }

      // }
    }
    console.log('ERR', errorList);
    if (_.size(errorList) > 0) {
      // await session.abortTransaction();

      throw errorList;
    } else {
      // await session.commitTransaction();
      // await session.endSession();
      return 'success';
    }

    // );

    // console.log('done', done);
    // if (!done.some((result) => result instanceof Error)) {
    //   await session.commitTransaction();
    //   await session.endSession();
    // } else {
    //   await session.abortTransaction();
    // }
  } catch (e) {
    console.log(JSON.stringify(e));
    throw responses.failure('Error when import user', e);
  }
};

exports.deleteUserByExternal = async (params) => {
  const { userId } = params;

  const foundDeletedUser = await UserModel.findOne({ _id: userId, isDeleted: true });
  if (foundDeletedUser) {
    throw responses.failure('The user does not exist.');
  }

  const foundAccesses = await AccessModel.updateMany({ uid: userId }, { isDeleted: true });
  const deletedUser = await UserModel.findOneAndUpdate({ _id: userId }, { isDeleted: true }, { new: true });

  // Delete the face image from Google Cloud Storage
  if (deletedUser.faceImageUrl) {
    try {
      // Extract the file path from the URL
      const fileName = deletedUser.faceImageUrl.split('/').pop().split('?')[0];
      console.log('file name: ', fileName);
      const filePath = `faces/${fileName}`; // Assuming the images are stored in 'faces' folder

      // Get the Google Cloud Storage bucket
      const storage = firebaseAdmin.storage();
      const bucket = storage.bucket();
      const file = bucket.file(filePath);

      // Delete the file from Google Cloud Storage
      await file.delete();

      // Update the user record to remove the face image URL
      await UserModel.findByIdAndUpdate(deletedUser._id, { faceImageUrl: null });
    } catch (error) {
      console.error('Error deleting file from Google Cloud Storage:', error);
      // Log the error but don't throw an exception to ensure user deletion completes
      console.log('Failed to delete face image from cloud storage, continuing with user deletion');
    }
  }

  return deletedUser;
};

exports.fetchUserListByParams = async (params) => {
  const {
    adminPremiseId, premiseIds, limit = 5, page = 1, showDeleted, fullName,
  } = params;

  try {
    // First step: Get admin premise information
    const adminPremise = await PremiseModel.findById(adminPremiseId);
    if (!adminPremise) {
      throw new Error('Admin premise not found');
    }

    // Second step: Find users created by this admin (based on premise ID)
    const filter = {
      createdBy: adminPremiseId,
      isDeleted: false,
    };

    // Add fullName search if provided
    if (fullName) {
      filter.fullName = { $regex: fullName, $options: 'i' };
    }

    if (showDeleted) {
      _.unset(filter, 'isDeleted');
    }
    const usersCreatedByAdmin = await UserModel.find(filter).sort({ createdAt: -1 }).lean();

    // Third step: Get users from admin premise and premises under admin
    let allPremiseIds = [adminPremiseId];
    if (!_.isEmpty(premiseIds)) {
      allPremiseIds = [...allPremiseIds, ...premiseIds];
    }

    // Remove duplicates
    allPremiseIds = _.uniq(allPremiseIds);

    // Get all users with access to these premises
    const premiseAccessData = await Promise.all(_.map(allPremiseIds, async (premiseId) => {
      return getAccessesByPremiseId(premiseId, limit, page, fullName);
    }));

    // Combine all users from premise accesses
    let premiseUsers = [];
    premiseAccessData.forEach((accessData) => {
      if (accessData && accessData.users && accessData.users.length > 0) {
        premiseUsers = [...premiseUsers, ...accessData.users];
      }
    });

    // Remove duplicates based on _id
    premiseUsers = _.compact(_.uniqBy(premiseUsers, '_id'));

    console.log('_id', usersCreatedByAdmin);

    // Filter out users that are already in the usersCreatedByAdmin list
    const adminUserIds = usersCreatedByAdmin.map((user) => user._id.toString());
    const otherUsers = premiseUsers.filter((user) => !adminUserIds.includes(user._id.toString()));

    // Calculate total users for pagination
    const totalUsers = usersCreatedByAdmin.length + otherUsers.length;

    const combinedUsers = [...usersCreatedByAdmin, ...otherUsers];
    const sortedUsers = combinedUsers.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    const paginatedUsers = sortedUsers.slice((page - 1) * limit, page * limit);
    // Return the result with grouped users and pagination
    return {
      users: paginatedUsers,
      totalUsers,
      pagination: {
        totalPage: Math.ceil(totalUsers / limit),
        itemsPerPage: limit,
        totalItems: totalUsers,
        previousPageUrl: '',
        nextPageUrl: '',
      },
    };
  } catch (error) {
    console.log('error', error);
    throw responses.failure(error);
  }
};

// Helper function to get accesses by premise ID
const getAccessesByPremiseId = async (premiseId, limit, page, fullName) => {
  if (!premiseId) return null;

  const matchStage = {
    $match: {
      premiseId: mongoose.Types.ObjectId(premiseId),
      isDeleted: false,
    },
  };

  const lookupPremises = {
    $lookup: {
      from: 'premises',
      localField: 'premiseId',
      foreignField: '_id',
      as: 'premises',
    },
  };

  const lookupUsers = {
    $lookup: {
      from: 'users',
      localField: 'uid',
      foreignField: '_id',
      as: 'users',
    },
  };

  // Add filter for fullName if provided
  const userMatchStage = fullName ? {
    $match: {
      'users.fullName': {
        $regex: fullName,
        $options: 'i',
      },
      'users.isDeleted': false,
    },
  } : {
    $match: {
      'users.isDeleted': false,
    },
  };

  const projectStage = {
    $project: {
      premise: { $arrayElemAt: ['$premises', 0] },
      user: { $arrayElemAt: ['$users', 0] },
      departments: 1,
      accessStartTime: 1,
      accessEndTime: 1,
      createdAt: 1,
    },
  };

  const sortStage = {
    $sort: {
      createdAt: -1,
    },
  };

  const usersAccesses = await AccessModel.aggregate([
    matchStage,
    lookupPremises,
    lookupUsers,
    userMatchStage,
    projectStage,
    sortStage,
  ]);

  if (_.isEmpty(usersAccesses)) return null;

  const users = _.map(usersAccesses, (userAccess) => userAccess.user);

  return {
    premise: usersAccesses[0]?.premise,
    users,
  };
};

exports.fetchUserNumber = async (params) => {
  const {
    premiseId,

  } = params;
  console.log(params);
  const usersAccesses = await AccessModel.find({
    premiseId,
    isDeleted: false,
  }).lean();
  const uidList = usersAccesses?.map((item) => item.uid);

  const inactiveUser = await UserModel.countDocuments({ isDeleted: true, _id: { $in: uidList } }).lean();
  const activeUser = await UserModel.countDocuments({ isDeleted: false, _id: { $in: uidList } }).lean();

  return {
    totalUser: activeUser + inactiveUser,
    activeUser,
    inactiveUser,
  };
};

// include deleted
exports.fetchAllUserListByPremiseId = async (params) => {
  const {
    premiseId,
    page,
    limit,
    searchText,
  } = params;
  console.log(params);
  const usersAccesses = await AccessModel.aggregate([
    {
      $match: {
        premiseId: mongoose.Types.ObjectId(premiseId),
        // isDeleted: false,
      },
    },
    {
      $lookup:
      {
        from: 'premises',
        localField: 'premiseId',
        foreignField: '_id',
        as: 'premises',
      },
    },
    {
      $lookup:
      {
        from: 'users',
        localField: 'uid',
        foreignField: '_id',
        as: 'users',
      },
    },
    {
      $match: {
        $or: [
          {
            'premises.name': {
              $regex: _.isEmpty(searchText) ? '' : searchText,
              $options: 'i',
            },
          },
          {
            'users.fullName': {
              $regex: _.isEmpty(searchText) ? '' : searchText,
              $options: 'i',
            },
          },
        ],
      },
    },
    {
      $project: {
        premise: { $arrayElemAt: ['$premises', 0] },
        user: { $arrayElemAt: ['$users', 0] },
        departments: 1,
        accessStartTime: 1,
        accessEndTime: 1,
      },
    },
    {
      $project: {
        'premise.apiKey': 0,
        'premise.theme': 0,
        'premise.childPremises': 0,
        'premise.employees': 0,
      },
    },
    {
      $match: {
        'user._id': { $exists: true },
      },
    },
    {
      $skip: page * limit,
    },
    {
      $limit: limit,
    },
  ]);

  // populate departments data into access.
  const newUserAccesses = _.flatten(_.map(usersAccesses, (userAccess) => {
    return _.map(
      userAccess.departments,
      (userDepartment) => {
        const foundDepartment = _.find(
          userAccess.premise.departments,
          { _id: userDepartment._id },
        );
        if (userAccess?.user) {
          userAccess.user.role = userAccess.departments[0].role || '';
          userAccess.user.accessStartTime = userAccess.accessStartTime || '';
          userAccess.user.accessEndTime = userAccess.accessEndTime || '';
        }
        const formattedData = {
          department: foundDepartment,
          ...userAccess,
        };
        _.unset(formattedData, 'departments');
        _.unset(formattedData, 'premise.departments');
        _.unset(formattedData, 'department.isDeleted');
        return formattedData;
      },
    );
  }));

  // combined the user access with the records.
  const usersAccessRecords = await Promise.all(_.map(
    newUserAccesses,
    async (userAccess) => {
      const premiseAccessRecords = await AccessRecordModel.find({
        user: userAccess.user._id,
      });
      return {
        ...userAccess,
        accessRecords: premiseAccessRecords,
      };
    },
  ));

  return usersAccessRecords;
};
