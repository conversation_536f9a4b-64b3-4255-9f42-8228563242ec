/* eslint-disable prefer-promise-reject-errors */
/* eslint-disable no-tabs */
/* eslint-disable no-mixed-spaces-and-tabs */
/* eslint-disable class-methods-use-this */
/* eslint-disable no-param-reassign */
const mongoose = require('mongoose');
const limax = require('limax');
const moment = require('moment');

const collection = mongoose.connection.createCollection('socketio', {
  capped: true,
  size: 1e6,
});
const PremiseModel = require('#models/Premise.model');
const { getUsersByPremiseId, getUsersByPremiseIdAndDepartmentId } = require('#services/premise.service');
const { createAdapter } = require('@socket.io/mongo-adapter');
const _ = require('lodash');
const { port } = require('#config/env');
const DeviceModel = require('#models/Device.model');
const DeviceUsersModel = require('#models/DeviceUsers.model');
const { checkIn, createAccessRecord } = require('#services/access.service');
const { stringify } = require('querystring');
const DeviceSettingsRecord = require('#models/DeviceSettingsRecord');
const { setDeviceSettingsV2 } = require('#services/device.service');
const AccessRecordModel = require('#models/AccessRecord.model');
const AccessModel = require('#models/Access.model');

// for qingzheng device
class FacePass {
  // constructor
  constructor(io) {
    this.io = io;

    collection.then(async (db) => {
      await io.adapter(createAdapter(db));
    }).catch(async (err) => {
      if (err.code === 48) {
        await io.adapter(createAdapter(mongoose.connection.collection('socketio'), {
          heartbeatInterval: 8640000,
        }));
      }
    }).finally(async () => {
      // device authentication middleware.
      io.use(async (socket, next) => {
        console.log('someone is connecting', socket.id);
        io.emit('data-test', 'blablabla');
        if (port === '8080') {
          console.log('emitting...');
          io.emit('data-test', 'blablabla');
        }

        const { apiKey } = socket.handshake.auth;
        const foundApiKey = await PremiseModel.findOne({ apiKey });
        // socket.serialNumber = 'abc12dkl23';
        // TO DO LIST
        // if it's an existing device, should follow department value in deviceModel.
        // if not, use first department value in premiseModel.
        socket.devicePremiseId = _.get(foundApiKey, '_id', '').toString();
        socket.departmentId = _.get(foundApiKey, 'departments[0]._id', '');
        socket.user = {
          name: 'andy',
        };

        if (_.isEmpty(foundApiKey)) {
          const err = new Error('Not Authorized');
          err.data = { content: 'Please make sure the API key is valid.' };
          return next(err);
        }

        return next();
        // return next(new Error('authentication error'));
      });

      // on device connected.
      io.on('connection', async (socket) => {
        console.log(`a user is connected. ${socket.id}`);
        const { devicePremiseId, departmentId, user } = socket;
        const serialNumber = _.get(socket, 'handshake.headers.serialnumber', '');
        console.log('serial number:', socket.handshake.headers.serialnumber);

        // socket.join('main');
        socket.emit('data', 'data');
        try {
          const checkDevice = await DeviceModel.find({
            serialNumber,
            name: 'spo',
            premiseId: devicePremiseId,
            departmentId: mongoose.Types.ObjectId(departmentId),
          });
          if (_.size(checkDevice) <= 0) {
            const device = await DeviceModel.create({
              serialNumber,
              name: 'spo',
              premiseId: devicePremiseId,
              departmentId: mongoose.Types.ObjectId(departmentId),
              settings: {},
              cameraSettings: {},
            });

            await DeviceSettingsRecord.create({
              premiseId: devicePremiseId,
              serialNumber,
              from: 'system',
              settings: {},
              cameraSettings: {},
              timestamps: moment().toISOString(),
            });
            console.log('created device:', device);
          } else {
            await DeviceModel.findOneAndUpdate({ serialNumber }, {
              name: 'spo',
              premiseId: devicePremiseId,
              departmentId: mongoose.Types.ObjectId(departmentId),
              status: 'online',
            });
            console.log('updated device:', serialNumber);
          }
        } catch (err) {
          console.log('error during registration of the device:', err);
          console.log('device premise Id:', devicePremiseId);
          await DeviceModel.findOneAndUpdate({ serialNumber }, {
            name: 'spo',
            premiseId: devicePremiseId,
            departmentId: mongoose.Types.ObjectId(departmentId),
            status: 'online',
          });
          console.log('The device is registered.');
        }

        // const allSockets = await this.io.fetchSockets();
        // console.log('all sockets:', allSockets);

        // sync devices' users.
        socket.on('syncUser', async (cb) => {
          console.log(`${socket.handshake.headers.serialnumber} is syncing users.`);
          const users = await getUsersByPremiseIdAndDepartmentId(devicePremiseId, serialNumber);
          cb(users);
        });

        // add face token.
        socket.on('addFaceToken', async (params) => {
          console.log('add face token');
          const { userId, faceToken } = params;
          const device = await DeviceModel.findOne({ serialNumber: _.get(socket, 'handshake.headers.serialnumber', '') });
          const findIfUserExistedInDevice = await DeviceUsersModel.findOne({
            premiseId: _.get(socket, 'devicePremiseId', ''),
            'users.user': userId,
            device: device._id,
          });

          // console.log('find if user existed in device:', findIfUserExistedInDevice);

          if (findIfUserExistedInDevice) {
            const result = await DeviceUsersModel.findOneAndUpdate({
              device: device._id,
              premiseId: _.get(socket, 'devicePremiseId', ''),
              'users.user': userId,
            }, {
              'users.$.faceToken': faceToken,
            }, {
              new: true,
            });
            // console.log('updated result:', result);
          } else {
            const addedResult = await DeviceUsersModel.findOneAndUpdate(
              {
                device: device._id,
                premiseId: _.get(socket, 'devicePremiseId', ''),
              },
              {
                $push: {
                  users: { user: userId, faceToken: params?.faceToken },
                },
              },
              {
                upsert: true,
              },
            );
            console.log('added result:', addedResult);
          }
        });

        // update settings
        socket.on('updateSettings', async (generalSettings) => {
          console.log('updating settings from device.', generalSettings);
          if (!_.isEmpty(generalSettings)) {
            await setDeviceSettingsV2({
              settings: generalSettings.settings,
              cameraSettings: generalSettings.cameraSettings,
              serialNumber,
              timestamps: generalSettings.timestamps,
              premiseId: devicePremiseId,
            }, 'device', null);
          }
        });

        // check in logic
        socket.on('checkIn', async (data) => {
          console.log('data', data);
          await createAccessRecord(data, socket, 'checkIn');
        });
        socket.on('checkOut', async (data) => {
          console.log('data', data);
          await createAccessRecord(data, socket, 'checkout');
        });
        socket.on('lunchIn', async (data) => {
          console.log('data', data);
          await createAccessRecord(data, socket, 'lunchIn');
        });
        socket.on('lunchOut', async (data) => {
          console.log('data', data);
          await createAccessRecord(data, socket, 'lunchOut');
        });
        socket.on('none', async (data) => {
          console.log('data', data);
          await createAccessRecord(data, socket, 'none');
        });

        socket.on('sync-access-records', async (accessRecords) => {
          _.map(accessRecords, async (accessRecord) => {
            await createAccessRecord(accessRecord, socket, _.get(accessRecord, 'scanType', 'none'));
          });
        });

        socket.on('add-access-records', async (record) => {
          await createAccessRecord(record, socket, _.get(record, 'scanType', 'none'));
        });

        socket.on('liveStatistic', async (data) => {
          console.log('LiveData', data);
          const access = await AccessModel.find({ premiseId: data });
          const accessUID = _.map(access, (accessData) => {
            return _.get(accessData, 'uid');
          });
          const accessRecord = await AccessRecordModel.find({ user: { $in: accessUID } }).sort({ createdAt: -1 }).limit(20).populate({ path: 'user', select: 'fullName' });
          return accessRecord;
        });

        // on device disconnected.
        socket.on('disconnect', async () => {
          console.log(`${socket.id} disconnected`);
          console.log('serial number:', serialNumber);
          await DeviceModel.findOneAndUpdate({
            serialNumber,
          }, {
            status: 'offline',
          });
        });
      });
    });
  }

  async emit(devicePremiseId, event, data) {
    const allSockets = await this.io.fetchSockets();
    // console.log('all sockets:', allSockets);
    const sameNamePremiseSockets = _.filter(allSockets, { devicePremiseId });

    return Promise.all(_.map(sameNamePremiseSockets, (socket) => {
      console.log('socket in emit:', socket);
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('timed out'));
        }, 10000);

        // emit to all devices within the premise.
        socket.emit(event, data, async (params) => {
          const { result } = params;

          console.log('data from device:', params);
          clearTimeout(timeout);
          console.log('socket returned result:', result);
          if (result === 0) {
            // console.log(JSON.stringify({
            //   token: params.faceToken,
            //   user: data.fullName,
            // }));

            if (event === 'addFace') {
              const device = await DeviceModel.findOne({ uid: _.get(socket, 'handshake.headers.serialnumber', '') });
              await DeviceUsersModel.updateOne(
                {
                  device: device._id,
                  premiseId: _.get(socket, 'devicePremiseId', ''),
                },
                {
                  $push: {
                    users: { user: data._id, faceToken: params?.faceToken },
                  },
                },
                {
                  upsert: true,
                },
              );
            }

            clearTimeout(timeout);
            resolve('OK');
          } else if (result === 1) {
            reject(new Error('No face detected.'));
          } else if (result === 2) {
            reject(new Error('Photo quality is too low.'));
          } else if (result === 3) {
            reject(new Error('Failed to bind face to device.'));
          } else if (result === 4) {
            reject(new Error('The user is registered in the premise'));
          }
        });
      });
    }));
  }

  // emit to premise's devices
  async emitToDevice(devicePremiseId, deviceSerialNumber, event, data) {
    const allSockets = await this.io.fetchSockets();
    const sameNamePremiseSockets = _.filter(allSockets, { handshake: { headers: { serialnumber: deviceSerialNumber } } });
    console.log('all sockets:', allSockets);
    console.log('device premise id:', sameNamePremiseSockets);
    return Promise.all(_.map(sameNamePremiseSockets, (socket) => {
      console.log('request header from socket:', socket.handshake.headers);
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('timed out'));
        }, 10000);

        // emit to all devices within the premise.
        socket.emit(event, data, async (params) => {
          const { result } = params;

          console.log('data from device:', params);
          clearTimeout(timeout);
          console.log('socket returned result:', result);
          if (result === 0) {
            // console.log(JSON.stringify({
            //   token: params.faceToken,
            //   user: data.fullName,
            // }));

            if (event === 'check-face') {
              console.log(' is face validated:', params.isFaceValidated);
              console.log('type of is face validated:', typeof params.isFaceValidated);
              if (params.isFaceValidated) {
                resolve('ok');
              } else {
                reject(new Error('The face quality is too low.'));
              }
            }

            if (event === 'addFace') {
              const device = await DeviceModel.findOne({ uid: _.get(socket, 'handshake.headers.serialnumber', '') });
              await DeviceUsersModel.updateOne(
                {
                  device: device._id,
                  premiseId: _.get(socket, 'devicePremiseId', ''),
                },
                {
                  $push: {
                    users: { user: data._id, faceToken: params?.faceToken },
                  },
                },
                {
                  upsert: true,
                },
              );
            }

            clearTimeout(timeout);
            resolve('OK');
          } else if (result === 1) {
            reject(new Error('No face detected.'));
          } else if (result === 2) {
            reject(new Error('Photo quality is too low.'));
          } else if (result === 3) {
            reject(new Error('Failed to bind face to device.'));
          } else if (result === 4) {
            reject(new Error('The user is registered in the premise'));
          }
        });
      });
    }));
  }
}

module.exports = FacePass;
