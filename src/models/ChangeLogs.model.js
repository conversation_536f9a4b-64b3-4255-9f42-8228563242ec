const { ObjectId } = require('mongodb');
const mongoose = require('mongoose');
const mongooseUniqueValidator = require('mongoose-unique-validator');

const { Schema } = mongoose;

// at least one department must be created together with premise.

const ChangeLogsSchema = new Schema({
  premiseId: {
    type: ObjectId,
    required: true,
    ref: 'premise',
  },
  userId: {
    type: ObjectId,
    required: true,
    ref: 'user',
  },
  documentId: {
    type: ObjectId,
    // required: true,
  },
  isDeletedChange: {
    type: String,
    required: true,
  },
  adminId: {
    type: ObjectId,
    required: true,
  },
}, { timestamps: true });

ChangeLogsSchema.plugin(mongooseUniqueValidator);

module.exports = mongoose.model('changeLogs', ChangeLogsSchema);
