const { required } = require('joi');
const { ObjectId } = require('mongodb');
const mongoose = require('mongoose');
const mongooseUniqueValidator = require('mongoose-unique-validator');

const { Schema } = mongoose;

// at least one department must be created together with premise.

const DepartmentSchema = new Schema({
  departmentId: {
    type: String, // the id will refer back to department id in premise collection.
  },
  access: {
    type: Boolean,
    required: true,
    default: true,
  },
  remark: {
    type: String,
  },
  type: {
    type: String,
    enum: ['toggle'],
    required: true,
    default: 'toggle',
  },
  role: {
    type: String,
    enum: ['visitor', 'employee', 'user'],
  },
  leaveAmount: {
    type: Number,
    default: 0,
    // required: true,
  },
});

const AccessSchema = new Schema({
  uid: {
    type: ObjectId,
    ref: 'user',
    required: true,
  },
  premiseId: {
    type: ObjectId,
    ref: 'premise',
    required: true,
  },
  departments: {
    type: [DepartmentSchema],
    // required: true,
  },
  startWorkingTime: {
    type: Date,
    required: true,
  },
  endWorkingTime: {
    type: Date,
    required: true,
  },
  accessStartTime: {
    type: Date,
  },
  accessEndTime: {
    type: Date,
  },
  isDeleted: {
    type: Boolean,
    required: true,
  },
  remark: {
    type: String,
  },
}, { timestamps: true });

// AccessSchema.index({
//   uid: 1,
//   premiseId: 1,
// }, { unique: true });

AccessSchema.plugin(mongooseUniqueValidator);

module.exports = mongoose.model('access', AccessSchema);
