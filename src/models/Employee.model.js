const mongoose = require('mongoose');
const mongooseUniqueValidator = require('mongoose-unique-validator');
const { Schema } = require('mongoose');

const EmergencyContactSchema = new Schema({
  name: {
    type: String,
  },
  email: {
    type: String,
  },
  phoneNumber: {
    type: String,
  },
});

const EmployeeSchema = new Schema({
  user: {
    type: String,
    ref: 'users',
    required: true,
  },
  KWSP: {
    type: String,
  },
  SOCSO: {
    type: String,
  },
  religion: {
    type: String,
  },
  birthDate: {
    type: Date,
  },
  race: {
    type: String,
  },
  maritalStatus: {
    type: String,
  },
  nationality: {
    type: String,
  },
  department: {
    type: String,
  },
  designation: {
    type: String,
  },
  emergencyContact: {
    type: EmergencyContactSchema,
  },
}, { timestamps: true });

EmployeeSchema.plugin(mongooseUniqueValidator);

module.exports = mongoose.model('employee', EmployeeSchema);
