const mongoose = require('mongoose');
const mongooseUniqueValidator = require('mongoose-unique-validator');
const { Schema } = require('mongoose');
const { ObjectId } = require('mongodb');

const CallSchema = new Schema({
  serialNumber: {
    type: ObjectId,
    ref: 'devices',
    required: true,
  },
  status: {
    type: String,
    required: true,
  },
}, { timestamps: true });

CallSchema.plugin(mongooseUniqueValidator);

module.exports = mongoose.model('call', CallSchema);
