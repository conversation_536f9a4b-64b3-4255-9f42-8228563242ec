const { callback } = require('#services/callback.service');
const { ObjectId } = require('mongodb');
const mongoose = require('mongoose');
const mongooseUniqueValidator = require('mongoose-unique-validator');

const { Schema } = mongoose;

// at least one department must be created together with premise.

const AccessRecordSchema = new Schema({
  premiseId: {
    type: ObjectId,
    required: true,
    ref: 'premise',
  },
  deviceId: {
    type: ObjectId,
    required: true,
    ref: 'device',
  },
  user: {
    type: ObjectId,
    required: true,
    ref: 'user',
  },
  score: {
    type: String,
    required: true,
  },
  trackId: {
    type: String,
    required: true,
  },
  department: {
    type: String,
    required: true,
  },
  attributes: {
    type: String,
    required: true,
  },
  prediction: {
    type: String,
    required: true,
  },
  serialNumber: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    required: true,
  },
  faceImageUrl: {
    type: String,
  },
}, { timestamps: true });

AccessRecordSchema.plugin(mongooseUniqueValidator);
const AccessRecord = mongoose.model('accessRecord', AccessRecordSchema);
// for callback url.
AccessRecord.watch().on('change', async (data) => {
  // console.log('access:', data);
  await callback(data?.fullDocument);
});
module.exports = AccessRecord;
