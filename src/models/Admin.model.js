const mongoose = require('mongoose');
const mongooseUniqueValidator = require('mongoose-unique-validator');

const { Schema } = mongoose;

const PremiseSchema = new Schema({
  premiseId: {
    type: Schema.Types.ObjectId,
    ref: 'premise',
    required: true,
    // unique: true,
  },
  position: {
    type: String,
    required: true,
    default: 'admin',
    enum: ['admin', 'owner', 'viewer'],
  },
});

const AdminSchema = new Schema({
  uid: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    required: true,
    unique: true,
  },
  password: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    required: true,
    enum: ['platform-admin', 'premise-admin'],
  },
  premises: [PremiseSchema],
});

AdminSchema.plugin(mongooseUniqueValidator);

module.exports = mongoose.model('admin', AdminSchema);
