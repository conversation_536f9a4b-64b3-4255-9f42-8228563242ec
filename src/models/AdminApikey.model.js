const mongoose = require('mongoose');
const mongooseUniqueValidator = require('mongoose-unique-validator');

const { Schema } = mongoose;

const PremiseSchema = new Schema({
  premiseId: {
    type: Schema.Types.ObjectId,
    ref: 'premise',
    required: true,
    // unique: true,
  },
  position: {
    type: String,
    required: true,
    default: 'admin',
    enum: ['admin', 'owner', 'viewer'],
  },
});

const AdminApikeySchema = new Schema({
  apiKey: {
    type: String,
    required: true,
  },
  projectName: {
    type: String,
    required: true,
  },
  premises: [PremiseSchema],
}, {
  timestamps: true,
});

AdminApikeySchema.plugin(mongooseUniqueValidator);

module.exports = mongoose.model('adminApikey', AdminApikeySchema);
