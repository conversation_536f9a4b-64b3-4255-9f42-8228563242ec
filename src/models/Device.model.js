const { ObjectId, Double, Int32 } = require('mongodb');
const mongoose = require('mongoose');
const mongooseUniqueValidator = require('mongoose-unique-validator');

const { Schema } = mongoose;

const SettingSchema = new Schema({
  rcAttributeAndOcclusionMode: {
    type: Number,
    required: true,
    default: 1,
  },
  searchThreshold: {
    type: Number,
    required: true,
    default: 69,
  },
  livenessThreshold: {
    type: Number,
    required: true,
    default: 55,
  },
  livenessEnabled: {
    type: Boolean,
    required: true,
    default: false,
  },
  rgbIrLivenessEnabled: {
    type: Boolean,
    required: true,
    default: false,
  },
  poseThresholdRoll: {
    type: Number,
    required: true,
    default: 35,
  },
  poseThresholdPitch: {
    type: Number,
    required: true,
    default: 35,

  },
  poseThresholdYaw: {
    type: Number,
    required: true,
    default: 35,
  },
  blurThreshold: {
    type: Number,
    required: true,
    default: 0.8,
  },
  lowBrightnessThreshold: {
    type: Number,
    required: true,
    default: 30,
  },
  highBrightnessThreshold: {
    type: Number,
    required: true,
    default: 210,
  },
  brightnessSTDThreshold: {
    type: Number,
    required: true,
    default: 80,
  },
  faceMinThreshold: {
    type: Number,
    required: true,
    default: 100,
  },
  retryCount: {
    type: Number,
    required: true,
    default: 2,
  },
  smileEnabled: {
    type: Boolean,
    required: true,
    default: false,
  },
  maxFaceEnabled: {
    type: Boolean,
    required: true,
    default: true,
  },
  FacePoseThresholdPitch: {
    type: Number,
    required: true,
    default: 35,
  },
  FacePoseThresholdRoll: {
    type: Number,
    required: true,
    default: 35,
  },
  FacePoseThresholdYaw: {
    type: Number,
    required: true,
    default: 35,
  },
  FaceBlurThreshold: {
    type: Number,
    required: true,
    default: 0.7,
  },
  FaceLowBrightnessThreshold: {
    type: Number,
    required: true,
    default: 70,
  },
  FaceHighBrightnessThreshold: {
    type: Number,
    default: 220,
  },
  FaceBrightnessSTDThreshold: {
    type: Number,
    required: true,
    default: 60,
  },
  FaceFaceMinThreshold: {
    type: Number,
    required: true,
    default: 100,
  },
  FaceRcAttributeAndOcclusionMode: {
    type: Number,
    required: true,
    default: 2,
  },
});

const CameraSettingSchema = new Schema({
  cameraFacingFront: {
    type: Boolean,
    required: true,
    default: true,
  },
  faceRotation: {
    type: Number,
    required: true,
    default: 90,
  },
  isSettingAvailable: {
    type: Boolean,
    required: true,
    default: true,
  },
  cameraPreviewRotation: {
    type: Number,
    required: true,
    default: 90,
  },
  isCross: {
    type: Boolean,
    required: true,
    default: false,
  },
});

const DeviceSchema = new Schema({
  serialNumber: {
    type: String,
    required: true,
    unique: true,
  },
  name: {
    type: String,
    required: true,
  },
  premiseId: {
    type: ObjectId,
    ref: 'premise',
    required: true,
  },
  departmentId: {
    type: ObjectId,
    required: true,
  },
  // settings: {
  //   type: SettingSchema,
  // },
  // cameraSettings: {
  //   type: CameraSettingSchema,
  // },
  status: {
    type: String,
    required: true,
    enum: ['online', 'offline'],
    default: 'offline',
  },
  isDeleted: {
    type: Boolean,
    required: true,
    default: false,
  },
}, { timestamps: true });

DeviceSchema.plugin(mongooseUniqueValidator);

module.exports = mongoose.model('device', DeviceSchema);
