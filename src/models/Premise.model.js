const mongoose = require('mongoose');

const mongooseUniqueValidator = require('mongoose-unique-validator');

const { Schema } = mongoose;

const ThemeSchema = new Schema({
  premiseLogoUrl: {
    type: String,
    required: true,
  },
  mainColor: {
    type: String,
    required: true,
  },
  textColor: {
    type: String,
    required: true,
  },
});

const DepartmentSchema = new Schema({
  departmentName: {
    type: String,
    required: true,
  },
  isDeleted: {
    type: Boolean,
    required: true,
    default: false,
  },
}, {
  timestamps: true,
});

const PremiseSchema = new Schema({
  premiseId: { // slug
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  departments: {
    type: [DepartmentSchema],
    required: true,
  },
  theme: {
    type: ThemeSchema,
    required: true,
  },
  public: {
    type: String,
    required: true,
  },
  apiKey: {
    type: String,
    required: true,
    unique: true,
  },
  childPremises: {
    type: [String],
    ref: 'premise',
    required: true,
  },
  employees: {
    type: [String],
    ref: 'users',
  },
  callbackUrl: {
    type: String,
  },
  isDeleted: {
    type: Boolean,
    required: true,
    default: false,
  },
}, { timestamps: true });

PremiseSchema.plugin(mongooseUniqueValidator);
module.exports = mongoose.model('premise', PremiseSchema);
