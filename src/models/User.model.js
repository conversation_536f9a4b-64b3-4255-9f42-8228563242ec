const mongoose = require('mongoose');
const mongooseUniqueValidator = require('mongoose-unique-validator');

const { Schema } = mongoose;

const UserSchema = new Schema(
  {
    uid: {
      type: String,
      required: true,
    },

    fullName: {
      type: String,
      required: true,
    },
    faceImageUrl: {
      type: String,
      required: true,
    },
    copyOfHDImageUrl: {
      type: String,
      // required: true,
    },
    phoneNumber: {
      type: String,
      // required: true,
    },
    createdBy: {
      type: String,
      required: true,
    },
    from: {
      type: String,
      required: true,
    },
    NRIC: {
      type: String,
    },
    passportNumber: {
      type: String,
    },
    gender: {
      type: String,
    },

    isDeleted: {
      type: Boolean,
      required: true,
      default: false,
    },
    facialPhotoStatus: {
      type: String,
    },
    facialPhotoFailedReason: {
      type: String,
    },
  },
  {
    timestamps: true,
  },
);

UserSchema.plugin(mongooseUniqueValidator);

module.exports = mongoose.model('user', UserSchema);
