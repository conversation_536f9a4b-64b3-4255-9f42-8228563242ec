const { ObjectId } = require('mongodb');
const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');

const { Schema } = mongoose;

const requestSchama = new Schema({
  type: {
    type: String,
    required: true,
  },
  premise: {
    type: ObjectId,
    ref: 'premise',
  },
  remark: {
    type: String,
    required: true,
  },
  isLeave: { type: Boolean },
  paidLeave: { type: Boolean },
  startDate: {
    type: Date,
  },
  endDate: {
    type: Date,
  },
  user: {
    type: ObjectId,
    ref: 'user',
  },
  status: {
    type: String,
  },
  isDeleted: { type: Boolean, default: false },
}, {
  timestamps: true, strict: 'throw',
});

module.exports = mongoose.model('request', requestSchama);
