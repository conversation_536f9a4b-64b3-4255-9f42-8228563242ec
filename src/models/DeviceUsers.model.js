const { ObjectId } = require('mongodb');
const mongoose = require('mongoose');
const mongooseUniqueValidator = require('mongoose-unique-validator');

const { Schema } = mongoose;

const user = new Schema({
  user: {
    type: ObjectId,
    required: true,
    ref: 'user',
    // unique: true,
  },
  faceToken: {
    type: String,
    required: true,
    unique: true,
  },
});

const DeviceUsersSchema = new Schema({
  device: {
    type: ObjectId,
    required: true,
    ref: 'device',
  },
  premiseId: {
    type: ObjectId,
    required: true,
    ref: 'premise',
  },
  users: {
    type: [user],
  },
}, { timestamps: true });

// DeviceUsersSchema.index({
//   device: 1, 'users.user': 1,
// });
DeviceUsersSchema.plugin(mongooseUniqueValidator);

module.exports = mongoose.model('deviceUser', DeviceUsersSchema);
