import axios from 'axios';
import qs from 'qs';
import _ from 'lodash';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getDeviceSerialNumber } from 'facepass-react-native-module';
import { config } from './config';

const api = async (
  path,
  method = "get",
  json = {},
  setLoading,
  moreOptions = {},
  navigation,
) => {
  setLoading && setLoading(true);

  const apiKey = await AsyncStorage.getItem('apiKey');
  const serialNumberStringify = await getDeviceSerialNumber();
  const parsedSerialNumber = await JSON.parse(serialNumberStringify);
  const serialNumber = parsedSerialNumber['persist.ro.serialno'];

  const headers = {
    "Content-Type": "application/json",
    apikey: apiKey,
    serialnumber: serialNumber,
  };

  // const { apiBaseUrl } = config;
  // console.log('config api url:', config.apiBaseUrl);

  const options = {
    method,
    url: `https://facial-recognition-3bf89.as.r.appspot.com/api/${path}`,
    // url: `http://192.168.0.124:8080/api/${path}`,
    headers,
    paramsSerializer: { serialize: (params) => qs.stringify(params) }, // not in the official documents, correct syntax for axios v1.2.0 and above
    ...moreOptions,
  };

  if (method === "get") {
    options.params = json;
  } else {
    options.data = json;
  }

  return axios(options)
    .then((res) => res.data)
    .catch((e) => {
      const err = _.get(e, "response.data");
      console.log("err", err);
      console.log("userData", err?.message);
      if (err) {
        if (err.code === 401 && err?.message === "User data not found") {
          console.log("err here");
          navigation.navigate("registerSocial");
        } else if (err.code === 403) {
        }
        console.log(`${err}url${options.url}`);
        throw err;
      }
      throw e;
    })
    .finally(() => {});
};

export default api;
