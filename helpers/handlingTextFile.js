/* eslint-disable no-unused-vars */

// import RNFS from 'react-native-fs';
import _ from "lodash";

import * as FileSystem from "expo-file-system";

const initFiles = async () => {
  const userPath = `${FileSystem.documentDirectory}users.txt`; // ✅
  const generalSettingsPath = `${FileSystem.documentDirectory}generalSettings.txt`;
  const accessRecordsPath = `${FileSystem.documentDirectory}accessrecords.txt`; // ✅
  const premiseDataPath = `${FileSystem.documentDirectory}premiseData.txt`; // ✅

  // initiating user file
  await FileSystem.readAsStringAsync(userPath).catch(async (err) => {
    if (_.includes(err.message, "open failed")) {
      // write the file if the file does not exist. e.g. loading the app for the first time.
      await FileSystem.writeAsStringAsync(userPath, JSON.stringify([]))
        .then((success) => {
          console.log(`${userPath} file is written.`);
        }).catch((error) => {
          console.log(`file ${userPath} cannot be written - cannot read property:`, error.message);
        });
    }
  });

  // initiating general settings file
  await FileSystem.readAsStringAsync(generalSettingsPath).catch(async (err) => {
    if (_.includes(err.message, "open failed")) {
      // write the file if the file does not exist. e.g. loading the app for the first time.
      await FileSystem.writeAsStringAsync(generalSettingsPath, JSON.stringify({}))
        .then((success) => {
          console.log(`${generalSettingsPath} file is written.`);
        }).catch((error) => {
          console.log(`file ${generalSettingsPath} cannot be written - cannot read property:`, error.message);
        });
    }
  });

  // initiating access records file
  await FileSystem.readAsStringAsync(accessRecordsPath).catch(async (err) => {
    if (_.includes(err.message, "open failed")) {
      // write the file if the file does not exist. e.g. loading the app for the first time.
      await FileSystem.writeAsStringAsync(accessRecordsPath, JSON.stringify([]))
        .then((success) => {
          console.log(`${accessRecordsPath} file is written.`);
        }).catch((error) => {
          console.log(`file ${accessRecordsPath} cannot be written - cannot read property:`, error.message);
        });
    }
  });

  // initiating premise data file
  await FileSystem.readAsStringAsync(premiseDataPath).catch(async (err) => {
    if (_.includes(err.message, "open failed")) {
      // write the file if the file does not exist. e.g. loading the app for the first time.
      await FileSystem.writeAsStringAsync(premiseDataPath, JSON.stringify([]))
        .then((success) => {
          console.log(`${premiseDataPath} file is written.`);
        }).catch((error) => {
          console.log(`file ${premiseDataPath} cannot be written - cannot read property:`, error.message);
        });
    }
  });
};

const appendDataToTextFile = async (fileName, data) => {
  const path = `${FileSystem.documentDirectory}${fileName}.txt`;

  await FileSystem.readAsStringAsync(path)
    .then(async (result) => {
      console.log('data from file read in append mode:', result.toString());

      const parsedJSON = JSON.parse(result.toString());
      console.log('before parsing access records:', parsedJSON);

      parsedJSON.push(data);

      const finalData = JSON.stringify(parsedJSON);
      console.log('after appended to the file:', finalData);

      // append the file
      await FileSystem.writeAsStringAsync(path, finalData)
        .then((success) => {
          console.log('FILE WRITTEN!');
        })
        .catch((err) => {
          console.log('something wrong when writing to file:', err.message);
        });
      // stat the first file
      // return Promise.all([RNFS.stat(result[0].path), result[0].path]);
    })

    .then((statResult) => 'no file')
    .then((contents) => {
      // log the file contents
      console.log(contents);
    })
    .catch(async (err) => {
      console.log('something wrong when going through writing process:', err.message);
    });
};

const getTextFile = async (fileName) => {
  const path = `${FileSystem.documentDirectory}${fileName}.txt`;

  const retrievedDataFromFile = await FileSystem.readAsStringAsync(path)
    .then(async (jsonInString) => {
      console.log('data from path: ', path);
      console.log('data from file read:', jsonInString);

      const parsedJSON = JSON.parse(jsonInString);
      return parsedJSON;
    })
    .catch(async (err) => {
      console.log('something wrong when reading file:', err.message);
    });

  console.log('retrieved data from file:', retrievedDataFromFile);
  return retrievedDataFromFile;
};

const updateFile = async (fileName, data) => {
  const path = `${FileSystem.documentDirectory}${fileName}.txt`;

  await FileSystem.writeAsStringAsync(path, JSON.stringify(data))
    .then((success) => {
      console.log('FILE WRITTEN!');
    })
    .catch((err) => {
      console.log('something wrong when writing to file:', err.message);
    });
};

const deleteFile = async (fileName) => {
  const path = `${FileSystem.documentDirectory}${fileName}.txt`;

  await FileSystem.writeAsStringAsync(path, JSON.stringify([]))
    .then((success) => {
      console.log('FILE DELETED WRITTEN!');
    })
    .catch((err) => {
      console.log('something wrong when writing to file:', err.message);
    });
};

export {
  appendDataToTextFile, getTextFile, deleteFile, updateFile, initFiles,
};
