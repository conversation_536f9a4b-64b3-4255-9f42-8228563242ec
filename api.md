This is the apis docs

1. create terms and conditions
   url: http://localhost:8081/api/terms

sample params:
terms:[{
title:"title",
content:"content"
}]

2. get terms and conditions
   url: http://localhost:8081/api/terms

3. update terms and conditions
   url: http://localhost:8081/api/terms/:termsId
   sample params:
   terms:[{
   title:"title",
   content:"content"
   }]

4. update voucher
   url: http://localhost:8081/api/voucher
   sample params:
   vouchers:[{
   voucherName:"voucher",
   voucherImage:"voucherImage",
   voucherCode:"QT1122"
   }]


5. add voucher
   url: http://localhost:8081/api/voucher
   sample params:
   vouchers:[{
   voucherName:"voucher",
   voucherImage:"voucherImage",
   voucherCode:"QT1122"
   }]


6. add voucher
   url: http://localhost:8081/api/vouchers


