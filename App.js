/* eslint-disable no-underscore-dangle */
import * as React from "react";
import { NavigationContainer, StackActions } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import {
  setDefaultGroupName,
  initData,
  cameraSetting,
  createGroup,
  checkGroupExist,
  releaseFacePassHandler,
  hideNavigationBar,
  setExposureCompensation,
} from "facepass-react-native-module";
import _ from "lodash";
import AsyncStorage from "@react-native-async-storage/async-storage";

import {
  View, Text, Dimensions, AppRegistry,
  ActivityIndicator,
  LogBox,
} from "react-native";
import moment from "moment";
import { getSerialNumber } from 'react-native-device-info';
import { useEffect } from "react";
import RNFS from 'react-native-fs';
import Home from "./Home";
import SettingsPage from "./Settings";
import socket, { initSocket, syncUser } from "./socket";

import api from "./helpers/api";
import { initAudio } from "./audio";
import DoorbellPage from "./Screens/Doorbell";
import ApiRegistration from "./ApiRegistration";
import ScanFaceMain from "./ScanFaceMain";
import ApiContext from "./ApiContext";
import { getTextFile, initFiles, updateFile } from "./helpers/handlingTextFile";

LogBox.ignoreAllLogs();

const Stack = createStackNavigator();

function LogIn() {
  React.useEffect(() => {
    console.log(CONSOLE_.FgRed, getSerialNumber());
  }, []);

  return (
    <View
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        height: Dimensions.get("screen").height,
      }}
    >
      <View style={{ width: "100%", padding: "10%" }}>
        <Text style={{ textAlign: "center" }}>
          Logging in. Please wait a moment
        </Text>
      </View>
      <View style={{ width: "100%", height: 300 }}>
        <ActivityIndicator size="large" color="#00f" />
      </View>
    </View>
  );
}

function App() {
  const parameter = {
    rcAttributeAndOcclusionMode: 1,
    searchThreshold: 69,
    livenessThreshold: 69,
    livenessEnabled: true,
    rgbIrLivenessEnabled: false,
    poseThresholdRoll: 35,
    poseThresholdPitch: 35,
    poseThresholdYaw: 35,
    blurThreshold: 0.8,
    lowBrightnessThreshold: 30,
    highBrightnessThreshold: 210,
    brightnessSTDThreshold: 80,
    faceMinThreshold: 70,
    retryCount: 2,
    smileEnabled: false,
    maxFaceEnabled: true,
    FacePoseThresholdPitch: 35,
    FacePoseThresholdRoll: 35,
    FacePoseThresholdYaw: 35,
    FaceBlurThreshold: 0.7,
    FaceLowBrightnessThreshold: 70,
    FaceHighBrightnessThreshold: 220,
    FaceBrightnessSTDThreshold: 60,
    FaceFaceMinThreshold: 70,
    FaceRcAttributeAndOcclusionMode: 2,
    // stayAfterScan: true,
  };

  // const setting = {
  //   // Default parameter if no setting found in storage & offline
  //   cameraFacingFront: false,
  //   faceRotation: 90,
  //   isSettingAvailable: true,
  //   cameraPreviewRotation: 270,
  //   isCross: false,
  // }; // get ur setting from storage

  const settingSmall = {
    // Default parameter if no setting found in storage & offline
    cameraFacingFront: true,
    faceRotation: 90,
    isSettingAvailable: true,
    cameraPreviewRotation: 90,
    isCross: false,
  }; // get ur setting from storage

  const [successInit, setSuccessInit] = React.useState(false);
  const [finalString, setFinalString] = React.useState(""); // FOR API KEY INPUT
  const [componentLoad, setcomponentLoad] = React.useState(
    "Starting Initialization",
  );
  const [statusCompleted, setStatusCompleted] = React.useState([
    { initializing: false, text: "Audio" },
    { initializing: true, text: "Camera" },
    { initializing: true, text: "Groups" },
    // { initializing: true, text: "Database" },
    { initializing: true, text: "Recognition" },
  ]);

  const users = [];

  const GROUP_NAME = "facepass";

  const initCamera = async () => {
    // if the internet is online, use the cloud settings and get a copy from it.
    await api("device/settings")
      .then(async (res) => {
        // if got internet connection, use cloud camera settings.
        const cloudDeviceSettings = res.data;
        console.log("get cloud settings:", cloudDeviceSettings);
        const setting = await getTextFile("generalSettings");
        const tempSettings = await setting;
        let latestSettings = null;
        if (tempSettings === null || tempSettings === undefined || _.isEmpty(tempSettings)) {
          latestSettings = cloudDeviceSettings;
          console.log("local settings are emptied.");
        } else {
          console.log("cloud time:", _.get(cloudDeviceSettings, "timestamps"));
          console.log("local time:", _.get(setting, 'timestamps'));
          latestSettings = moment(_.get(cloudDeviceSettings, "timestamps")).isAfter(
            moment(_.get(setting, "timestamps")),
          )
            ? cloudDeviceSettings
            : setting;
          console.log("selected settings:", setting);
          console.log("selected settings:", latestSettings);
          if (_.get(latestSettings, 'settings.defaultScan')) {
            AsyncStorage.getItem('defaultScan').then(async (res) => {
              if (_.isNil(res)) {
                await AsyncStorage.setItem('defaultScan', _.get(latestSettings, 'settings.defaultScan'));
              }
            });
          } else {
            AsyncStorage.getItem('defaultScan').then(async (res) => {
              if (_.isNil(res)) {
                await AsyncStorage.setItem('defaultScan', 'none');
              }
            });
          }
        }
        if (!_.isEmpty(latestSettings)) {
          await updateFile('generalSettings', latestSettings);
        } else {
          await updateFile('generalSettings', {
            cameraSettings: settingSmall,
            settings: parameter,
            timestamps: moment().toISOString(),
          });
        }

        if (_.isEmpty(cloudDeviceSettings) && _.isEmpty(setting)) {
          await updateFile('generalSettings', {
            cameraSettings: settingSmall,
            settings: parameter,
            timestamps: moment().toISOString(),
          });
        }

        await cameraSetting(latestSettings?.cameraSettings);
      })
      .catch(async (err) => {
        // if no internet connection, use local settings. if local settings is empty, use  default settings.
        console.log('init camera error:', err);
        const setting = await getTextFile("generalSettings");
        const localSettings = _.pick(setting?.cameraSettings, [
          "cameraFacingFront",
          "faceRotation",
          "isSettingAvailable",
          "cameraPreviewRotation",
          "isCross",
        ]);
        if (!_.isEmpty(localSettings)) {
          await cameraSetting(localSettings);
        } else if (_.isEmpty(localSettings)) {
          await cameraSetting(settingSmall);
        }
      });
  };

  const updateLoadingStatus = (moduleName) => {
    const newStatus = [...statusCompleted];
    const moduleObj = newStatus.find((a) => a.text === moduleName);
    moduleObj.initializing = false;
    setStatusCompleted(newStatus);
  };

  async function initFacePass() {
    console.log("initiating facepass...");
    const setting = await getTextFile("generalSettings");
    try {
      // TODO: INIT REALM
      // TODO: INIT SOCKET
      const gSettings = setting?.settings;

      setcomponentLoad("Initializing Audio Component");
      updateLoadingStatus("Audio");
      try {
        await initAudio();

        setcomponentLoad("Initializing Camera Component");
        try {
          await initCamera();
          updateLoadingStatus("Camera");
          setcomponentLoad("Creating Groups");

          try {
            await setDefaultGroupName(GROUP_NAME);
            updateLoadingStatus("Groups");

            const stgs = !_.isEmpty(gSettings) ? gSettings : parameter;
            setcomponentLoad("Initializing Recognition Module");

            try {
              const res = await initData(stgs);
              console.log(CONSOLE_.FgGreen, `DONE INITIALIZING: ${res}`);
              updateLoadingStatus("Recognition");
            } catch (e) {
              console.log(CONSOLE_.FgRed, `Error initializing with params, ${e}`);
              try {
                await releaseFacePassHandler();
                const res = await initData(stgs);
                console.log(CONSOLE_.FgGreen, `DONE INITIALIZING: ${res}`);
              } catch (reinitError) {
                console.log(CONSOLE_.FgRed, `Failed to reinitialize after error: ${reinitError}`);
                throw reinitError;
              }
            } finally {
              setSuccessInit(true);
            }

            try {
              await checkGroupExist("facepass");
            } catch (e) {
              console.log(CONSOLE_.FgYellow, `Group does not exist, creating: ${e}`);
              try {
                const res = await createGroup("facepass");
                console.log(CONSOLE_.FgGreen, `GROUP CREATED: ${res}`);
              } catch (createError) {
                console.log(CONSOLE_.FgRed, `Failed to create group: ${createError}`);
              }
            }
          } catch (groupError) {
            console.log(CONSOLE_.FgRed, `Error setting default group name: ${groupError}`);
            throw groupError;
          }
        } catch (cameraError) {
          console.log(CONSOLE_.FgRed, `Error initializing camera: ${cameraError}`);
          throw cameraError;
        }
      } catch (audioError) {
        console.log(CONSOLE_.FgRed, `Error initializing audio: ${audioError}`);
        throw audioError;
      }
    } catch (error) {
      console.log(`Error initializing`);
      console.log(CONSOLE_.FgRed, error);
      // Attempt cleanup in case of initialization failure
      try {
        await releaseFacePassHandler();
      } catch (cleanupError) {
        console.log(CONSOLE_.FgRed, `Error during cleanup: ${cleanupError}`);
      }
      throw error; // Re-throw the error for the caller to handle
    }
  }

  React.useEffect(() => {
    let isComponentMounted = true;

    // Initialize FacePass
    initFacePass()
      .then(async () => {
        if (!isComponentMounted) return;

        try {
          const exp = await AsyncStorage.getItem("cameraExposure");
          if (!_.isNil(exp)) {
            console.log('GOT IT 1234', exp);
            await setExposureCompensation(_.parseInt(exp));
          } else {
            await setExposureCompensation(1);
            console.log('GOT NOT 1234', exp);
          }
        } catch (error) {
          console.log('Error setting camera exposure:', error);
          // Set default exposure on error
          try {
            await setExposureCompensation(1);
          } catch (fallbackError) {
            console.log('Error setting default exposure:', fallbackError);
          }
        }
      })
      .catch((error) => {
        if (!isComponentMounted) return;
        console.log('Failed to initialize FacePass:', error);
        // Consider showing an error message to the user here
      });

    // Handle socket initialization
    if (_.isNil(socket)) {
      console.log('initiating socket in api registration page');
      AsyncStorage.getItem("apiKey")
        .then((res) => {
          if (!isComponentMounted) return;

          if (!_.isEmpty(res)) {
            try {
              initSocket(res);
            } catch (socketError) {
              console.log('Error initializing socket:', socketError);
            }
          }
        })
        .catch((error) => {
          if (!isComponentMounted) return;
          console.log('Error getting API key from storage:', error);
        });
    }

    // Cleanup function to prevent state updates after unmount
    return () => {
      isComponentMounted = false;
    };
  }, [socket]);

  React.useEffect(() => {
    console.log(CONSOLE_.FgYellow, "Starting Sync user");
    // initRealm().then(async () => {
    //   // setStatusCompleted((prevState) => [...prevState, "✅ Realm initialized"]);
    //   updateLoadingStatus("Realm");
    //   // run immediately on first load.
    // });

    AsyncStorage.getItem("hideNavbar").then((res) => {
      if (!_.isNil(res)) {
        hideNavigationBar(JSON.parse(res));
      } else {
        hideNavigationBar(true);
        AsyncStorage.setItem("hideNavbar", JSON.stringify(true));
      }
    });
  }, []);

  // write to file
  useEffect(() => {
    initFiles();

    // console.log('reading result from text');
    //
    // const path = `${RNFS.DocumentDirectoryPath}/test.txt`;
    // // RNFS.unlink(path);
    // RNFS.readFile(path) // On Android, use "RNFS.DocumentDirectoryPath" (MainBundlePath is not defined)
    //   .then((result) => {
    //     console.log('GOT RESULT', result);
    //
    //     const parsedJSON = JSON.parse(result.toString());
    //     console.log('before parsing access records:', parsedJSON);
    //     parsedJSON.push({ name: "Brendan" });
    //     console.log('after access records:', JSON.stringify(parsedJSON));
    //
    //     // write the file
    //     RNFS.writeFile(path, JSON.stringify(parsedJSON), 'utf8')
    //       .then((success) => {
    //         console.log('FILE WRITTEN!');
    //       })
    //       .catch((err) => {
    //         console.log(err.message);
    //       });
    //     // stat the first file
    //     return Promise.all([RNFS.stat(result[0].path), result[0].path]);
    //   })
    //   .then((statResult) => {
    //     if (statResult[0].isFile()) {
    //       // if we have a file, read it
    //       // return RNFS.readFile(statResult[1], 'utf8');
    //     }
    //     // // write the file
    //     // RNFS.writeFile(path, JSON.stringify([]), 'utf8')
    //     //   .then((success) => {
    //     //     console.log('FILE WRITTEN!');
    //     //   })
    //     //   .catch((err) => {
    //     //     console.log('file not written', err.message);
    //     //   });
    //
    //     return 'no file';
    //   })
    //   .then((contents) => {
    //     // log the file contents
    //     console.log(contents);
    //   })
    //   .catch((err) => {
    //     if (!_.includes(err.message, "Cannot read property")) {
    //     // write the file
    //       RNFS.writeFile(path, JSON.stringify([]), 'utf8')
    //         .then((success) => {
    //           console.log('FILE WRITTEN!');
    //         });
    //     }
    //     console.log('something wrong when writint file:', err.message);
    //   });
  }, []);

  if (successInit) {
    return (
      <ApiContext.Provider value={{ finalString, setFinalString }}>
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          <Stack.Screen name="Registration" component={ApiRegistration} />
          <Stack.Screen name="Home" component={Home} />
          <Stack.Screen name="Scan" component={ScanFaceMain} />
          <Stack.Screen name="Settings" component={SettingsPage} />
          <Stack.Screen name="Doorbell" component={DoorbellPage} />
        </Stack.Navigator>
      </ApiContext.Provider>
    );
  }

  return (
    // TODO: Add a loading UI
    <View
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        height: Dimensions.get("screen").height * 0.8,
      }}
    >
      <View
        style={{
          width: "100%",
          padding: "10%",
          display: "flex",
          alignItems: "center",
        }}
      >
        <Text
          style={{
            textAlign: "center",
            // marginBottom: 20,
            fontSize: 20,
            position: "absolute",
            display: "flex",
            top: 20,
          }}
        >
          App is Loading. Please wait
        </Text>
        <View
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
            marginBottom: 20,
          }}
        >
          <Text
            style={{
              textAlign: "center",
              marginRight: 10,
              // fontSize: 20,
            }}
          >
            {componentLoad}
          </Text>
          {/* <ActivityIndicator size="small" color="#00f" /> */}
        </View>
        <View
          style={{
            marginBottom: 40,
            width: Dimensions.get("screen").width * 0.6,
            display: "flex",
            justifyContent: "center",
            // borderWidth: 1,
          }}
        >
          {_.map(statusCompleted, (val, idx) => (
            <View
              key={idx}
              style={{
                display: "flex",
                flexDirection: "row",
                marginBottom: 5,
              }}
            >
              {val.initializing ? (
                <ActivityIndicator size="small" />
              ) : (
                <Text>✅</Text>
              )}

              <Text style={{ textAlign: "justify", fontSize: 16 }}>
                {val.initializing
                  ? `${val.text} initializing...`
                  : `${val.text} Ready`}
              </Text>
            </View>
          ))}
        </View>
      </View>
    </View>
  );
}

function RealmApp() {
  return (
    <NavigationContainer>
      <App />
    </NavigationContainer>
  );
}

const CONSOLE_ = {
  Reset: "\x1b[0m",
  Bright: "\x1b[1m",
  Dim: "\x1b[2m",
  Underscore: "\x1b[4m",
  Blink: "\x1b[5m",
  Reverse: "\x1b[7m",
  Hidden: "\x1b[8m",

  FgBlack: "\x1b[30m",
  FgRed: "\x1b[31m",
  FgGreen: "\x1b[32m",
  FgYellow: "\x1b[33m",
  FgBlue: "\x1b[34m",
  FgMagenta: "\x1b[35m",
  FgCyan: "\x1b[36m",
  FgWhite: "\x1b[37m",
  FgGray: "\x1b[90m",
};

export default RealmApp;
