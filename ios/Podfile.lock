PODS:
  - AgoraIrisRTC_iOS (4.2.1-build.1)
  - AgoraRtcEngine_iOS (4.2.1):
    - AgoraRtcEngine_iOS/AIAEC (= 4.2.1)
    - AgoraRtcEngine_iOS/AINS (= 4.2.1)
    - AgoraRtcEngine_iOS/AudioBeauty (= 4.2.1)
    - AgoraRtcEngine_iOS/ClearVision (= 4.2.1)
    - AgoraRtcEngine_iOS/ContentInspect (= 4.2.1)
    - AgoraRtcEngine_iOS/DRM (= 4.2.1)
    - AgoraRtcEngine_iOS/FaceDetection (= 4.2.1)
    - AgoraRtcEngine_iOS/ReplayKit (= 4.2.1)
    - AgoraRtcEngine_iOS/RtcBasic (= 4.2.1)
    - AgoraRtcEngine_iOS/SpatialAudio (= 4.2.1)
    - AgoraRtcEngine_iOS/VideoCodecDec (= 4.2.1)
    - AgoraRtcEngine_iOS/VideoCodecEnc (= 4.2.1)
    - AgoraRtcEngine_iOS/VirtualBackground (= 4.2.1)
    - AgoraRtcEngine_iOS/VQA (= 4.2.1)
  - AgoraRtcEngine_iOS/AIAEC (4.2.1)
  - AgoraRtcEngine_iOS/AINS (4.2.1)
  - AgoraRtcEngine_iOS/AudioBeauty (4.2.1)
  - AgoraRtcEngine_iOS/ClearVision (4.2.1)
  - AgoraRtcEngine_iOS/ContentInspect (4.2.1)
  - AgoraRtcEngine_iOS/DRM (4.2.1)
  - AgoraRtcEngine_iOS/FaceDetection (4.2.1)
  - AgoraRtcEngine_iOS/ReplayKit (4.2.1)
  - AgoraRtcEngine_iOS/RtcBasic (4.2.1)
  - AgoraRtcEngine_iOS/SpatialAudio (4.2.1)
  - AgoraRtcEngine_iOS/VideoCodecDec (4.2.1)
  - AgoraRtcEngine_iOS/VideoCodecEnc (4.2.1)
  - AgoraRtcEngine_iOS/VirtualBackground (4.2.1)
  - AgoraRtcEngine_iOS/VQA (4.2.1)
  - boost (1.76.0)
  - DoubleConversion (1.1.6)
  - EXApplication (5.3.0):
    - ExpoModulesCore
  - EXAV (13.4.1):
    - ExpoModulesCore
    - ReactCommon/turbomodule/core
  - EXCamera (13.4.2):
    - ExpoModulesCore
  - EXConstants (14.4.2):
    - ExpoModulesCore
  - EXFileSystem (15.4.4):
    - ExpoModulesCore
  - EXFont (11.4.0):
    - ExpoModulesCore
  - EXJSONUtils (0.7.1)
  - EXManifests (0.7.1):
    - ExpoModulesCore
  - Expo (49.0.7):
    - ExpoModulesCore
  - expo-dev-client (2.4.6):
    - EXManifests
    - expo-dev-launcher
    - expo-dev-menu
    - expo-dev-menu-interface
    - EXUpdatesInterface
  - expo-dev-launcher (2.4.8):
    - EXManifests
    - expo-dev-launcher/Main (= 2.4.8)
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-RCTAppDelegate
  - expo-dev-launcher/Main (2.4.8):
    - EXManifests
    - expo-dev-launcher/Unsafe
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-RCTAppDelegate
  - expo-dev-launcher/Unsafe (2.4.8):
    - EXManifests
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-RCTAppDelegate
  - expo-dev-menu (3.1.8):
    - expo-dev-menu/Main (= 3.1.8)
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - expo-dev-menu-interface (1.3.0)
  - expo-dev-menu/Main (3.1.8):
    - EXManifests
    - expo-dev-menu-interface
    - expo-dev-menu/Vendored
    - ExpoModulesCore
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - expo-dev-menu/SafeAreaView (3.1.8):
    - ExpoModulesCore
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - expo-dev-menu/Vendored (3.1.8):
    - expo-dev-menu/SafeAreaView
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - ExpoDevice (5.4.0):
    - ExpoModulesCore
  - ExpoHead (0.0.11):
    - ExpoModulesCore
  - ExpoKeepAwake (12.3.0):
    - ExpoModulesCore
  - ExpoLinearGradient (12.3.0):
    - ExpoModulesCore
  - ExpoModulesCore (1.5.9):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - ReactCommon/turbomodule/core
  - EXSplashScreen (0.20.5):
    - ExpoModulesCore
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - EXUpdatesInterface (0.10.1)
  - facepass-react-native-module (0.1.42):
    - React-Core
  - FBLazyVector (0.72.3)
  - FBReactNativeSpec (0.72.3):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.72.3)
    - RCTTypeSafety (= 0.72.3)
    - React-Core (= 0.72.3)
    - React-jsi (= 0.72.3)
    - ReactCommon/turbomodule/core (= 0.72.3)
  - fmt (6.2.1)
  - glog (0.3.5)
  - hermes-engine (0.72.3):
    - hermes-engine/Pre-built (= 0.72.3)
  - hermes-engine/Pre-built (0.72.3)
  - libevent (2.1.12)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.72.3)
  - RCTTypeSafety (0.72.3):
    - FBLazyVector (= 0.72.3)
    - RCTRequired (= 0.72.3)
    - React-Core (= 0.72.3)
  - React (0.72.3):
    - React-Core (= 0.72.3)
    - React-Core/DevSupport (= 0.72.3)
    - React-Core/RCTWebSocket (= 0.72.3)
    - React-RCTActionSheet (= 0.72.3)
    - React-RCTAnimation (= 0.72.3)
    - React-RCTBlob (= 0.72.3)
    - React-RCTImage (= 0.72.3)
    - React-RCTLinking (= 0.72.3)
    - React-RCTNetwork (= 0.72.3)
    - React-RCTSettings (= 0.72.3)
    - React-RCTText (= 0.72.3)
    - React-RCTVibration (= 0.72.3)
  - React-callinvoker (0.72.3)
  - React-Codegen (0.72.3):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.3)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.3)
    - React-Core/RCTWebSocket (= 0.72.3)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.72.3)
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.3)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.72.3):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.3)
    - React-Codegen (= 0.72.3)
    - React-Core/CoreModulesHeaders (= 0.72.3)
    - React-jsi (= 0.72.3)
    - React-RCTBlob
    - React-RCTImage (= 0.72.3)
    - ReactCommon/turbomodule/core (= 0.72.3)
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.72.3):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.3)
    - React-debug (= 0.72.3)
    - React-jsi (= 0.72.3)
    - React-jsinspector (= 0.72.3)
    - React-logger (= 0.72.3)
    - React-perflogger (= 0.72.3)
    - React-runtimeexecutor (= 0.72.3)
  - React-debug (0.72.3)
  - React-hermes (0.72.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.72.3)
    - React-jsi
    - React-jsiexecutor (= 0.72.3)
    - React-jsinspector (= 0.72.3)
    - React-perflogger (= 0.72.3)
  - React-jsi (0.72.3):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.72.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.72.3)
    - React-jsi (= 0.72.3)
    - React-perflogger (= 0.72.3)
  - React-jsinspector (0.72.3)
  - React-logger (0.72.3):
    - glog
  - react-native-agora (4.2.1):
    - AgoraIrisRTC_iOS (= 4.2.1-build.1)
    - AgoraRtcEngine_iOS (= 4.2.1)
    - React-Core
  - react-native-get-random-values (1.9.0):
    - React-Core
  - react-native-safe-area-context (4.6.3):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - ReactCommon/turbomodule/core
  - React-NativeModulesApple (0.72.3):
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.72.3)
  - React-RCTActionSheet (0.72.3):
    - React-Core/RCTActionSheetHeaders (= 0.72.3)
  - React-RCTAnimation (0.72.3):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.3)
    - React-Codegen (= 0.72.3)
    - React-Core/RCTAnimationHeaders (= 0.72.3)
    - React-jsi (= 0.72.3)
    - ReactCommon/turbomodule/core (= 0.72.3)
  - React-RCTAppDelegate (0.72.3):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-NativeModulesApple
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.72.3):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.3)
    - React-Core/RCTBlobHeaders (= 0.72.3)
    - React-Core/RCTWebSocket (= 0.72.3)
    - React-jsi (= 0.72.3)
    - React-RCTNetwork (= 0.72.3)
    - ReactCommon/turbomodule/core (= 0.72.3)
  - React-RCTImage (0.72.3):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.3)
    - React-Codegen (= 0.72.3)
    - React-Core/RCTImageHeaders (= 0.72.3)
    - React-jsi (= 0.72.3)
    - React-RCTNetwork (= 0.72.3)
    - ReactCommon/turbomodule/core (= 0.72.3)
  - React-RCTLinking (0.72.3):
    - React-Codegen (= 0.72.3)
    - React-Core/RCTLinkingHeaders (= 0.72.3)
    - React-jsi (= 0.72.3)
    - ReactCommon/turbomodule/core (= 0.72.3)
  - React-RCTNetwork (0.72.3):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.3)
    - React-Codegen (= 0.72.3)
    - React-Core/RCTNetworkHeaders (= 0.72.3)
    - React-jsi (= 0.72.3)
    - ReactCommon/turbomodule/core (= 0.72.3)
  - React-RCTSettings (0.72.3):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.3)
    - React-Codegen (= 0.72.3)
    - React-Core/RCTSettingsHeaders (= 0.72.3)
    - React-jsi (= 0.72.3)
    - ReactCommon/turbomodule/core (= 0.72.3)
  - React-RCTText (0.72.3):
    - React-Core/RCTTextHeaders (= 0.72.3)
  - React-RCTVibration (0.72.3):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.3)
    - React-Core/RCTVibrationHeaders (= 0.72.3)
    - React-jsi (= 0.72.3)
    - ReactCommon/turbomodule/core (= 0.72.3)
  - React-rncore (0.72.3)
  - React-runtimeexecutor (0.72.3):
    - React-jsi (= 0.72.3)
  - React-runtimescheduler (0.72.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-runtimeexecutor
  - React-utils (0.72.3):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-debug
  - ReactCommon/turbomodule/bridging (0.72.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.3)
    - React-cxxreact (= 0.72.3)
    - React-jsi (= 0.72.3)
    - React-logger (= 0.72.3)
    - React-perflogger (= 0.72.3)
  - ReactCommon/turbomodule/core (0.72.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.3)
    - React-cxxreact (= 0.72.3)
    - React-jsi (= 0.72.3)
    - React-logger (= 0.72.3)
    - React-perflogger (= 0.72.3)
  - RealmJS (11.10.2):
    - React
  - rn-fetch-blob (0.12.0):
    - React-Core
  - RNCAsyncStorage (1.18.2):
    - React-Core
  - RNCPicker (2.5.0):
    - React-Core
  - RNDeviceInfo (10.8.0):
    - React-Core
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.12.1):
    - React-Core
  - RNScreens (3.22.1):
    - React-Core
    - React-RCTImage
  - SocketRocket (0.6.1)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EXApplication (from `../node_modules/expo-application/ios`)
  - EXAV (from `../node_modules/expo-av/ios`)
  - EXCamera (from `../node_modules/expo-camera/ios`)
  - EXConstants (from `../node_modules/expo-constants/ios`)
  - EXFileSystem (from `../node_modules/expo-file-system/ios`)
  - EXFont (from `../node_modules/expo-font/ios`)
  - EXJSONUtils (from `../node_modules/expo-json-utils/ios`)
  - EXManifests (from `../node_modules/expo-manifests/ios`)
  - Expo (from `../node_modules/expo`)
  - expo-dev-client (from `../node_modules/expo-dev-client/ios`)
  - expo-dev-launcher (from `../node_modules/expo-dev-launcher`)
  - expo-dev-menu (from `../node_modules/expo-dev-menu`)
  - expo-dev-menu-interface (from `../node_modules/expo-dev-menu-interface/ios`)
  - ExpoDevice (from `../node_modules/expo-device/ios`)
  - ExpoHead (from `../node_modules/expo-head/ios`)
  - ExpoKeepAwake (from `../node_modules/expo-keep-awake/ios`)
  - ExpoLinearGradient (from `../node_modules/expo-linear-gradient/ios`)
  - ExpoModulesCore (from `../node_modules/expo-modules-core`)
  - EXSplashScreen (from `../node_modules/expo-splash-screen/ios`)
  - EXUpdatesInterface (from `../node_modules/expo-updates-interface/ios`)
  - facepass-react-native-module (from `../node_modules/facepass-react-native-module`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-agora (from `../node_modules/react-native-agora`)
  - react-native-get-random-values (from `../node_modules/react-native-get-random-values`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - RealmJS (from `../node_modules/realm`)
  - rn-fetch-blob (from `../node_modules/rn-fetch-blob`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - AgoraIrisRTC_iOS
    - AgoraRtcEngine_iOS
    - fmt
    - libevent
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EXApplication:
    :path: "../node_modules/expo-application/ios"
  EXAV:
    :path: "../node_modules/expo-av/ios"
  EXCamera:
    :path: "../node_modules/expo-camera/ios"
  EXConstants:
    :path: "../node_modules/expo-constants/ios"
  EXFileSystem:
    :path: "../node_modules/expo-file-system/ios"
  EXFont:
    :path: "../node_modules/expo-font/ios"
  EXJSONUtils:
    :path: "../node_modules/expo-json-utils/ios"
  EXManifests:
    :path: "../node_modules/expo-manifests/ios"
  Expo:
    :path: "../node_modules/expo"
  expo-dev-client:
    :path: "../node_modules/expo-dev-client/ios"
  expo-dev-launcher:
    :path: "../node_modules/expo-dev-launcher"
  expo-dev-menu:
    :path: "../node_modules/expo-dev-menu"
  expo-dev-menu-interface:
    :path: "../node_modules/expo-dev-menu-interface/ios"
  ExpoDevice:
    :path: "../node_modules/expo-device/ios"
  ExpoHead:
    :path: "../node_modules/expo-head/ios"
  ExpoKeepAwake:
    :path: "../node_modules/expo-keep-awake/ios"
  ExpoLinearGradient:
    :path: "../node_modules/expo-linear-gradient/ios"
  ExpoModulesCore:
    :path: "../node_modules/expo-modules-core"
  EXSplashScreen:
    :path: "../node_modules/expo-splash-screen/ios"
  EXUpdatesInterface:
    :path: "../node_modules/expo-updates-interface/ios"
  facepass-react-native-module:
    :path: "../node_modules/facepass-react-native-module"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2023-03-20-RNv0.72.0-49794cfc7c81fb8f69fd60c3bbf85a7480cc5a77
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-agora:
    :path: "../node_modules/react-native-agora"
  react-native-get-random-values:
    :path: "../node_modules/react-native-get-random-values"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RealmJS:
    :path: "../node_modules/realm"
  rn-fetch-blob:
    :path: "../node_modules/rn-fetch-blob"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  AgoraIrisRTC_iOS: 4c6cb9df772dacba4e0dccb00178378454d03fde
  AgoraRtcEngine_iOS: bbbacbf4797a6f85532c6023b72c4d66f2b49227
  boost: 57d2868c099736d80fcd648bf211b4431e51a558
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  EXApplication: 02655a251434d564bb0e73291f5a490c74b5b76f
  EXAV: f393dfc0b28214d62855a31e06eb21d426d6e2da
  EXCamera: 0fbfa338a3776af2722d626a3437abe33f708aad
  EXConstants: ce5bbea779da8031ac818c36bea41b10e14d04e1
  EXFileSystem: 2b826a3bf1071a4b80a8457e97124783d1ac860e
  EXFont: 738c44c390953ebcbab075a4848bfbef025fd9ee
  EXJSONUtils: 6802be4282d42b97c51682468ddc1026a06f8276
  EXManifests: 8a4a480e3b58891d5657d3996a39229e5c96d912
  Expo: d310f3b84bd30843324d41f23ea7cc5e4273abf1
  expo-dev-client: 4f773679557bc150e44eea1a1e3a3fd65ab82a5a
  expo-dev-launcher: de065738958421c704ee024f4fe1bf14b2d8a713
  expo-dev-menu: 9b3792cc2241011b63747cc77d06b0dcbcc57780
  expo-dev-menu-interface: bda969497e73dadc2663c479e0fa726ca79a306e
  ExpoDevice: 1c1b0c9cad96c292c1de73948649cfd654b2b3c0
  ExpoHead: 8da645502363b53eeb99d99276a0adbe0ec8cff0
  ExpoKeepAwake: be4cbd52d9b177cde0fd66daa1913afa3161fc1d
  ExpoLinearGradient: 5966dd5d49872cc9c104fedc8bbc298b6049b2e8
  ExpoModulesCore: e4e437139259c5a73530a8895af69774ff8ec12d
  EXSplashScreen: c0e7f2d4a640f3b875808ed0b88575538daf6d82
  EXUpdatesInterface: 82ed48d417cdcd376c12ca1c2ce390d35500bed6
  facepass-react-native-module: 99ab67cb768320e48152631308dbc61247d40514
  FBLazyVector: 4cce221dd782d3ff7c4172167bba09d58af67ccb
  FBReactNativeSpec: c6bd9e179757b3c0ecf815864fae8032377903ef
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  hermes-engine: 10fbd3f62405c41ea07e71973ea61e1878d07322
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: a2faf4bad4e438ca37b2040cb8f7799baa065c18
  RCTTypeSafety: cb09f3e4747b6d18331a15eb05271de7441ca0b3
  React: 13109005b5353095c052f26af37413340ccf7a5d
  React-callinvoker: c8c87bce983aa499c13cb06d4447c025a35274d6
  React-Codegen: 712d523524d89d71f1cf7cc624854941be983c4d
  React-Core: 688f88b7f3a3d30b4848036223f8b07102c687e5
  React-CoreModules: 63c063a3ade8fb3b1bec5fd9a50f17b0421558c6
  React-cxxreact: 37765b4975541105b2a3322a4b473417c158c869
  React-debug: 51f11ef8db14b47f24e71c42a4916d4192972156
  React-hermes: 935ae71fb3d7654e947beba8498835cd5e479707
  React-jsi: ec628dc7a15ffea969f237b0ea6d2fde212b19dd
  React-jsiexecutor: 59d1eb03af7d30b7d66589c410f13151271e8006
  React-jsinspector: b511447170f561157547bc0bef3f169663860be7
  React-logger: c5b527272d5f22eaa09bb3c3a690fee8f237ae95
  react-native-agora: 05bca96fe1ade57ad2630596a0f4d2b579849e62
  react-native-get-random-values: dee677497c6a740b71e5612e8dbd83e7539ed5bb
  react-native-safe-area-context: 36cc67648134e89465663b8172336a19eeda493d
  React-NativeModulesApple: c57f3efe0df288a6532b726ad2d0322a9bf38472
  React-perflogger: 6bd153e776e6beed54c56b0847e1220a3ff92ba5
  React-RCTActionSheet: c0b62af44e610e69d9a2049a682f5dba4e9dff17
  React-RCTAnimation: f9bf9719258926aea9ecb8a2aa2595d3ff9a6022
  React-RCTAppDelegate: e5ac35d4dbd1fae7df3a62b47db04b6a8d151592
  React-RCTBlob: c4f1e69a6ef739aa42586b876d637dab4e3b5bed
  React-RCTImage: e5798f01aba248416c02a506cf5e6dfcba827638
  React-RCTLinking: f5b6227c879e33206f34e68924c458f57bbb96d9
  React-RCTNetwork: d5554fbfac1c618da3c8fa29933108ea22837788
  React-RCTSettings: 189c71e3e6146ba59f4f7e2cbeb494cf2ad42afa
  React-RCTText: 19425aea9d8b6ccae55a27916355b17ab577e56e
  React-RCTVibration: 388ac0e1455420895d1ca2548401eed964b038a6
  React-rncore: 755a331dd67b74662108f2d66a384454bf8dc1a1
  React-runtimeexecutor: 369ae9bb3f83b65201c0c8f7d50b72280b5a1dbc
  React-runtimescheduler: 837c1bebd2f84572db17698cd702ceaf585b0d9a
  React-utils: bcb57da67eec2711f8b353f6e3d33bd8e4b2efa3
  ReactCommon: 3ccb8fb14e6b3277e38c73b0ff5e4a1b8db017a9
  RealmJS: 73a36da3cbbe85e1bdcbf55683172b51f35070d3
  rn-fetch-blob: f065bb7ab7fb48dd002629f8bdcb0336602d3cba
  RNCAsyncStorage: ddc4ee162bfd41b0d2c68bf2d95acd81dd7f1f93
  RNCPicker: 32ca102146bc7d34a8b93a998d9938d9f9ec7898
  RNDeviceInfo: 5795b418ed3451ebcaf39384e6cf51f60cb931c9
  RNFS: 4ac0f0ea233904cb798630b3c077808c06931688
  RNGestureHandler: c0d04458598fcb26052494ae23dda8f8f5162b13
  RNScreens: 50ffe2fa2342eabb2d0afbe19f7c1af286bc7fb3
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  Yoga: 8796b55dba14d7004f980b54bcc9833ee45b28ce

PODFILE CHECKSUM: c3a49d6a78de8c841c867df46b11f02e5ea0e3fe

COCOAPODS: 1.11.3
