/* eslint-disable import/no-mutable-exports */
const accessDeniedAudioFile = require(`./assets/sounds/access_denied.mp3`);
const checkInAudioFile = require(`./assets/sounds/check_in.mp3`);
const checkOutAudioFile = require(`./assets/sounds/check_out.mp3`);
const accessGrantedAudioFile = require(`./assets/sounds/access_granted.mp3`);
const { Audio } = require("expo-av");

let accessDeniedAudio = null;
let checkInAudio = null;
let checkOutAudio = null;
let accessGrantedAudio = null;

const initAudio = async () => {
  accessDeniedAudio = await Audio.Sound.createAsync(accessDeniedAudioFile);
  checkInAudio = await Audio.Sound.createAsync(checkInAudioFile);
  checkOutAudio = await Audio.Sound.createAsync(checkOutAudioFile);
  accessGrantedAudio = await Audio.Sound.createAsync(accessGrantedAudioFile);

  console.log('audio - is accessDeniedAudio loaded:', accessDeniedAudio.status.isLoaded);
};

export {
  initAudio,
  accessDeniedAudio,
  accessGrantedAudio,
  checkInAudio,
  checkOutAudio,
};
