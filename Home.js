import React, { useEffect, useState } from "react";
import { LinearGradient } from "expo-linear-gradient";
import {
  ImageBackground,
  Dimensions,
  StyleSheet,
  Text,
  View,
  Image,
  Pressable,
  SafeAreaView,
} from "react-native";
import _ from "lodash";
import { getSerialNumber } from "react-native-device-info";
import AsyncStorage from "@react-native-async-storage/async-storage";
import FloatingButton from "./components/FloatingButton";
import api from "./helpers/api";
import { updateFile } from "./helpers/handlingTextFile";

export default Home = function (props) {
  // const accessRecords = useQuery("accessrecords");
  // const realm = useRealm();
  // const userRealm = userUseRealm();
  // const users = userUseQuery("user");

  // const initRealm = async () => {
  //   try {
  //     console.log("realm");
  //     console.log("subscription state:", realm.subscriptions.error);

  //     await realm.subscriptions.update(async (mutableSubs) => {
  //       mutableSubs.removeByName("test");
  //       const t = realm.objects("accessrecords");
  //       mutableSubs.add(t, { name: "test" });
  //     });
  //   } catch (error) {
  //     console.log(CONSOLE_.FgRed, error);
  //   }
  // };

  // useEffect(() => {
  //   initRealm();
  //   console.log("access records:", _.size(accessRecords));
  // }, [realm]);

  // useEffect(() => {
  //   initRealm().then(async () => {
  //     console.log("syncing users...");
  //     setInterval(async () => {
  //       await syncUser(userRealm, users);
  //     }, 5000);
  //   });
  // }, []);

  const getSerial = async () => {
    console.log('get serial number:', await getSerialNumber());
  };

  React.useEffect(() => {
    getSerial();
  }, []);

  return (
    <View>
      <HomePage {...props} />
    </View>
  );
};

function HomePage({ route, navigation }) {
  return (
    <SafeAreaView style={styles.body}>
      <ImageBackground
        source={require("./assets/FRBackground.png")}
        resizeMode="repeat"
      >
        <View style={styles.logo}>
          <Image
            resizeMethod="resize"
            resizeMode="contain"
            style={{
              maxWidth: 0.7 * Dimensions.get("screen").width,
              marginTop: 50,
            }}
            source={require("./assets/qv_logo_new.png")}
          />
        </View>
        <View style={styles.controls}>
          <View style={{ display: "flex", flexDirection: "row" }}>
            <StyleButton
              text="Check-In"
              color1="#093c75"
              color2="#0d2458"
              imageSource={require("./assets/checkinIcon.png")}
              onPress={() => navigation.navigate("Scan", {
                type: "checkIn",
              })}
            />
            <StyleButton
              text="Check-Out"
              color1="#FF9C06"
              color2="#FF9C06"
              imageSource={require("./assets/checkout.png")}
              onPress={() => navigation.navigate("Scan", {
                type: "checkOut",
              })}
            />
          </View>
          <View style={{ display: "flex", flexDirection: "row" }}>
            <StyleButton
              text="Lunch-Out"
              color1="#309831"
              color2="#008001"
              imageSource={require("./assets/lunchout.png")}
              onPress={() => navigation.navigate("Scan", {
                type: "lunchOut",
              })}
            />
            <StyleButton
              text="Lunch-In"
              color1="#e12f2f"
              color2="#da0001"
              imageSource={require("./assets/lunchin.png")}
              onPress={() => navigation.navigate("Scan", {
                type: "lunchIn",
              })}
            />
          </View>
          <View style={{ display: "flex", flexDirection: "row" }}>
            <StyleButton
              text="Scan"
              color1="#093c75"
              color2="#0d2458"
              imageSource={require("./assets/scanicon.png")}
              onPress={() => navigation.navigate("Scan", {
                type: "none",
              })}
            />
          </View>
          {/* <View style={{ display: "flex", flexDirection: "row" }}>
            <StyleButton

              text="View Doorbell"
              color1="#093c75"
              color2="#0d2458"
              imageSource={require("./assets/viewdoorbell.png")}
              onPress={() => navigation.navigate("Doorbell")}
            />
          </View> */}
        </View>
      </ImageBackground>
      <FloatingButton link="Settings" />
    </SafeAreaView>
  );
}

function StyleButton({
  color1 = "#777",
  color2 = "#777",
  text = "TEXT_BUTTON",
  imageSource = require("./assets/checkout.png"),
  onPress = () => null,
  disabled = false,
}) {
  return (
    <Pressable
      style={{
        borderRadius: 10,
        flex: 1,
        margin: 15,
      }}
      disabled={disabled}
      onPress={onPress}
    >
      <LinearGradient
        colors={[color1, color2]}
        style={{
          padding: 17,
          borderRadius: 10,
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
        }}
      >
        <Image style={{ height: 20, width: 20 }} source={imageSource} />
        <Text
          style={{
            color: "#FFF",
            fontSize: 20,
            textAlign: "center",
            flex: 1,
            fontWeight: "600",
            // TODO: ADD POPPINS FONT8
          }}
        >
          {text}
        </Text>
      </LinearGradient>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  body: {
    backgroundColor: "#fff",
    minHeight: Dimensions.get("screen").height,
  },
  logo: {
    height: 0.45 * Dimensions.get("screen").height,
    alignItems: "center",
    justifyContent: "center",
  },
  controls: {
    padding: 15,
    display: "flex",
  },
  checkin: {
    fontSize: 30,
    borderRadius: 10,
    padding: 10,
    backgroundColor:
      "background: linear-gradient(180deg, rgba(12, 35, 88, 0.4) -132.1%, #0C2358 68.31%)",
    // padding:10,
    // fontWeight:'bold'
  },
});

const CONSOLE_ = {
  Reset: "\x1b[0m",
  Bright: "\x1b[1m",
  Dim: "\x1b[2m",
  Underscore: "\x1b[4m",
  Blink: "\x1b[5m",
  Reverse: "\x1b[7m",
  Hidden: "\x1b[8m",

  FgBlack: "\x1b[30m",
  FgRed: "\x1b[31m",
  FgGreen: "\x1b[32m",
  FgYellow: "\x1b[33m",
  FgBlue: "\x1b[34m",
  FgMagenta: "\x1b[35m",
  FgCyan: "\x1b[36m",
  FgWhite: "\x1b[37m",
  FgGray: "\x1b[90m",
};

// import { LinearGradient } from "expo-linear-gradient";
// import React, { useEffect, useState } from "react";
// import {
//   ImageBackground,
//   Dimensions,
//   StyleSheet,
//   Text,
//   View,
//   Image,
//   Pressable,
//   SafeAreaView,
//   Button,
// } from "react-native";
// import { io } from "socket.io-client";
// import Realm from "realm";
// import { deleteFace, getGroupInfo } from "facepass-react-native-module";
// import { useApp, useObject } from "@realm/react";
// import FloatingButton from "./components/FloatingButton";
// import SocketSync from "./components/socketSync";
// import {
//   AccessRecordsSchema,
//   UserSchema,
//   useQuery,
//   useRealm,
//   userUseQuery,
//   userUseRealm,
// } from "./helpers/realm";
// import socket, { syncUser } from "./socket";

// function Home(props) {
//   const accessRecords = useQuery("accessrecords");
//   const realm = useRealm();

//   const userRealm = userUseRealm();
//   const users = userUseQuery("user");

//   const initRealm = async () => {
//     try {
//       console.log("realm");
//       console.log("subscription state:", realm.subscriptions.error);
//       // realm.write(async () => {
//       //   // realm.delete(accessRecords);
//       //   await realm.create('accessrecords', {
//       //     _id: new Realm.BSON.ObjectId(),
//       //     premiseId: new Realm.BSON.ObjectId(),
//       //     deviceId: new Realm.BSON.ObjectId(),
//       //     user: new Realm.BSON.ObjectId(),
//       //     score: 'string',
//       //     trackId: 'string',
//       //     prediction: 'string',
//       //     type: 'string',
//       //     attributes: 'string',
//       //   });
//       //   console.log('data written successfully');
//       // });
//       await realm.subscriptions.update(async (mutableSubs) => {
//         mutableSubs.removeByName("test");
//         const t = realm.objects("accessrecords");
//         mutableSubs.add(t, { name: "test" });
//       });
//     } catch (err) {
//       console.warn(err);
//     }
//   };

//   useEffect(() => {
//     initRealm();
//     console.log("access records:", accessRecords);
//   }, [realm]);

//   React.useEffect(() => {
//     syncUser(userRealm, users);
//   }, []);

//   return (
//     // <SocketSync>
//     <HomePage {...props} />
//     // </SocketSync>
//   );
// }

// function HomePage({ route, navigation }) {
//   const [dimensions, setdimensions] = useState({ width: 0, height: 0 });
//   useEffect(() => {
//     if (Dimensions) {
//       setdimensions({
//         width: Dimensions.get("window").width,
//         height: Dimensions.get("window").height,
//       });
//     }
//   }, [Dimensions]);

//   return (
//     <SafeAreaView style={styles.body}>
//       <ImageBackground
//         source={require("./assets/FRBackground.png")}
//         resizeMode="stretch"
//       >
//         <View style={styles.logo}>
//           <Image
//             resizeMethod="resize"
//             resizeMode="contain"
//             style={{ maxWidth: 0.9 * dimensions.width }}
//             source={require("./assets/qv_logo_new.png")}
//           />
//         </View>
//         <Button
//           style={{
//             paddingTop: 100,
//           }}
//           title="remove face from device"
//           onPress={async () => {
//             const faceTokenListArray = await getGroupInfo("facepass");
//             console.log(faceTokenListArray);
//             faceTokenListArray.map(async (token) => {
//               await deleteFace(token, "facepass");
//             });
//           }}
//         />
//         <View style={styles.controls}>
//           <View style={{ display: "flex", flexDirection: "row" }}>
//             <StyleButton
//               text="Check-In"
//               color1="#093c75"
//               color2="#0d2458"
//               imageSource={require("./assets/checkinIcon.png")}
//               onPress={() =>
//                 navigation.navigate("Scan", {
//                   type: "checkIn",
//                 })
//               }
//             />
//             <StyleButton
//               text="Check-Out"
//               color1="#FF9C06"
//               color2="#FF9C06"
//               imageSource={require("./assets/checkout.png")}
//               onPress={() =>
//                 navigation.navigate("Scan", {
//                   type: "checkOut",
//                 })
//               }
//             />
//           </View>
//           <View style={{ display: "flex", flexDirection: "row" }}>
//             <StyleButton
//               text="Lunch-Out"
//               color1="#309831"
//               color2="#008001"
//               imageSource={require("./assets/lunchout.png")}
//               onPress={() =>
//                 navigation.navigate("Scan", {
//                   type: "lunchOut",
//                 })
//               }
//             />
//             <StyleButton
//               text="Lunch-In"
//               color1="#e12f2f"
//               color2="#da0001"
//               imageSource={require("./assets/lunchin.png")}
//               onPress={() =>
//                 navigation.navigate("Scan", {
//                   type: "lunchIn",
//                 })
//               }
//             />
//           </View>
//           <View style={{ display: "flex", flexDirection: "row" }}>
//             <StyleButton
//               text="Scan"
//               color1="#093c75"
//               color2="#0d2458"
//               imageSource={require("./assets/scanicon.png")}
//               onPress={() =>
//                 navigation.navigate("Scan", {
//                   type: "none",
//                 })
//               }
//             />
//           </View>
//           <View style={{ display: "flex", flexDirection: "row" }}>
//             <StyleButton
//               text="View Doorbell"
//               color1="#093c75"
//               color2="#0d2458"
//               imageSource={require("./assets/viewdoorbell.png")}
//               onPress={() => navigation.navigate("Connection")}
//               // onPress={() => navigation.navigate("Scan")}
//             />
//           </View>
//         </View>
//       </ImageBackground>
//       <FloatingButton link="Settings" />
//     </SafeAreaView>
//   );
// }

// function StyleButton({
//   color1 = "#777",
//   color2 = "#777",
//   text = "TEXT_BUTTON",
//   imageSource = require("./assets/checkout.png"),
//   onPress = () => null,
// }) {
//   return (
//     <Pressable
//       style={{
//         borderRadius: 10,
//         flex: 1,
//         margin: 15,
//       }}
//       onPress={onPress}
//     >
//       <LinearGradient
//         colors={[color1, color2]}
//         style={{
//           padding: 17,
//           borderRadius: 10,
//           display: "flex",
//           flexDirection: "row",
//           alignItems: "center",
//         }}
//       >
//         <Image style={{ height: 20, width: 20 }} source={imageSource} />
//         <Text
//           style={{
//             color: "#FFF",
//             fontSize: 20,
//             textAlign: "center",
//             flex: 1,
//             fontWeight: "600",
//             // TODO: ADD POPPINS FONT8
//           }}
//         >
//           {text}
//         </Text>
//       </LinearGradient>
//     </Pressable>
//   );
// }

// const styles = StyleSheet.create({
//   body: {
//     backgroundColor: "#fff",
//   },
//   logo: {
//     height: 0.45 * Dimensions.get("screen").height,
//     alignItems: "center",
//     justifyContent: "center",
//   },
//   controls: {
//     padding: 15,
//     display: "flex",
//   },
//   checkin: {
//     fontSize: 30,
//     borderRadius: 10,
//     padding: 10,
//     backgroundColor:
//       "background: linear-gradient(180deg, rgba(12, 35, 88, 0.4) -132.1%, #0C2358 68.31%)",
//     // padding:10,
//     // fontWeight:'bold'
//   },
// });

// export default Home;
