const io = require('socket.io-client');

const runIO = async () => {
  console.log('running');
  const socket = io('http://192.168.5.186:8080', {
    transports: ['websocket'],
  });
  socket.on('connect', () => {
    console.log('Connected to server:', socket.id);

    // Send a test message
    // socket.emit('message', 'Hello Server!');
    // socket.emit('none', 'Hello Server!');
  });

  socket.on('data-test', (data) => {
    console.log('data', data);
  });
};
//
runIO();
