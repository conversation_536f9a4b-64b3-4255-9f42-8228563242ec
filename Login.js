import React, { useState } from "react";
import { Button, TouchableOpacity } from "react-native";
import { Dimensions, Image, TextInput, View } from "react-native";
import { SafeAreaView, Text } from "react-native";
import { useIsFocused } from "@react-navigation/native";


export default function Login({ route, navigation }) {
  const [usernameAccount, setUsernameAccount] = useState("");
  const [password, setPassword] = useState("");

  return (
    <SafeAreaView>
      <View
        style={{
          height: 0.4 * Dimensions.get("screen").height,
          alignItems: "center",
          justifyContent: "center",
          marginBottom: 10,
        }}
      >
        <Image
          resizeMethod="resize"
          resizeMode="contain"
          style={{ maxWidth: 0.9 * Dimensions.get("screen").width }}
          source={require("./assets/qv_logo_new.png")}
        />
      </View>
      <View style={{ margin: 30 }}>
        <TextInput
          onChange={(username) => setUsernameAccount(username)}
          value={usernameAccount}
          placeholder="Username"
          style={{
            backgroundColor: "#e8e8e8",
            padding: 10,
            fontSize: 20,
            marginBottom: 20,
          }}
        />
        <TextInput
          onChange={(pass) => setPassword(pass)}
          value={usernameAccount}
          placeholder="Password"
          secureTextEntry={true}
          style={{
            backgroundColor: "#e8e8e8",
            padding: 10,
            fontSize: 20,
            marginBottom: 20,
          }}
        />
        <TouchableOpacity
          style={{
            backgroundColor: "#A8A8A8",
            alignItems: "center",
            padding: 10,
            borderRadius:5,
            shadowRadius: 10,
            shadowColor: "#777",
            shadowOffset: 2,
          }}
          onPress={()=>navigation.navigate("Home")}
        >
          <Text style={{ fontSize: 20, fontWeight: "bold" }}>LOGIN</Text>
        </TouchableOpacity>
      </View>
      <View></View>
    </SafeAreaView>
  );
}
