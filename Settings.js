/* eslint-disable no-underscore-dangle */
import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  initData,
  releaseFacePassHandler,
  restartApplication,
  hideNavigationBar,
  setExposureCompensation,
  checkGroupExist,
  deleteGroup,
  createGroup,
} from "facepass-react-native-module";
import _, { set } from "lodash";
import React, { useEffect, useRef, useState } from "react";
import {
  Dimensions,
  ScrollView,
  Text,
  TextInput,
  ToastAndroid,
  TouchableOpacity,
  View,
} from "react-native";
import { Picker } from "@react-native-picker/picker";
import moment from "moment";
import { getTextFile, updateFile } from "./helpers/handlingTextFile";
import { updateSettings } from "./socket";

function SettingsPage({ route, navigation }) {
  const [updatedSettings, setUpdatedSettings] = useState({});

  const [hideNav, setHideNav] = useState(true);

  const [afterScan, setAfterScan] = useState("stay");

  const [showLunchIn, setShowLunchIn] = useState(true);
  const [showCheckIn, setShowCheckIn] = useState(true);
  const [defaultScan, setDefaultScan] = useState('none');
  const [camExposure, setCamExposure] = useState(1);

  useEffect(() => {
    AsyncStorage.getItem("stayAfterScan").then((res) => {
      if (!_.isNil(res)) {
        setAfterScan(res);
      }
    });
    AsyncStorage.getItem("hideNavbar").then((res) => {
      if (!_.isNil(res)) {
        console.log('VALUE OF NAVBAR: ', JSON.parse(res));
        setHideNav(!JSON.parse(res));
      } else {
        throw "no value of navbar";
      }
    });
    console.log('exposure adjusted', camExposure);
  }, [camExposure]);

  const submit = async () => {
    await AsyncStorage.setItem("stayAfterScan", afterScan);
    await AsyncStorage.setItem("hideNavBar", JSON.stringify(hideNav));
    await AsyncStorage.setItem("showLunchIn", JSON.stringify(showLunchIn));
    await AsyncStorage.setItem("showCheckIn", JSON.stringify(showCheckIn));
    await AsyncStorage.setItem("cameraExposure", JSON.stringify(camExposure));
    await AsyncStorage.setItem('defaultScan', (defaultScan));
    await setExposureCompensation(_.parseInt(camExposure));

    const newUpdatedSettings = {
      ...updatedSettings,
      timestamps: moment().toISOString(),
    };

    await updateSettings(newUpdatedSettings);

    await updateFile('generalSettings', newUpdatedSettings);
    ToastAndroid.show("Saving, please wait it for app to restart", 3);

    await releaseFacePassHandler();
    await initData(updatedSettings.settings).then(() => {
      navigation.navigate("Home");
    });
  };

  // TODO: ADD THESE FEATURES AS PART OF THE SETTINGS CALLED AccessTypes

  const cancelAction = () => {
    navigation.navigate("Scan");
  };

  const forceRemoveAllFaceTokens = async () => {
    const groupName = "facepass";
    try {
      const report = await checkGroupExist(groupName);
      if (report === "LOCALGROUP_FOUND") {
        await deleteGroup(groupName).then(() => console.log("group_reset"));
        await createGroup(groupName)
          .then(() => console.log("group_remade"))
          .finally(() => {
            AsyncStorage.setItem("tokenFaces", JSON.stringify([]));
          });
      }
    } catch (error) { }
  };

  // const getStorage = async () => {
  //   const storagefaces = await AsyncStorage.getItem("tokenFaces");
  //   const storeParse = JSON.parse(storagefaces);
  //   console.log(storeParse);
  //   console.log(typeof storeParse);
  // };

  return (
    <View
      style={{
        minHeight: Dimensions.get("screen").height,
      }}
      contentContainerStyle={{ flexGrow: 1 }}
    >
      <View style={{ padding: 20 }}>
        <Text style={{ fontSize: 32 }}>Settings</Text>
        <Text style={{ color: "#777", marginTop: 10 }}>
          Press to enable or disable features
        </Text>
        <SettingsList
          afterScan={afterScan}
          setAfterScan={setAfterScan}
          hideNav={hideNav}
          setHideNav={setHideNav}
          returnedValue={(value) => {
            setUpdatedSettings(value);
          }}
          showLunchIn={showLunchIn}
          setShowLunchIn={setShowLunchIn}
          showCheckIn={showCheckIn}
          setShowCheckIn={setShowCheckIn}
          setCamExposure={setCamExposure}
          camExposure={camExposure}
          defaultScan={defaultScan}
          setDefaultScan={setDefaultScan}
        />
        <View
          style={{
            display: "flex",
            flexDirection: "row",
            position: "absolute",
            bottom: -50,
            width: Dimensions.get("window").width,
          }}
        >
          {/* <SettingsButton
            text="Reset storage"
            onPress={() => forceRemoveAllFaceTokens()}
          /> */}
          <SettingsButton text="Cancel" onPress={() => cancelAction()} />
          <SettingsButton text="Submit" onPress={() => submit()} />
        </View>
        {/* <SettingsButton text="Reboot" onPress={() => BackHandler.exitApp()} /> */}
      </View>
    </View>
  );
}

function SettingsList({
  returnedValue, afterScan, setAfterScan, hideNav, setHideNav,
  showLunchIn,
  setShowLunchIn,
  showCheckIn,
  setShowCheckIn,
  camExposure,
  setCamExposure,
  setDefaultScan,
  defaultScan,
}) {
  const [updatedSettings, setUpdatedSettings] = useState({});

  const initSetting = async () => {
    const setting = await getTextFile('generalSettings');
    console.log("settings in settings page:", setting);
    if (!_.isEmpty(setting)) {
      _.unset(setting, "_id");
      // need to do this to convert object id to string.
      const formatedSettings = setting;
      console.log(
        "entries settings:",
        JSON.stringify(_.entries(formatedSettings), null, 2),
      );
      setUpdatedSettings(formatedSettings);
    }
  };

  useEffect(() => {
    initSetting();
  }, []);

  useEffect(() => {
    console.log(CONSOLE_.FgCyan, JSON.stringify(updatedSettings, null, 2));
    returnedValue(updatedSettings);
  }, [updatedSettings]);

  const cameraFields = [
    "cameraFacingFront",
    "cameraPreviewRotation",
    "faceRotation",
    "isCross",
    "isSettingAvailable",
  ];

  return (
    <ScrollView
      style={{
        height: Dimensions.get("window").height * 0.75,
      }}
    >
      {
        _.map(_.entries(updatedSettings.settings), (item) =>
          // console.log(CONSOLE_.FgBlue, item[0]);
          renderByType(item, setUpdatedSettings, updatedSettings, cameraFields))
      }

      <Text
        style={{
          marginTop: 20,
          fontSize: 20,
          fontWeight: "bold",
          marginBottom: 10,
        }}
      >
        Camera Settings :
        {" "}
      </Text>

      {_.map(_.entries(updatedSettings.cameraSettings), (item) => renderByType(item, setUpdatedSettings, updatedSettings, cameraFields))}

      <AccessButtonDisplayOption
        afterScan={afterScan}
        setAfterScan={setAfterScan}
        hideNav={hideNav}
        setHideNav={setHideNav}
      />

      <CheckInOutButtons
        showCheckIn={showCheckIn}
        setShowCheckIn={setShowCheckIn}
      />

      <LunchButtons
        showLunchIn={showLunchIn}
        setShowLunchIn={setShowLunchIn}
      />

      <SetExposureBrightness
        camExposure={camExposure}
        setCamExposure={setCamExposure}
      />
      <SetDefaultScan
        defaultScan={defaultScan}
        setDefaultScan={setDefaultScan}
      />
    </ScrollView>
  );
}

function SettingsButton({ text = "placeholder_text", onPress = null }) {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={{
        flex: 1,
        margin: 4,
        padding: 10,
        borderWidth: 1,
        borderRadius: 10,
      }}
    >
      <Text style={{ textAlign: "center", fontSize: 18 }}>
        {' '}
        {text}
        {' '}
      </Text>
    </TouchableOpacity>
  );
}

function AccessButtonDisplayOption({
  afterScan, setAfterScan, hideNav, setHideNav,
}) {
  console.log(`AFTER SCAN VALUE: ${afterScan}`);
  return (
    <View>
      <Text style={{ fontSize: 20, fontWeight: "bold" }}>
        Access Type Options:
      </Text>
      <View style={{
        color: "red",
        display: "flex",
        flexDirection: "row",
        height: 50,
        alignItems: "center",
        marginBottom: 10,
      }}
      >
        <Text style={{ flex: 3, fontSize: 18 }}> Post Scan Action </Text>
        <Picker
          style={{ flex: 2, borderColor: "#f00" }}
          selectedValue={afterScan}
          onValueChange={(itemValue, itemIndex) => {
            console.log(`changed: ${itemValue}`);
            setAfterScan(itemValue);
          }}
        >
          <Picker.Item label="Stay" value="stay" />
          <Picker.Item label="Return Home" value="leave" />
        </Picker>
      </View>

      <View style={{
        color: "red",
        display: "flex",
        flexDirection: "row",
        height: 50,
        alignItems: "center",
        marginBottom: 10,
      }}
      >
        <Text style={{ flex: 3, fontSize: 18 }}>
          Device Navigation Bar
        </Text>

        <Picker
          style={{ flex: 2, borderColor: "#f00" }}
          selectedValue={hideNav}
          onValueChange={async (itemValue, itemIndex) => {
            console.log(itemValue);
            setHideNav(itemValue);
            if (itemValue) {
              await hideNavigationBar(false);
              await AsyncStorage.setItem("hideNavbar", "false").then(() => {
                console.log("HIDE NAV BAR SET FALSE");
              });
            } else {
              await hideNavigationBar(true);
              await AsyncStorage.setItem("hideNavbar", "true").then(() => {
                console.log("HIDE NAV BAR SET TO TRUE");
              });
            }
          }}
        >
          <Picker.Item label="Hide" value={false} />
          <Picker.Item label="Show" value />
        </Picker>
      </View>
    </View>
  );
}

function LunchButtons({ showLunchIn, setShowLunchIn }) {
  // const [showLunchIn, setShowLunchIn] = useState(true);
  useEffect(() => { // attempt to get data from server
    AsyncStorage.getItem('showLunchIn').then((res) => {
      if (!_.isNil(res)) {
        setShowLunchIn(JSON.parse(res));
      } else {
        console.log(CONSOLE_.FgYellow, "showLunchIn not found, creating in Aysncstorage");
        AsyncStorage.setItem('showLunchIn', "true").then(() => console.log(CONSOLE_.FgYellow, "set showLunchIn"));
      }
    });
  }, []);

  return (
    <View style={{
      display: "flex", flexDirection: 'row', alignItems: "center", marginBottom: 10, height: 50,
    }}
    >
      <Text style={{ flex: 3, fontSize: 18 }}>
        Lunch In / Out
      </Text>

      <Picker
        style={{ flex: 2, borderColor: "#f00" }}
        selectedValue={showLunchIn}
        onValueChange={async (itemValue, itemIndex) => {
          setShowLunchIn(itemValue);
          ToastAndroid.show("Please submit to apply changes", 2);
        }}
      >
        <Picker.Item label="Show" value />
        <Picker.Item label="Hide" value={false} />
      </Picker>
    </View>
  );
}

function CheckInOutButtons({
  showCheckIn,
  setShowCheckIn,
}) {
  // const [showCheckIn, setShowCheckIn] = useState(true); // show by default
  useEffect(() => { // attempt to get data from server
    AsyncStorage.getItem('showCheckIn').then((res) => {
      if (!_.isNil(res)) {
        setShowCheckIn(JSON.parse(res));
      } else {
        console.log(CONSOLE_.FgYellow, "showCheckIn not found, creating in Aysncstorage");
        AsyncStorage.setItem('showCheckIn', "true").then(() => console.log(CONSOLE_.FgYellow, "set showCheckIn"));
      }
    });
  }, []);

  return (
    <View style={{
      display: "flex", flexDirection: 'row', alignItems: "center", marginBottom: 10, height: 50,
    }}
    >
      <Text style={{ flex: 3, fontSize: 18 }}>
        Check In / Out
      </Text>

      <Picker
        style={{ flex: 2, borderColor: "#f00" }}
        selectedValue={showCheckIn}
        onValueChange={async (itemValue, itemIndex) => {
          setShowCheckIn(itemValue);
          ToastAndroid.show("Please submit to apply changes", 2);
        }}
      >
        <Picker.Item label="Show" value />
        <Picker.Item label="Hide" value={false} />
      </Picker>
    </View>
  );
}

function SetExposureBrightness({
  setCamExposure,
  camExposure,
}) {
  useEffect(() => { // attempt to get data from server
    AsyncStorage.getItem('cameraExposure').then((res) => {
      if (!_.isNil(res)) {
        console.log('camexposure', res);
        setCamExposure(_.parseInt(JSON.parse(res)));
      } else {
        console.log(CONSOLE_.FgYellow, "setCamExposure not set, creating in Aysncstorage");
        AsyncStorage.setItem('cameraExposure', "1").then(() => console.log(CONSOLE_.FgYellow, "set cameraExposure"));
      }
    });
  }, []);
  return (
    <View style={{
      display: "flex", flexDirection: 'row', alignItems: "center", marginBottom: 10, height: 50,
    }}
    >
      <Text style={{ flex: 3, fontSize: 18 }}>
        Camera Exposure
      </Text>

      <Picker
        style={{ flex: 2, borderColor: "#f00" }}
        selectedValue={_.toString(camExposure)}
        onValueChange={async (itemValue, itemIndex) => {
          setCamExposure(itemValue);
          ToastAndroid.show("Please submit to apply changes", 2);
        }}
      >
        <Picker.Item label="5 - Very Dark" value="5" />
        <Picker.Item label="4 - Dark" value="4" />
        <Picker.Item label="3 - A little Dark" value="3" />
        <Picker.Item label="2 - Normal" value="2" />
        <Picker.Item label="1 - Bright" value="1" />
      </Picker>
    </View>
  );
}

function SetDefaultScan({
  setDefaultScan,
  defaultScan,
}) {
  useEffect(() => { // attempt to get data from server
    AsyncStorage.getItem('defaultScan').then((res) => {
      if (!_.isNil(res)) {
        console.log('defaultScan', res);
        setDefaultScan(res);
      } else {
        console.log(CONSOLE_.FgYellow, "setDefaultScan not set, creating in Aysncstorage");
        AsyncStorage.setItem('defaultScan', "none").then(() => console.log(CONSOLE_.FgYellow, "set setDefaultScan"));
      }
    });
  }, []);
  return (
    <View style={{
      display: "flex", flexDirection: 'row', alignItems: "center", marginBottom: 10, height: 50,
    }}
    >
      <Text style={{ flex: 3, fontSize: 18 }}>
        Default Scan Mode
      </Text>

      <Picker
        style={{ flex: 2, borderColor: "#f00" }}
        selectedValue={_.toString(defaultScan)}
        onValueChange={async (itemValue, itemIndex) => {
          setDefaultScan(itemValue);
          ToastAndroid.show("Please submit to apply changes", 2);
        }}
      >

        <Picker.Item label="Check Out" value="checkOut" />
        <Picker.Item label="Check in" value="checkIn" />
        <Picker.Item label="None " value="none" />
      </Picker>
    </View>
  );
}

const parseVariableNames = (settingName) => {
  const cloneName = _.startCase(_.cloneDeep(settingName));
  return cloneName;
};

const renderByType = (
  item,
  setUpdatedSettings,
  updatedSettings,
  cameraFields,
) => (
  <View
    key={item[0]}
    style={{
      color: "red",
      display: "flex",
      flexDirection: "row",
      // borderWidth: 0.4,
      height: 50,
      alignItems: "center",
      marginBottom: 10,
    }}
  >
    <Text style={{ flex: 3, fontSize: 18 }}>{parseVariableNames(item[0])}</Text>
    {typeof item[1] === "boolean" ? (
      <Picker
        style={{ flex: 2, borderColor: "#f00", borderWidth: 1 }}
        selectedValue={item[1]}
        onValueChange={(itemValue, itemIndex) => {
          let tempObj = {};
          // TODO: adding condition to check
          if (_.includes(cameraFields, item[0])) {
            tempObj = {
              ...updatedSettings.cameraSettings,
              [item[0]]: itemValue,
            };
            setUpdatedSettings({
              ...updatedSettings,
              cameraSettings: tempObj,
            });
          } else {
            tempObj = {
              ...updatedSettings.settings,
              [item[0]]: itemValue,
            };
            setUpdatedSettings({
              ...updatedSettings,
              settings: tempObj,
            });
          }
        }}
      >
        <Picker.Item label="True" value />
        <Picker.Item label="False" value={false} />
      </Picker>
    ) : (
      <TextInput
        style={{
          borderWidth: 0.5,
          flex: 2,
          paddingLeft: 10,
          fontSize: 18,
          borderColor: "grey",
        }}
        keyboardType="number-pad"
        onChangeText={(text) => {
          if (!_.isEmpty(text)) {
            // TODO: ✅Add another condition to check which settings
            let tempObj = {};
            if (_.includes(cameraFields, item[0])) {
              tempObj = {
                ...updatedSettings.cameraSettings,
                [item[0]]: parseInt(text, 10),
              };
              console.log("temp obj - num:", tempObj);
              setUpdatedSettings({
                ...updatedSettings,
                cameraSettings: tempObj,
              });
            } else {
              tempObj = {
                ...updatedSettings.settings,
                [item[0]]: parseInt(text, 10),
              };
              console.log("temp obj - num:", tempObj);
              setUpdatedSettings({
                ...updatedSettings,
                settings: tempObj,
              });
            }
          } else {
            // TODO:✅ Add another condition to check which settings
            let tempObj = {};
            if (_.includes(cameraFields, item[0])) {
              tempObj = {
                ...updatedSettings.cameraSettings,
                [item[0]]: 0,
              };
              setUpdatedSettings({
                ...updatedSettings,
                cameraSettings: tempObj,
              });
            } else {
              tempObj = {
                ...updatedSettings.settings,
                [item[0]]: 0,
              };
              setUpdatedSettings({
                ...updatedSettings,
                settings: tempObj,
              });
            }
          }
        }}
      >
        {item[1].toString()}
      </TextInput>
    )}
  </View>
);

export default SettingsPage;

const CONSOLE_ = {
  Reset: "\x1b[0m",
  Bright: "\x1b[1m",
  Dim: "\x1b[2m",
  Underscore: "\x1b[4m",
  Blink: "\x1b[5m",
  Reverse: "\x1b[7m",
  Hidden: "\x1b[8m",

  FgBlack: "\x1b[30m",
  FgRed: "\x1b[31m",
  FgGreen: "\x1b[32m",
  FgYellow: "\x1b[33m",
  FgBlue: "\x1b[34m",
  FgMagenta: "\x1b[35m",
  FgCyan: "\x1b[36m",
  FgWhite: "\x1b[37m",
  FgGray: "\x1b[90m",
};
