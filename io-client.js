const { io } = require('socket.io-client');

const socket = io('http://192.168.0.124:8080/', {
  transports: ['websocket'],
  auth: {
    apiKey: '3F418FE611CC4FC5B51747429BE3E662',
  },
  extraHeaders: {
    serialNumber: 'QV3001',
  },
});

// console.log(socket);

socket.on('connect', () => {
  console.log('connected');
});

socket.on('disconnect', () => {
  console.log('disconnected');
});

socket.on('data', (d) => {
  console.log('data:', d);
});

socket.on('data-test', (d) => {
  console.log('data test:', d);
});

socket.emit('checkIn', (d) => {
  console.log('data test:', d);
});

socket.emit('syncUser', (d) => {
  console.log('data test:', d);
});
