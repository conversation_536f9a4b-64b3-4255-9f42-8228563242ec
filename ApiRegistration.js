import AsyncStorage from "@react-native-async-storage/async-storage";
import _ from "lodash";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Button,
  Dimensions,
  StyleSheet,
  Text,
  View,
} from "react-native";
import {
  CodeField,
  Cursor,
  useBlurOnFulfill,
  useClearByFocusCell,
} from "react-native-confirmation-code-field";
import { getDeviceSerialNumber } from "facepass-react-native-module";
import { initSocket, socket } from "./socket";
import api from "./helpers/api";
import { updateFile } from "./helpers/handlingTextFile";

const styles = StyleSheet.create({
  root: { flex: 1, padding: 20 },
  title: { textAlign: "center", fontSize: 30 },
  codeFieldRoot: { marginTop: 20 },
  cell: {
    width: 40,
    height: 40,
    lineHeight: 38,
    fontSize: 24,
    borderWidth: 2,
    borderColor: "#00000030",
    textAlign: "center",
    margin: 2,
  },
  focusCell: {
    borderColor: "#000",
  },
  registerbutton: {
    padding: 20,
    borderWidth: 1,
    width: "100%",
    marginTop: 30,
  },
});

const checkAPIIsValid = async (navigation, setIsInvalid, apiString, userRealm, users, settings) => {
  await api("premise/api")
    .then(async (premiseData) => {
      await AsyncStorage.setItem("premiseId", JSON.stringify(premiseData.data.premiseId));
      await AsyncStorage.setItem("premiseData", JSON.stringify(premiseData.data));
      console.log('premise data from check api:', premiseData);
      await updateFile('premiseData', premiseData?.data);
      if (!_.isEmpty(premiseData)) {
        // reinitialize socket
        await initSocket(apiString, userRealm, users, settings);

        console.log('premise data:', premiseData);
        await navigation.replace("Scan", {
          type: "none",
        });
      }
    }).catch(async (err) => {
      setIsInvalid(true);
      await AsyncStorage.removeItem("apiKey");
      // console.log(CONSOLE_.FgRed, err);
      throw err;
    });
};

const initDepartmentData = async (localApiKey) => {
  const stringifySerialNumber = await getDeviceSerialNumber();
  const parsedSerialNumber = await JSON.parse(stringifySerialNumber);
  await api(`department?apiKey=${localApiKey}&serialNumber=${parsedSerialNumber['persist.ro.serialno']}`).then(async (res) => {
    await AsyncStorage.setItem("departmentData", JSON.stringify(res.data));
  }).catch((err) => { console.log('init department data error:', err); });
};

function ApiRegistration({ route, navigation }) {
  const [isInvalid, setIsInvalid] = useState(false);
  const [apiString, setApiString] = useState("");
  // const { finalString, setFinalString } = React.useContext(ApiContext);
  const ref = useBlurOnFulfill({ value: apiString, cellCount: 8 });
  const [propss, getCellOnLayoutHandler] = useClearByFocusCell({
    value: apiString,
    setValue: setApiString,
  });
  const [loading, setloading] = React.useState(false);
  const [fetching, setisFetching] = React.useState(true);
  const userRealm = [];
  const users = [];
  const settings = [];

  const init = async () => {
    const localApiKey = await AsyncStorage.getItem('apiKey');
    console.log('local api key:', localApiKey);
    if (_.isEmpty(localApiKey)) {
      setisFetching(false);
      console.log("NO API KEY REGISTERED");
      ref.current.focus();
    } else {
      console.log("KEY REGISTERED");
      console.log(`CHECKING SOCKET: ${typeof socket}`);
      await initDepartmentData(localApiKey);
      if (_.isNil(socket)) {
        console.log(`➤➤socket is nil, initializing in api page`, localApiKey);
        await checkAPIIsValid(navigation, setIsInvalid, localApiKey, userRealm, users, settings);
      }

      console.log('going to main page');
      navigation.replace("Scan", {
        type: "none",
      });
    }
  };

  useEffect(() => {
    init();
  }, []);

  useEffect(() => {
    console.log(` 80 : socket status ${typeof socket}`);
  }, [socket]);

  const saveApiKey = async () => {
    // TODO: replace hard coded api with input

    console.log('save api key:', apiString);
    // setKeyExist(true);
    // setSuccess(true);
    await AsyncStorage.setItem("apiKey", apiString);
    await initDepartmentData(apiString);
    await checkAPIIsValid(navigation, setIsInvalid, apiString);
  };

  // React.useEffect(() => {
  //   if (socket) {
  //     console.log("socket status:", socket.connected);
  //     if (
  //       settings[0]?.settings !== null
  //       || settings[0]?.settings !== undefined
  //     ) {
  //       socket.emit("updateSettings", settings[0]);
  //     }
  //   }
  // }, [socket]);

  if (fetching) {
    return (
      <View
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: Dimensions.get("screen").height,
        }}
      >
        <View style={{ width: "100%", padding: "10%" }}>
          <Text
            style={{
              textAlign: "center",
              marginBottom: 20,
              fontSize: 20,
            }}
          >
            App is Loading. Please wait
          </Text>
          <Text style={{ textAlign: "center" }}>
            Checking for API key. Please wait a moment...
          </Text>
        </View>
        <View style={{ width: "100%", height: 300 }}>
          <ActivityIndicator size="large" color="#00f" />
        </View>
      </View>
    );
  }

  return (
    <View
      style={{
        width: Dimensions.get("screen").width,
        height: Dimensions.get("screen").height,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#FFF",
      }}
    >
      <Text style={{ fontSize: 22 }}>Please enter provided API key</Text>
      <View style={{ display: "flex", flexDirection: "row" }}>
        <CodeField
          ref={ref}
          {...propss}
          value={apiString}
          onChangeText={setApiString}
          cellCount={8}
          rootStyle={styles.codeFieldRoot}
          textContentType="oneTimeCode"
          renderCell={({ index, symbol, isFocused }) => (
            <View
              key={index}
              style={{ display: "flex", flexDirection: "row" }}
            >
              <Text
                key={index}
                style={[styles.cell, isFocused && styles.focusCell]}
                onLayout={getCellOnLayoutHandler(index)}
              >
                {symbol || (isFocused ? <Cursor /> : null)}
              </Text>
              {index === 3 ? (
                <View style={{ padding: 10 }}>
                  <Text> - </Text>
                </View>
              ) : null}
            </View>
          )}
        />
      </View>
      <View style={{ display: "flex", flexDirection: "column", padding: 20 }}>
        <Button
          title="Register"
          disabled={apiString.length < 8}
          onPress={() => saveApiKey()}
        />
      </View>
      <ActivityIndicator
        size="large"
        style={{
          display: loading ? "flex" : "none",
        }}
      />
      <Text style={{
        color: "red",
        display: isInvalid ? "flex" : 'none',
      }}
      >
        Unrecognized key
      </Text>
    </View>
  );
}

export default ApiRegistration;
