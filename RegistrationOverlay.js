import React, { useEffect, useRef } from "react";
import {
  Button,
  Dimensions,
  Pressable,
  StyleSheet,
  Text,
  TextInput,
  View,
} from "react-native";
import {
  CodeField,
  Cursor,
  useBlurOnFulfill,
  useClearByFocusCell,
} from "react-native-confirmation-code-field";
// import { TouchableOpacity } from "react-native-gesture-handler";

const styles = StyleSheet.create({
  root: { flex: 1, padding: 20 },
  title: { textAlign: "center", fontSize: 30 },
  codeFieldRoot: { marginTop: 20 },
  cell: {
    width: 40,
    height: 40,
    lineHeight: 38,
    fontSize: 24,
    borderWidth: 2,
    borderColor: "#00000030",
    textAlign: "center",
    margin: 2,
  },
  focusCell: {
    borderColor: "#000",
  },
  registerbutton: {
    padding: 20,
    borderWidth: 1,
    width: "100%",
    marginTop: 30,
    // flex: 1,
  },
});

function RegistrationOverlay(props) {
  const { apiString, setApiString } = props;
  //   const inputRef = useRef(null);
  const ref = useBlurOnFulfill({ value: apiString, cellCount: 8 });
  const [propss, getCellOnLayoutHandler] = useClearByFocusCell({
    value: apiString,
    setValue: setApiString,
  });

  useEffect(() => {
    // inputRef.current.focus();
    ref.current.focus();
  }, []);

  try {
    return (
      <View>
        <View
          style={{
            width: Dimensions.get("screen").width,
            height: Dimensions.get("screen").height,
            display: "flex",
            // position: "absolute",
            // backgroundColor: "#ff0",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Text style={{ fontSize: 22 }}>Please enter provided API key</Text>
          <View style={{ display: "flex", flexDirection: "row" }}>
            <CodeField
              ref={ref}
              {...propss}
              value={apiString}
              onChangeText={setApiString}
              cellCount={8}
              rootStyle={styles.codeFieldRoot}
              textContentType="oneTimeCode"
              renderCell={({ index, symbol, isFocused }) => {
                return (
                  <View
                    key={index}
                    style={{ display: "flex", flexDirection: "row" }}
                  >
                    <Text
                      key={index}
                      style={[styles.cell, isFocused && styles.focusCell]}
                      onLayout={getCellOnLayoutHandler(index)}
                    >
                      {symbol || (isFocused ? <Cursor /> : null)}
                    </Text>
                    {index === 3 ? (
                      <View style={{ padding: 10 }}>
                        <Text> - </Text>
                      </View>
                    ) : null}
                  </View>
                );
              }}
            />
            {/* <TextInput
              ref={inputRef}
              onChangeText={setApiString}
              value={apiString}
              style={{
                borderWidth: 1,
                padding: 10,
                width: 50,
                fontSize: 24,
              }}
            />
            <Text style={{ padding: 5, fontSize: 20 }}> - </Text>
            <TextInput
              onChangeText={setApiString}
              value={apiString}
              style={{
                borderWidth: 1,
                padding: 10,
                width: 50,
                fontSize: 24,
              }}
            /> */}
          </View>
          <View
            style={{ display: "flex", flexDirection: "column", padding: 20 }}
          >
            <Button title="Register" disabled={apiString.length < 8} />
          </View>
        </View>
      </View>
    );
  } catch (error) {}
}

export default RegistrationOverlay;
