const { exec } = require('child_process');
const fetch = require('node-fetch');

const runSetup = async () => {
  const latestSerialNumber = await fetch('https://facial-recognition-3bf89.as.r.appspot.com/api/serial-number-by-external', {
    method: 'GET',
    headers: {
      apikey: 'Mm58TZibeie38aodWA7dJJx2nPUUPtRuXMeQH8VV',
    },
  }).then((res) => res.json()).then((res) => res?.data?.serialNumber);

  console.log('latest serial number:', latestSerialNumber);
  // put the serial number gotten into the devices.
  exec(`adb shell "setprop persist.ro.serialno QV${latestSerialNumber}"`, async (error, stdout, stderr) => {
    if (error) {
      console.log(`error: ${error.message}`);
      return;
    }
    if (stderr) {
      console.log(`stderr: ${stderr}`);
      return;
    }
    console.log(`setup device serial number ${latestSerialNumber} successfully!`);
    process.exit(0);
  });
};

runSetup();
