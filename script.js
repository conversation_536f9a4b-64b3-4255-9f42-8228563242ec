const AccessModel = require('#models/Access.model');
const DeviceSettingsRecord = require('#models/DeviceSettingsRecord');
const DeviceUsersModel = require('#models/DeviceUsers.model');
const AccessRecordModel = require('#models/AccessRecord.model');
const UserModel = require('#models/User.model');
const moment = require('moment');
const _ = require('lodash');
const ExcelJS = require('exceljs');
require('#config/mongodb');

const { getUsersByPremiseId, getUsersByPremiseIdAndDepartmentId } = require('#services/premise.service');
const {
  writeFile, createWriteStream, readFile,
} = require('fs');
const { default: mongoose } = require('mongoose');

require('#config/mongodb');

const runScript = async () => {
  await DeviceSettingsRecord.deleteMany({
    serialNumber: 'QV3022',
  });
};

const runDeleteDeviceUsers = async () => {
  const deleted = await DeviceUsersModel.deleteMany({
    premiseId: '654c931699313cac33e46652',
  });

  console.log('deleted object:', deleted);
};

const runSyncUsers = async () => {
  const synced = await getUsersByPremiseIdAndDepartmentId('654c931699313cac33e46652', 'QV3015');
  console.log('synced:', synced);
  console.log('total users:', synced.length);
};

const runBackupAccess = async () => {
  const accesses = await AccessModel.find({
    premiseId: '654c931699313cac33e46652',
  });

  console.log('accesses:', accesses);

  await writeFile('backup.json', JSON.stringify(accesses), () => { });
};

const deleteAccesses = async () => {
  const deleted = await AccessModel.deleteMany({
    premiseId: '654c931699313cac33e46652',
  });

  console.log('deleted:', deleted);
};

const addBackAccesses = async () => {
  const accesses = readFile('backup.json', async (err, data) => {
    console.log('accesses:', JSON.parse(Buffer.from(data).toString()));
    const addedAccesses = await AccessModel.create(JSON.parse(Buffer.from(data).toString()));

    console.log('added accesses:', addedAccesses);
  });
};

const clearFaceCache = async (devicePremiseId, serialNo, facePassServer) => {
  console.log(devicePremiseId);
  console.log(serialNo);
  await facePassServer.emitToDevice(devicePremiseId, serialNo, 'clear-face-cache', {});
};

const emitToDeviceToOpenDoor = async (devicePremiseId, serialNo, facePassServer) => {
  await facePassServer.emitToDevice(devicePremiseId, serialNo, 'doorOpen', {});
};

const getAccessRecords = async () => {
  const startDate = moment('1/6/2024', 'DD-MM-YYYY').toISOString();
  const endDate = moment('16/1/2025', 'DD-MM-YYYY').toISOString();
  const accessRecords = await AccessRecordModel.find({
    premiseId: '64e6c0100703004f6d5430af',
    // user: '6600ce80ac8d09dd5098f42e',
    createdAt: {
      $gte: startDate,
      $lte: endDate,
    },
  }).populate('user').lean();

  console.log('access records:', accessRecords);

  const groupedByDate = _.groupBy(_.map(accessRecords, (ar) => ({
    ...ar,
    created: moment(ar.createdAt).format('DD-MM-YYYY'),
    createdAt: ar.createdAt,
  })), 'created');

  const workbook = new ExcelJS.Workbook();
  const sheet = workbook.addWorksheet('access records');
  sheet.columns = [
    {
      header: 'Date',
      key: 'date',
      width: 20,
    }, {
      header: 'Name',
      key: 'name',
      width: 20,
    }, {
      header: 'First Check In',
      key: 'firstCheckIn',
      width: 20,
    }, {
      header: 'Last Check Out',
      key: 'lastCheckOut',
      width: 20,
    },
  ];

  const finalData = [];
  // grouped data
  _.map(groupedByDate, (dataList, dateIndex) => {
    // grouped user
    const groupedUser = _.groupBy(dataList, 'user._id');

    _.map(groupedUser, (sameUserList, userId) => {
      // console.log('sameUser', sameUserList);
      console.log('sameUser date', dateIndex);
      console.log('sameUser earliest time', _.map(sameUserList, (user) => moment(user?.createdAt)));
      finalData.push({
        date: dateIndex !== 'undefined' ? moment(dateIndex, 'DD-MM-YYYY').format('DD-MM-YYYY') : null,
        name: sameUserList[0].user?.fullName,
        firstCheckIn: moment(moment.min(_.map(sameUserList, (user) => moment(user?.createdAt)))).format('HH:mm:a'),
        lastCheckOut: moment(moment.max(_.map(sameUserList, (user) => moment(user?.createdAt)))).format('HH:mm:a'),
      });
    });
  });
  console.log('final data:', finalData);
  // sheet.addRows(_.uniqBy(finalData, 'name'));
  sheet.addRows(finalData);

  await workbook.xlsx.writeFile('access-records.xlsx');
};

const stressTest = async () => {
  _.times(500, async () => {
    console.log('running....');
    await AccessModel.create({
      uid: '64b0f4ff68934268a0cd72a0',
      premiseId: '64e6c0100703004f6d5430af',
      departments: [{
        departmentId: 'main',
        access: true,
        type: 'toggle',
        _id: '64e6c0100703004f6d5430b0',
      }],
      startWorkingTime: moment().toISOString(),
      endWorkingTime: moment().toISOString(),
      remark: 'stress-test',
      isDeleted: false,
    });
  });
};

const deleteStressTest = async () => {
  await AccessModel.deleteMany({
    uid: '64b0f4ff68934268a0cd72a0',
    premiseId: '64e6c0100703004f6d5430af',
    remark: 'stress-test',
  });
};

const formatDorm360AccessRecords = async () => {
  const serialNo = ['QV3011', 'QV3014', 'QV3015', 'QV3016', 'QV3018', 'QV3024'];

  const updatedAccessRecords = await AccessRecordModel.updateMany({
    serialNumber: {
      $in: serialNo,
    },
  }, {
    premiseId: mongoose.Types.ObjectId('654c931699313cac33e46652'),
  });

  console.log('updated Access Records:', updatedAccessRecords);
};

const multipleFaces = async () => {
  const accesses = await AccessModel.find({
  }).limit(500).sort({ createdAt: -1 }).lean();

  const createdNewAccesses = await Promise.all(_.map(accesses, async (access) => {
    if (_.has(access, 'uid') && _.has(access, 'startWorkingTime') && _.has(access, 'endWorkingTime')) {
      console.log('access:', access);
      _.unset(access, '_id');
      return AccessModel.create({
        ...access,
        premiseId: '64e6c0100703004f6d5430af',
        departments: [{
          departmentId: 'main',
          access: true,
          type: 'toggle',
          _id: '64e6c0100703004f6d5430b0',
        }],
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
      });
    }
    return null;
  }));
  console.log('createdNewAccesses:', createdNewAccesses);
};

const deleteMultipleFaces = async () => {
  const today = moment().startOf('day');
  const tomorrow = moment().endOf('day');

  const result = await AccessModel.deleteMany({
    premiseId: '64e6c0100703004f6d5430af',
    createdAt: {
      $gte: today.toISOString(),
      $lte: tomorrow.toISOString(),
    },
  });

  console.log(`Deleted ${result.deletedCount} access records created today.`);
};

// runScript();
// runDeleteDeviceUsers();
// runSyncUsers();
// runBackupAccess();
// deleteAccesses();
// addBackAccesses();
// getAccessRecords();

// stressTest();
// deleteStressTest();

// formatDorm360AccessRecords();

// multiple faces sync
// multipleFaces();

// delete multiple faces
// deleteMultipleFaces();

exports.clearFaceCache = clearFaceCache;
exports.emitToDeviceToOpenDoor = emitToDeviceToOpenDoor;
