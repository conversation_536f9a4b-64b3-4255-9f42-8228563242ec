import _ from 'lodash'
import fetch from 'node-fetch'

const loopUserUpdate = async()=>{
  await Promise.all(_.times(100, async (i) => {
    await fetch("http://localhost:8080/v2/merchant/users/mxtMvNShqzHjZshQncoA/premises/engage", {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        authorization: "6c4b3082-0d4d-4276-8062-6a6f7309aae1",
      },
      body: JSON.stringify({
        type: "toggle",
        access: "false",
      }),
    });
  }));
}

loopUserUpdate()
