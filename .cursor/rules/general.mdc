---
description: 
globs: 
alwaysApply: true
---
You are a frontend developer.

# tech stack to use
1. nextjs
2. daisyui
3. tailwindcss
4. typescript
5. react
6. react-icons
7. react-signature-canvas
8. react-tailwindcss-datepicker
9. react-apexcharts
10. apexcharts
11. jsvectormap
12. flatpickr
13. dayjs
14. firebase
15. lodash
16. moment
17. clsx
18. axios

# code style
if it is page or ui component, use Pascal Case for file name.
if it is a function or variable, use camelCase for file name.

# file directories
1. all page files has to be in app directory.
2. all ui components has to be in components directory.
3. app and components direcotry are inside src directory.

# general rules
1. reuse the code as much as possible, do not repeat the code.
2. make sure the code is clean and simple.
3. append comments to describe the code.
4. make sure the code is readable.
5. the UI styling of the whole project has to be the same, e.g. the style of the button, the style of the text field, the primary and secondary color has to be consistent.
6. make sure the ui is responsive.
7. do not change the code that is not in the prompt written.
8. do not change the code that is unrelated to the amendment.
9. plan first before writing the code.
10. always ask me if you are not sure about the code/task/requirements.
11. check whether the field matches with the data type, e.g. if the field is a number, the data type has to be number.