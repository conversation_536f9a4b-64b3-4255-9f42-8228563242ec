---
description: 
globs: 
alwaysApply: true
---
You are a backend developer.

# tech stack to use
1. express js
2. multer
3. firebase
5. google cloud
6. mongo/mongoose
7. lodash
8. moment
9. cors
10. helmet
11. joi
12. mongoose unique validator

# code style
if it is model, use Pascal Case for file name.
if it is a function or variable, use camelCase for file name.
do not use typescript

# file directories
1. there are six types of directories which are models, dtos, services, controllers, helpers and configs.
2. all routes with url, middleware and api calling method will be written in route.js

# file names sample
user.controller.js
user.service.js
user.dto.js
User.model.js


# general rules
1. reuse the code as much as possible, do not repeat the code.
2. make sure the code is clean and simple.
3. append comments to describe the code.
4. make sure the code is readable.
5. schema is under models.
6. joi validation schema is under dtos.
7. function that is related to business logic is under services.
8. inside controllers, there will be a try catch to catch the error and pass to the next middleware. controller will pass the parameters received to joi validation for checking, then pass to services to run business logic.
9. general functions that can be reused many time, e.g. api call, will be written under helpers.
10. configs will store all the configuration to third party. e.g. initialization of the firebase, cloud storage.
11. do not change the code that is not in the prompt written.
12. do not change the code that is unrelated to the amendment.
13. plan first before writing the code.
14. always ask me if you are not sure about the code/task/requirements.
15. do not use class.
16. do not use try catch in service.
17. use arrow functions.
18. always use timestamps for mongoose model.


# sample for controller
exports.createUser = async (req,res,next)=>{
  try{
    const params = await asyncJoiValidate(req.body, UserDTO.createUser);
    const response = await UserService.createUser(params);
    return res.success(response);
  }catch(err){
    next(err)
  }
}

# sample for service
exports.createUser = async (params)=>{
  const {} = params;

  const createdUser = await UserModel.createUser(params);
  return createdUser;
}

# sample for dto
exports.createUser = Joi.object({
  name:Joi.string().required()
})

1. must use the format provided in the sample.
